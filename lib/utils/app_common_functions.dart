import 'dart:async';
import 'dart:io';

import 'package:easy_refresh/easy_refresh.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_kronos/flutter_kronos.dart';
import 'package:flutter_udid/flutter_udid.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:geocoding/geocoding.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/shared/repositories/account_repository.dart';
import 'package:transport_match/shared/rest_api/api_keys.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/utils/logger.dart';
import 'package:url_launcher/url_launcher.dart';

/// common application functions
class AppCommonFunctions {
  /// Fetches address from lat-long values using google api
  static Future<String?> getAddressDetails(LatLng latLng) async {
    var address = '';
    try {
      final placeMarks = await placemarkFromCoordinates(
        latLng.latitude,
        latLng.longitude,
      );
      final place = placeMarks[0];
      address =
          '${place.name}, ${place.locality}, ${place.administrativeArea} ${place.postalCode}, ${place.country}';
    } catch (e) {
      e.logD;
    }
    address.logE;
    return address;
  }

  static String cleanUpAddress(String input) {
    // Step 1: Remove spaces around commas and collapse multiple commas
    var result = input.replaceAll(RegExp(r'\s*,\s*'), ',');
    result = result.replaceAll(RegExp(',{2,}'), ',');

    // Step 2: Remove comma if directly followed by a dot
    result = result.replaceAll(RegExp(r',\.'), '.');

    // Step 3: Add space after commas if needed
    result = result.replaceAllMapped(
      RegExp(r',(?!\s|$|\.|,)'),
      (match) => ', ',
    );

    // Step 4: Remove all periods except the last one (if any)
    // First remove all dots
    result = result.replaceAll('.', '');

    // Step 5: Add final period if not already present
    result = result.trim();
    if (!result.endsWith('.')) {
      result += '.';
    }

    return result;
  }

  static String addComma(String? value) =>
      value.isNotEmptyAndNotNull ? ' ,$value' : '';

  /// Fetches latitude and longitude from address using google api
  static Future<LatLng?> getLatLongFromAddress({
    required String address,
  }) async {
    var data = <Location>[];
    try {
      data = await locationFromAddress(address);
    } catch (e) {
      e.logD;
    }
    return LatLng(data.elementAt(0).latitude, data.elementAt(0).longitude);
  }

  static Future<bool> checkInternet() async {
    try {
      final result = await InternetAddress.lookup('www.google.com');
      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        return true;
      } else {
        return false;
      }
    } catch (_) {
      return false;
    }
  }

  static void closeKeyboard() {
    FocusManager.instance.primaryFocus?.unfocus();
  }

  static String timeAgoConverter({required String dateString}) {
    final parsedTime = DateTime.parse(dateString).toLocal();

    final now = DateTime.now();

    final difference = now.difference(parsedTime);

    String timeAgo;
    final l10n = rootNavKey.currentContext!.l10n;

    if (difference.inSeconds < 60) {
      timeAgo = l10n.just_now;
    } else if (difference.inMinutes < 60) {
      timeAgo = '${difference.inMinutes} ${l10n.min_ago}';
    } else if (difference.inHours < 24) {
      timeAgo = '${difference.inHours} ${l10n.hour_ago}';
    } else if (difference.inDays < 7) {
      timeAgo = '${difference.inDays} ${l10n.day_ago}';
    } else if (difference.inDays < 365) {
      timeAgo = '${(difference.inDays / 7).floor()} ${l10n.week_ago}';
    } else {
      timeAgo = '${(difference.inDays / 365).floor()} ${l10n.year_ago}';
    }

    return timeAgo;
  }

  static String formatTime({required DateTime dateTime}) {
    return DateFormat.jm().format(dateTime);
  }

  static bool isSameDay({required DateTime date1, required DateTime date2}) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  static String getOrdinalSuffix(int day) {
    if (day >= 11 && day <= 13) {
      return 'th';
    }
    switch (day % 10) {
      case 1:
        return 'st';
      case 2:
        return 'nd';
      case 3:
        return 'rd';
      default:
        return 'th';
    }
  }

  static Future<String> getDeviceId() async {
    var uuid = '';
    try {
      uuid = await FlutterUdid.consistentUdid;
    } on PlatformException catch (e) {
      if (kDebugMode) {
        print('error from uid : ${e.message}');
      }
    }
    return uuid;
  }

  static Future<String?> getFcmToken() {
    return FirebaseMessaging.instance.getToken();
  }

  static Future<void> showCommonToast({
    required String msg,
    Toast? toastLength,
    Color? backgroundColor,
    Color? textColor,
  }) async {
    await Fluttertoast.cancel().whenComplete(() {
      Fluttertoast.showToast(
        msg: msg,
        textColor: textColor,
        toastLength: toastLength ?? Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: backgroundColor ?? AppColors.red,
      );
    });
  }

  static Future<DateTime> getCurrentTime() async {
    FlutterKronos.sync();
    int? currentTimeMs;
    try {
      currentTimeMs = await FlutterKronos.getCurrentTimeMs;
      'Current time : $currentTimeMs'.logD;
    } catch (e) {
      'Exception :: $e'.logE;
    }
    final localTime = currentTimeMs != null
        ? DateTime.fromMillisecondsSinceEpoch(currentTimeMs, isUtc: true)
        : DateTime.now();

    'Local time : $localTime'.logD;

    return localTime;
  }

  static DateTime convertLocalToUtc(DateTime localTime) {
    return localTime.toUtc();
  }

  static DateTime convertUtcToLocal(DateTime utcTime) {
    return utcTime.toLocal();
  }

  // static void showSnackBar({
  //   required BuildContext context,
  //   required String message,
  //   Color? backgroundColor,
  // }) {
  //   ScaffoldMessenger.of(context)
  //     ..clearSnackBars()
  //     ..showSnackBar(
  //       SnackBar(
  //         content: Text(message),
  //         backgroundColor: backgroundColor ?? AppColors.primaryColor,
  //       ),
  //     );
  // }

  static Future<void> openMap(double latitude, double longitude) async {
    try {
      final googleUrl =
          'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude';
      if (await canLaunchUrl(Uri.parse(googleUrl))) {
        await launchUrl(Uri.parse(googleUrl));
      } else {
        'Could not open the map.'.showErrorAlert();
      }
    } catch (e) {
      'Could not open the map due to : $e'.logE;
    }
  }

  static Future<List<XFile>?> showImagePickerPopup({
    required BuildContext context,
    bool isSingleImage = false,
    bool isMultiMedia = false,
  }) async {
    final picker = ImagePicker();
    return showModalBottomSheet(
      backgroundColor: AppColors.transparent,
      context: context,
      builder: (BuildContext dialogContext) => Container(
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(AppSize.r20),
          ),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(
            vertical: AppSize.h30,
            horizontal: AppSize.w14,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    context.l10n.chooseAnAction,
                    style: context.textTheme.titleMedium?.copyWith(
                      fontSize: AppSize.sp18,
                      fontWeight: FontWeight.w600,
                      color: AppColors.ff343A40,
                    ),
                  ),
                  GestureDetector(
                    onTap: () => Navigator.pop(dialogContext),
                    child: CircleAvatar(
                      radius: AppSize.w12,
                      backgroundColor: AppColors.black.withValues(alpha: 0.1),
                      child: Icon(
                        Icons.close,
                        size: AppSize.sp20,
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
              Padding(
                padding: EdgeInsets.symmetric(vertical: AppSize.h20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        GestureDetector(
                          onTap: () async {
                            const isPermissionAccess = true;
                            if (isPermissionAccess) {
                              final pickedCameraFile = await picker.pickImage(
                                source: ImageSource.camera,
                                imageQuality: 100,
                                maxHeight: 600,
                                maxWidth: 600,
                              );
                              List<XFile>? pickedFile;
                              if (pickedCameraFile != null) {
                                pickedFile = <XFile>[pickedCameraFile];
                              }

                              if (dialogContext.mounted) {
                                Navigator.pop(dialogContext, pickedFile);
                              }
                            }
                          },
                          child: Container(
                            height: AppSize.h70,
                            width: context.width * 0.4,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: AppColors.white,
                              borderRadius: BorderRadius.circular(AppSize.r6),
                              boxShadow: [
                                BoxShadow(
                                  spreadRadius: 2,
                                  blurRadius: 1,
                                  color: AppColors.black.withValues(
                                    alpha: 0.06,
                                  ),
                                  offset: Offset(0, AppSize.h2),
                                ),
                              ],
                            ),
                            child: Icon(
                              Icons.camera_alt_outlined,
                              size: AppSize.sp42,
                              color: AppColors.primaryColor,
                            ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(top: AppSize.h10),
                          child: Text(
                            context.l10n.camera,
                            style: context.textTheme.titleMedium?.copyWith(
                              fontSize: AppSize.sp14,
                              fontWeight: FontWeight.w500,
                              color: AppColors.ff343A40,
                            ),
                          ),
                        ),
                      ],
                    ),
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        GestureDetector(
                          onTap: () async {
                            const isPermissionAccess = true;
                            if (isPermissionAccess) {
                              if (isSingleImage) {
                                final pickedCameraFile = await picker.pickImage(
                                  source: ImageSource.gallery,
                                  imageQuality: 100,
                                  maxHeight: 1000,
                                  maxWidth: 1000,
                                );
                                List<XFile>? pickedFile;
                                if (pickedCameraFile != null) {
                                  pickedFile = <XFile>[pickedCameraFile];
                                }
                                // final pickedFile = <XFile>[pickedCameraFile!];

                                if (dialogContext.mounted) {
                                  Navigator.pop(dialogContext, pickedFile);
                                }
                              } else if (isMultiMedia) {
                                final pickedFile = await picker
                                    .pickMultipleMedia(
                                      imageQuality: 80,
                                      maxHeight: 600,
                                      maxWidth: 600,
                                    );
                                if (dialogContext.mounted) {
                                  Navigator.pop(dialogContext, pickedFile);
                                }
                              } else {
                                final pickedFile = await picker.pickMultiImage(
                                  imageQuality: 80,
                                  maxHeight: 600,
                                  maxWidth: 600,
                                );

                                if (dialogContext.mounted) {
                                  Navigator.pop(dialogContext, pickedFile);
                                }
                              }
                            }
                          },
                          child: Container(
                            height: AppSize.h70,
                            width: context.width * 0.4,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: AppColors.white,
                              borderRadius: BorderRadius.circular(AppSize.r6),
                              boxShadow: [
                                BoxShadow(
                                  spreadRadius: 2,
                                  blurRadius: 1,
                                  color: AppColors.black.withValues(
                                    alpha: 0.06,
                                  ),
                                  offset: Offset(0, AppSize.h2),
                                ),
                              ],
                            ),
                            child: Icon(
                              Icons.add_photo_alternate_outlined,
                              size: AppSize.sp42,
                              color: AppColors.primaryColor,
                            ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(top: AppSize.h10),
                          child: Text(
                            context.l10n.gallery,
                            style: context.textTheme.titleMedium?.copyWith(
                              fontSize: AppSize.sp14,
                              fontWeight: FontWeight.w500,
                              color: AppColors.ff343A40,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  static Future<bool> uploadFile({
    required String url,
    required Uint8List byteImages,
    required String mimeType,
    void Function(double)? onSendProgress,
    CancelToken? cancelToken,
  }) {
    final baseOptions = BaseOptions(
      connectTimeout: const Duration(minutes: 10),
      sendTimeout: const Duration(minutes: 10),
      receiveTimeout: const Duration(minutes: 10),
      // contentType: mimeType,
      headers: {ApiKeys.contentType: ''},
    );

    final dio = Dio(baseOptions);

    return dio
        .put<Map<String, dynamic>>(
          url,
          data: byteImages,
          cancelToken: cancelToken,
          onSendProgress: (count, total) {
            final progressPercent = count / total;
            onSendProgress?.call(progressPercent);
          },
        )
        .then(
          (value) {
            return true;
          },
          onError: (Object error) {
            if (error is DioException) {
              if (error.type == DioExceptionType.cancel) {
                return false;
              }
            }
            return false;
          },
        )
        .onError((error, stackTrace) {
          return false;
        });
  }

  /// to upload image we need to first generate empty list of image link then we have
  /// put image data into that generated link
  static Future<List<String>>? generateEmptyListImages({
    required List<File> imgList,
    required CancelToken? generateImgToken,
    required bool isClosed,
    required Function(bool loader) whenLoaderChange,
  }) async {
    try {
      if (isClosed) return [];

      whenLoaderChange(true);
      final imagesList = <String>[];
      final completer = Completer<List<String>>();

      final data = FormData();
      data.fields.addAll([
        const MapEntry(ApiKeys.fileExtension, 'jpg'),
        MapEntry(ApiKeys.folderName, ImageTypes.BOOKED_CAR.name),
        MapEntry(ApiKeys.numberOfUrl, imgList.length.toString()),
      ]);
      final request = ApiRequest(
        path: EndPoints.generateUrl,
        data: data,
        cancelToken: generateImgToken,
      );

      final res = await Injector.instance<AccountRepository>().generateUrl(
        request,
      );

      res.when(
        success: (data) async {
          if (isClosed || (generateImgToken?.isCancelled ?? true)) return [];

          // Convert data['keys'] to List<String> and add to imagesList
          imagesList.addAll(data.map((e) => e[ApiKeys.putUrl].toString()));
          final uploadFutures = <Future<void>>[];
          if (imagesList.length == imgList.length) {
            // Upload images

            for (var i = 0; i < imgList.length; i++) {
              uploadFutures.add(
                uploadFile(
                  byteImages: imgList[i].readAsBytesSync(),
                  url: data[i][ApiKeys.putUrl].toString(),
                  mimeType: 'image/jpeg',
                ),
              );
            }
          } else {
            return [];
          }
          // Wait for all uploads to complete
          if (isClosed || (generateImgToken?.isCancelled ?? true)) return [];
          await Future.wait(uploadFutures);
          completer.complete(
            data.map((e) => e[ApiKeys.keyName].toString()).toList(),
          );
        },
        error: (exception) {
          if (generateImgToken?.isCancelled ?? true) return [];
        },
      );
      whenLoaderChange(false);
      return completer.future;
    } catch (e) {
      if (isClosed || (generateImgToken?.isCancelled ?? true)) return [];
      whenLoaderChange(false);
      '===>>>> here from catch part '.logE;
      return [];
    }
  }

  static Header getLoadingHeader() {
    return Platform.isAndroid
        ? const MaterialHeader()
        : const CupertinoHeader();
  }

  static Footer getLoadingFooter() {
    return Platform.isAndroid
        ? const ClassicFooter(
            showText: false,
            showMessage: false,
            progressIndicatorSize: 24,
            progressIndicatorStrokeWidth: 3,
            iconTheme: IconThemeData(color: AppColors.primaryColor),
          )
        : const CupertinoFooter();
  }
}

class NoSpecialCharactersTextInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Allow only letters, numbers, and spaces
    final filteredText = newValue.text.replaceAll(RegExp(r'[^\w\s]'), '');

    return TextEditingValue(
      text: filteredText,
      selection: newValue.selection.copyWith(
        baseOffset: filteredText.length,
        extentOffset: filteredText.length,
      ),
    );
  }
}
