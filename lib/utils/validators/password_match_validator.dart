

import 'package:form_field_validator/form_field_validator.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/utils/validators/global_text_validator.dart';

class ConfirmPasswordValidator extends TextFieldValidator {
  ConfirmPasswordValidator({
    required String errorText,
    required this.password,
  }) : super(errorText);
  final String password;

  @override
  bool get ignoreEmptyValues => false;

  @override
  bool isValid(String? value) => call(value) == null;

  @override
  String? call(String? value) {
    if (value.isEmptyOrNull) {
      return errorText; // e.g. "Confirm Password is required"
    }

    // ✅ Step 1: Validate the original password
    final passwordError = passwordValidator().call(password);
    if (passwordError == null) {
      // ✅ Step 3: Match check
      if (value != password) {
        return rootNavKey.currentContext?.l10n.passwordDoesNotMatch;
      }
    }

    // ✅ Step 2: Validate the confirm password input itself
    final confirmError = passwordValidator().call(value);
    if (confirmError != null) {
      return confirmError;
    }

    // ✅ Step 3: Match check
    if (value != password) {
      return rootNavKey.currentContext?.l10n.passwordDoesNotMatch;
    }

    return null;
  }
}
