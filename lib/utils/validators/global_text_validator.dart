import 'package:form_field_validator/form_field_validator.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/router/app_navigation_service.dart';

class EmptyValueValidator extends TextFieldValidator {
  EmptyValueValidator({required String errorText}) : super(errorText);

  @override
  bool get ignoreEmptyValues => false;

  @override
  bool isValid(String? value) {
    return value!.trim().isNotEmpty;
  }

  @override
  String? call(String? value) {
    return isValid(value) ? null : errorText;
  }
}

MultiValidator emailValidator() => MultiValidator([
  EmptyValueValidator(
    errorText: rootNavKey.currentContext?.l10n.pleaseEnterEmail ?? '',
  ),
  RequiredValidator(
    errorText: rootNavKey.currentContext?.l10n.pleaseEnterEmail ?? '',
  ),
  EmailValidator(
    errorText: rootNavKey.currentContext?.l10n.pleaseEnterValidEmail ?? '',
  ),
]);
MultiValidator currentPasswordValidator() => MultiValidator([
  RequiredValidator(
    errorText: rootNavKey.currentContext?.l10n.pleaseEnterCurrentPassword ?? '',
  ),
]);
MultiValidator passwordValidator() => MultiValidator([
  RequiredValidator(
    errorText: rootNavKey.currentContext?.l10n.pleaseEnterPassword ?? '',
  ),
  MinLengthValidator(
    8,
    errorText:
        rootNavKey.currentContext?.l10n.passwordShouldBeAtLeast8Characters ??
        '',
  ),
  PatternValidator(
    r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>]).{8,}$',
    errorText:
        rootNavKey.currentContext?.l10n.passwordIncludeLetterNumberSymbol ?? '',
  ),
]);
MultiValidator otpValidator() => MultiValidator([
  EmptyValueValidator(
    errorText: rootNavKey.currentContext?.l10n.pleaseEnterValidOtp ?? '',
  ),
  RequiredValidator(
    errorText: rootNavKey.currentContext?.l10n.pleaseEnterValidOtp ?? '',
  ),
  MinLengthValidator(
    4,
    errorText: rootNavKey.currentContext?.l10n.pleaseEnterValidOtp ?? '',
  ),
]);
MultiValidator userNameValidator() => MultiValidator([
  RequiredValidator(
    errorText: rootNavKey.currentContext?.l10n.pleaseEnterValidUsername ?? '',
  ),
  MinLengthValidator(
    1,
    errorText: rootNavKey.currentContext?.l10n.pleaseEnterValidUsername ?? '',
  ),
  EmptyValueValidator(
    errorText: rootNavKey.currentContext?.l10n.pleaseEnterValidUsername ?? '',
  ),
]);
MultiValidator descriptionValidator() => MultiValidator([
  EmptyValueValidator(
    errorText: rootNavKey.currentContext?.l10n.pleaseEnterDescription ?? '',
  ),
  RequiredValidator(
    errorText: rootNavKey.currentContext?.l10n.pleaseEnterDescription ?? '',
  ),
  MinLengthValidator(
    1,
    errorText: rootNavKey.currentContext?.l10n.pleaseEnterDescription ?? '',
  ),
]);
MultiValidator nameValidator() => MultiValidator([
  EmptyValueValidator(
    errorText: rootNavKey.currentContext?.l10n.pleaseEnterName ?? '',
  ),
  RequiredValidator(
    errorText: rootNavKey.currentContext?.l10n.pleaseEnterName ?? '',
  ),
  MinLengthValidator(
    1,
    errorText: rootNavKey.currentContext?.l10n.pleaseEnterName ?? '',
  ),
  PatternValidator(r'^[A-Za-z0-9 ]+$', errorText: 'please enter valid name'),
]);
MultiValidator firstNameValidator() => MultiValidator([
  EmptyValueValidator(
    errorText: rootNavKey.currentContext?.l10n.pleaseEnterFirstName ?? '',
  ),
  RequiredValidator(
    errorText: rootNavKey.currentContext?.l10n.pleaseEnterFirstName ?? '',
  ),
  MinLengthValidator(
    1,
    errorText: rootNavKey.currentContext?.l10n.pleaseEnterFirstName ?? '',
  ),
]);
MultiValidator lastNameValidator() => MultiValidator([
  EmptyValueValidator(
    errorText: rootNavKey.currentContext?.l10n.pleaseEnterLastName ?? '',
  ),
  RequiredValidator(
    errorText: rootNavKey.currentContext?.l10n.pleaseEnterLastName ?? '',
  ),
  MinLengthValidator(
    1,
    errorText: rootNavKey.currentContext?.l10n.pleaseEnterLastName ?? '',
  ),
]);
// MultiValidator commonValidator(String? error) => MultiValidator([
//       RequiredValidator(errorText: error ?? ''),
//     ]);

String? commonValidator({String? inputValue, String? errorMessage}) {
  if (inputValue == null || inputValue.isEmpty) {
    return errorMessage;
  }
  return null;
}
