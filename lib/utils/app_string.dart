class AppStrings {
  static const String carHauler = 'Car Hauler';
  static const String flatBed = 'Flat Bed';
  static const String towTruck = 'Tow Truck';
  static const String bullet = '• ';
  static const String arrowWithoutSpace = '-> ';
  static const String arrow = '   $arrowWithoutSpace';

  /// notification type
  static const String trip = 'TRIP';
  static const String car = 'CAR';
  static const String bookingDetail = 'BOOKING_DETAIL';
  static const String checkList = 'CHECKLIST';
  static const String chat = 'CHAT';
  static const String booking = 'BOOKING';
  static const String exclusive = 'EXCLUSIVE';

  static const String type = 'type';
  static const String sender = 'sender';
  static const String chatRoom = 'chat_room';
  static const String participants = 'participants';
  static const String firstName = 'first_name';
  static const String id = 'id';
  static const String user = 'user';
  static const String bookingDetailStr = 'booking_detail';
  static const String bookedCarId = 'booked_car_id';
  static const String notToRedirect = 'not_to_redirect';
  static const String clientName = 'client_name';
  static const String relatedObjectId = 'related_object_id';
  static const String relatedObjectType = 'related_object_type';
  static const String bookingStatus = 'booking_status';
  static const String relatedObjectData = 'related_object_data';
  static const String bookingId = 'booking_id';
  static const String bookingName = 'booking';
  static const String bookedCar = 'booked_car';
  static const String cancelReason = 'cancellation_reason';



  /// db keys
  static const String bookingApiReqData = 'bookingApiReqData';
  static const String paymentData = 'paymentData';
  static const String token = 'token';
  static const String refreshToken = 'refreshToken';
  static const String internetStatus = 'internetStatus';
  static const String connected = 'connected';
  static const String userModel = 'userModel';
  static const String languageCode = 'languageCode';
  static const String isExclusiveTrip = 'isExclusiveTrip';
}
