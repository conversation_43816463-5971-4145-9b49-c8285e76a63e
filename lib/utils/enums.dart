// App enum will be declared here

// ignore_for_file: constant_identifier_names, public_member_api_docs

/// enum DeviceType :: IOS, ANDROID, WEB
enum DeviceType { IOS, ANDROID }

/// enum demo
/// enum demo
enum Demo { step1, step2, step3 }

/// dummy enum
extension MeetingTypeExtension on Demo {
  String get name {
    switch (this) {
      case Demo.step1:
        return 'step1';
      case Demo.step2:
        return 'step2';
      case Demo.step3:
        return 'step3';
    }
  }
}

enum ImageTypes {
  PROFILE_PICTURES,
  BOOKING_ASSIGNEE_DOCUMENT,
  EQUIPMENT,
  COMPLAINT,
  BOOKED_CAR,
}

enum BookingType { EXCLUSIVE, SHARED }

enum CarVerificationStatus { PENDING, VERIFIED, REJECTED }

enum BookingStatusType {
  IN_PROGRESS,
  CONFIRMED,
  CANCELLED_BY_CUSTOMER,
  CANCELLED_BY_PROVIDER,
  CANCELLED_BY_STOP_ADMIN,
  COMPLETED,
  ONGOING,
  FAILED,
  ALL_UNIT_RECEIVED_BY_DESTINATION_STOP_ADMIN,
  ALL_UNIT_READY_TO_COLLECT,
}

enum CarStatus {
  REMOVED_BY_DRIVER,
  REMOVED_BY_PLATFORM,
  REMOVED_BY_CUSTOMER,
  REMOVED_BY_STOP_ADMIN,
  RECEIVED_BY_ORIGIN_STOP_ADMIN,
  HANDED_OVER_TO_DRIVER,
  RECEIVED_BY_DESTINATION_STOP_ADMIN,
  IN_PROGRESS,
  CONFIRMED,
  FAILED,
  DELIVERED,
}

enum UserType {Customer}

enum UserProofType { CURP, INE, RFC, CEDULA_PROFESSIONAL, PASSPORT }

enum AssigneeType { PICKUP, DROP }

enum AcceptedTripType { CONFIRMED, ONGOING, COMPLETED }

enum ExclusiveTripType { EXCLUSIVE, WAITING_LIST, RESTED }

enum NotificationType { TRIP, BOOKING, BOOKING_DETAIL, CHECKLIST, CHAT }

enum NotificationBookingType { EXCLUSIVE, WAITING_LIST, RESTED }

enum ChecklistRequestDataType { ACCEPTED }

enum PasswordType {
  FORGOT_PASSWORD,
  PROFILE_RESET_PASSWORD,
}
