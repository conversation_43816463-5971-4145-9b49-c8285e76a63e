/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';
import 'package:lottie/lottie.dart' as _lottie;

class AppAssets {
  AppAssets._();

  /// File path: assets/animation_loader/app_loader.json
  static const LottieGenImage animationLoaderAppLoader = LottieGenImage('assets/animation_loader/app_loader.json');

  /// File path: assets/icons/booking.png
  static const AssetGenImage iconsBooking = AssetGenImage('assets/icons/booking.png');

  /// File path: assets/icons/calendar.png
  static const AssetGenImage iconsCalendar = AssetGenImage('assets/icons/calendar.png');

  /// File path: assets/icons/car.png
  static const AssetGenImage iconsCar = AssetGenImage('assets/icons/car.png');

  /// File path: assets/icons/car_hauler.png
  static const AssetGenImage iconsCarHauler = AssetGenImage('assets/icons/car_hauler.png');

  /// File path: assets/icons/chat.png
  static const AssetGenImage iconsChat = AssetGenImage('assets/icons/chat.png');

  /// File path: assets/icons/delete.png
  static const AssetGenImage iconsDelete = AssetGenImage('assets/icons/delete.png');

  /// File path: assets/icons/dollar.png
  static const AssetGenImage iconsDollar = AssetGenImage('assets/icons/dollar.png');

  /// File path: assets/icons/edit_profile.png
  static const AssetGenImage iconsEditProfile = AssetGenImage('assets/icons/edit_profile.png');

  /// File path: assets/icons/flat_bed.png
  static const AssetGenImage iconsFlatBed = AssetGenImage('assets/icons/flat_bed.png');

  /// File path: assets/icons/goldman_sachs.png
  static const AssetGenImage iconsGoldmanSachs = AssetGenImage('assets/icons/goldman_sachs.png');

  /// File path: assets/icons/happy.png
  static const AssetGenImage iconsHappy = AssetGenImage('assets/icons/happy.png');

  /// File path: assets/icons/happy_color.png
  static const AssetGenImage iconsHappyColor = AssetGenImage('assets/icons/happy_color.png');

  /// File path: assets/icons/home.png
  static const AssetGenImage iconsHome = AssetGenImage('assets/icons/home.png');

  /// File path: assets/icons/home_fill.png
  static const AssetGenImage iconsHomeFill = AssetGenImage('assets/icons/home_fill.png');

  /// File path: assets/icons/image_add.png
  static const AssetGenImage iconsImageAdd = AssetGenImage('assets/icons/image_add.png');

  /// File path: assets/icons/left_arrow.png
  static const AssetGenImage iconsLeftArrow = AssetGenImage('assets/icons/left_arrow.png');

  /// File path: assets/icons/location.png
  static const AssetGenImage iconsLocation = AssetGenImage('assets/icons/location.png');

  /// File path: assets/icons/location_origin.png
  static const AssetGenImage iconsLocationOrigin = AssetGenImage('assets/icons/location_origin.png');

  /// File path: assets/icons/mastercard.png
  static const AssetGenImage iconsMastercard = AssetGenImage('assets/icons/mastercard.png');

  /// File path: assets/icons/neutral.png
  static const AssetGenImage iconsNeutral = AssetGenImage('assets/icons/neutral.png');

  /// File path: assets/icons/neutral_color.png
  static const AssetGenImage iconsNeutralColor = AssetGenImage('assets/icons/neutral_color.png');

  /// File path: assets/icons/no_provider.png
  static const AssetGenImage iconsNoProvider = AssetGenImage('assets/icons/no_provider.png');

  /// File path: assets/icons/notes.png
  static const AssetGenImage iconsNotes = AssetGenImage('assets/icons/notes.png');

  /// File path: assets/icons/notification.png
  static const AssetGenImage iconsNotification = AssetGenImage('assets/icons/notification.png');

  /// File path: assets/icons/notification_fill.png
  static const AssetGenImage iconsNotificationFill = AssetGenImage('assets/icons/notification_fill.png');

  /// File path: assets/icons/pin.png
  static const AssetGenImage iconsPin = AssetGenImage('assets/icons/pin.png');

  /// File path: assets/icons/profile.png
  static const AssetGenImage iconsProfile = AssetGenImage('assets/icons/profile.png');

  /// File path: assets/icons/profile_fill.png
  static const AssetGenImage iconsProfileFill = AssetGenImage('assets/icons/profile_fill.png');

  /// File path: assets/icons/send.png
  static const AssetGenImage iconsSend = AssetGenImage('assets/icons/send.png');

  /// File path: assets/icons/signout.png
  static const AssetGenImage iconsSignout = AssetGenImage('assets/icons/signout.png');

  /// File path: assets/icons/success.png
  static const AssetGenImage iconsSuccess = AssetGenImage('assets/icons/success.png');

  /// File path: assets/icons/swap.png
  static const AssetGenImage iconsSwap = AssetGenImage('assets/icons/swap.png');

  /// File path: assets/icons/timer.png
  static const AssetGenImage iconsTimer = AssetGenImage('assets/icons/timer.png');

  /// File path: assets/icons/tow_truck.png
  static const AssetGenImage iconsTowTruck = AssetGenImage('assets/icons/tow_truck.png');

  /// File path: assets/icons/trip.png
  static const AssetGenImage iconsTrip = AssetGenImage('assets/icons/trip.png');

  /// File path: assets/icons/trip_color.png
  static const AssetGenImage iconsTripColor = AssetGenImage('assets/icons/trip_color.png');

  /// File path: assets/icons/trip_fill.png
  static const AssetGenImage iconsTripFill = AssetGenImage('assets/icons/trip_fill.png');

  /// File path: assets/icons/unhappy.png
  static const AssetGenImage iconsUnhappy = AssetGenImage('assets/icons/unhappy.png');

  /// File path: assets/icons/unhappy_color.png
  static const AssetGenImage iconsUnhappyColor = AssetGenImage('assets/icons/unhappy_color.png');

  /// File path: assets/icons/vary_happy.png
  static const AssetGenImage iconsVaryHappy = AssetGenImage('assets/icons/vary_happy.png');

  /// File path: assets/icons/vary_happy_color.png
  static const AssetGenImage iconsVaryHappyColor = AssetGenImage('assets/icons/vary_happy_color.png');

  /// File path: assets/icons/vary_unhappy.png
  static const AssetGenImage iconsVaryUnhappy = AssetGenImage('assets/icons/vary_unhappy.png');

  /// File path: assets/icons/vary_unhappy_color.png
  static const AssetGenImage iconsVaryUnhappyColor = AssetGenImage('assets/icons/vary_unhappy_color.png');

  /// File path: assets/icons/visa.png
  static const AssetGenImage iconsVisa = AssetGenImage('assets/icons/visa.png');

  /// File path: assets/images/android_12_logo.png
  static const AssetGenImage imagesAndroid12Logo = AssetGenImage('assets/images/android_12_logo.png');

  /// File path: assets/images/logo.png
  static const AssetGenImage imagesLogo = AssetGenImage('assets/images/logo.png');

  /// File path: assets/images/pin.png
  static const AssetGenImage imagesPin = AssetGenImage('assets/images/pin.png');

  /// File path: assets/images/pin2.png
  static const AssetGenImage imagesPin2 = AssetGenImage('assets/images/pin2.png');

  /// File path: assets/images/upgrade_logo.png
  static const AssetGenImage imagesUpgradeLogo = AssetGenImage('assets/images/upgrade_logo.png');

  /// List of all assets
  static List<dynamic> get values => [
        animationLoaderAppLoader,
        iconsBooking,
        iconsCalendar,
        iconsCar,
        iconsCarHauler,
        iconsChat,
        iconsDelete,
        iconsDollar,
        iconsEditProfile,
        iconsFlatBed,
        iconsGoldmanSachs,
        iconsHappy,
        iconsHappyColor,
        iconsHome,
        iconsHomeFill,
        iconsImageAdd,
        iconsLeftArrow,
        iconsLocation,
        iconsLocationOrigin,
        iconsMastercard,
        iconsNeutral,
        iconsNeutralColor,
        iconsNoProvider,
        iconsNotes,
        iconsNotification,
        iconsNotificationFill,
        iconsPin,
        iconsProfile,
        iconsProfileFill,
        iconsSend,
        iconsSignout,
        iconsSuccess,
        iconsSwap,
        iconsTimer,
        iconsTowTruck,
        iconsTrip,
        iconsTripColor,
        iconsTripFill,
        iconsUnhappy,
        iconsUnhappyColor,
        iconsVaryHappy,
        iconsVaryHappyColor,
        iconsVaryUnhappy,
        iconsVaryUnhappyColor,
        iconsVisa,
        imagesAndroid12Logo,
        imagesLogo,
        imagesPin,
        imagesPin2,
        imagesUpgradeLogo
      ];
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.low,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class LottieGenImage {
  const LottieGenImage(
    this._assetName, {
    this.flavors = const {},
  });

  final String _assetName;
  final Set<String> flavors;

  _lottie.LottieBuilder lottie({
    Animation<double>? controller,
    bool? animate,
    _lottie.FrameRate? frameRate,
    bool? repeat,
    bool? reverse,
    _lottie.LottieDelegates? delegates,
    _lottie.LottieOptions? options,
    void Function(_lottie.LottieComposition)? onLoaded,
    _lottie.LottieImageProviderFactory? imageProviderFactory,
    Key? key,
    AssetBundle? bundle,
    Widget Function(
      BuildContext,
      Widget,
      _lottie.LottieComposition?,
    )? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    double? width,
    double? height,
    BoxFit? fit,
    AlignmentGeometry? alignment,
    String? package,
    bool? addRepaintBoundary,
    FilterQuality? filterQuality,
    void Function(String)? onWarning,
  }) {
    return _lottie.Lottie.asset(
      _assetName,
      controller: controller,
      animate: animate,
      frameRate: frameRate,
      repeat: repeat,
      reverse: reverse,
      delegates: delegates,
      options: options,
      onLoaded: onLoaded,
      imageProviderFactory: imageProviderFactory,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      package: package,
      addRepaintBoundary: addRepaintBoundary,
      filterQuality: filterQuality,
      onWarning: onWarning,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
