import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/models/provider_model.dart';
import 'package:transport_match/presentation/modules/home_module/provider/home_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/utils/logger.dart';
import 'package:transport_match/widgets/no_of_vehicle_widget.dart';
import 'package:transport_match/widgets/title_info.dart';

/// Stock transport details widget
class TransportDetailsCard extends StatelessWidget {
  /// Constructor
  const TransportDetailsCard({
    super.key,
    this.providerData,
    required this.totalCar,
    this.homeController,
    required this.isOpen,
    required this.onOpen,
    required this.isFirstScreen,
    required this.isTrip,
    this.selectedTripData,
    this.homeProvider
  });
  final ProviderListData? providerData;
  final int totalCar;
  final HomeProvider? homeController;
  final TripModel? selectedTripData;
  final bool isOpen;
  final bool isFirstScreen;
  final bool isTrip;
  final Function() onOpen;
  final HomeProvider? homeProvider;

  @override
  Widget build(BuildContext context) {
    
    final finalSlot =
        ((homeProvider?.totalSlots != null && homeProvider?.totalSlots != 0)
                ? (providerData?.availableSlot ?? 0) >= (homeProvider?.totalSlots ?? 0)
                      ? homeProvider?.totalSlots
                      : providerData?.availableSlot
                : providerData?.availableSlot)
            ?.round();
    '|||||=> final slots = ${finalSlot}'.logD;
    '|||||=> total car = ${totalCar}'.logD;
    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppSize.r12),
      ),
      padding: EdgeInsets.all(AppSize.w10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _ColumnText(
                title: context.l10n.transporter,
                value: providerData?.companyName ?? '',
              ),
              _ColumnText(
                title: context.l10n.total_trip_cost,
                // value: '${(providerData?.totalCost?.toInt() ?? 1)*totalCar}'.smartFormat(),
                value: '${(providerData?.totalCost ?? 1) * (finalSlot ?? totalCar)}'.smartFormat(),
                isEnd: true,
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              NoOfVehicleWidget(noOfVehicle: totalCar.toString()),
              if (!isTrip)
                Text(
                  // '${(providerData?.totalCost?.toInt() ?? 0).toString().smartFormat()}/car',
                  '${(providerData?.totalCost ?? 0).toString().smartFormat()}/slot',

                  style: context.textTheme.bodySmall,
                ),
            ],
          ),
          Padding(
            padding: EdgeInsets.only(top: AppSize.h16, bottom: AppSize.h10),
            child: _LocationRow(
              icon: AppAssets.iconsLocation.image(height: AppSize.h18),
              title:
                  '${context.l10n.pickupFrom} '
                  '${isTrip ? selectedTripData?.userStartLocation?.city : homeController?.selectedOriginStockLocation.value?.name}',
              date:
                  (isTrip
                          ? selectedTripData?.customerStartDate
                          : providerData
                                ?.customerStartStopLocation
                                ?.estimatedArrivalDate)
                      ?.monthDateFormate ??
                  '',
            ),
          ),
          _LocationRow(
            icon: AppAssets.iconsLocation.image(height: AppSize.h18),
            title:
                '${context.l10n.dropAt} ${isTrip ? selectedTripData?.userEndLocation?.city : homeController?.selectedDropStockLocation.value?.name}',
            date:
                (isTrip
                        ? selectedTripData?.customerEndDate
                        : providerData
                              ?.customerEndStopLocation
                              ?.estimatedArrivalDate)
                    ?.monthDateFormate ??
                '',
          ),
          Gap(AppSize.h16),
          if (!isOpen && !isFirstScreen)
            GestureDetector(
              onTap: onOpen,
              child: Center(
                child: Text(
                  context.l10n.viewDetails,
                  style: context.textTheme.bodyLarge?.copyWith(
                    fontSize: AppSize.sp16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.primaryColor,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Column text widget
class _ColumnText extends StatelessWidget {
  /// Constructor
  const _ColumnText({
    required this.title,
    required this.value,
    this.isEnd = false,
  });

  /// Title
  final String title;

  /// Value
  final String value;

  final bool isEnd;

  @override
  Widget build(BuildContext context) {
    return TitleInfo(
      isAxisEnd: isEnd,
      title: title,
      subTitle: value,
      subTitleColor: isEnd ? AppColors.ff67509C : null,
      subTitleFontWeight: isEnd ? FontWeight.w600 : null,
      subTitleSize: isEnd ? AppSize.sp16 : null,
    );
  }
}

///Shipment location widget
class _LocationRow extends StatelessWidget {
  /// Constructor
  const _LocationRow({
    required this.icon,
    required this.title,
    required this.date,
  });

  /// Icon
  final Widget icon;

  /// Title
  final String title;

  /// Date
  final String date;

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppSize.r12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: Row(
              spacing: AppSize.w8,
              children: [
                icon,
                Flexible(
                  child: Text(title, style: context.textTheme.bodyLarge),
                ),
              ],
            ),
          ),
          Text(
            date,
            style: context.textTheme.bodyLarge?.copyWith(
              fontSize: AppSize.sp12,
              fontWeight: FontWeight.bold,
              color: AppColors.ff67509C,
            ),
          ),
        ],
      ),
    );
  }
}
