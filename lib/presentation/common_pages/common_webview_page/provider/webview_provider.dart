// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/common_pages/common_webview_page/models/common_web_view_params.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/shared/repositories/home_repository.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/app_string.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/utils/logger.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebViewProvider extends ChangeNotifier {
  WebViewProvider(CommonWebViewParams webviewData) {
    '/// noww webviewData : ${webviewData.bookingId}'.logV;
    if (webviewData.url.isNotEmptyAndNotNull &&
        !webviewData.isSettlementAmount) {
      webViewController = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setNavigationDelegate(
          NavigationDelegate(
            onPageStarted: (String url) {
              '/// noww urlll : $url'.logV;
              isWebViewShowLoader.value = true;
              if (url.contains('providers')) {
                AppNavigationService.pop(webviewData.context);
              }
            },
            onPageFinished: (String url) {
              '/// noww url finish : $url'.logV;
              '/// noww url payments : ${url.contains('https://staging.d171pkw491jul9.amplifyapp.com/signin/')}'
                  .logV;
              if (url.contains(
                'https://staging.d171pkw491jul9.amplifyapp.com/signin/',
              )) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  Future.delayed(const Duration(milliseconds: 100), () {
                    // AppNavigationService.pushAndRemoveAllPreviousRoute(
                    //   context,
                    //   AppRoutes.authBase,
                    //   isBaseRoute: true,
                    // );
                    AppNavigationService.pushAndRemoveAllPreviousRoute(
                      webviewData.context,
                      AppRoutes.homeBase,
                    );
                  });
                });
              }
              isWebViewShowLoader.value = false;
            },
          ),
        )
        ..loadRequest(Uri.parse(webviewData.url ?? ''));
    } else if (webviewData.isSettlementAmount &&
        webviewData.url.isNotEmptyAndNotNull) {
      // webviewData.context.l10n.pleaseWaitWeAreRedirectingPayment
      //     .showSuccessAlert();
      webViewController = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setNavigationDelegate(
          NavigationDelegate(
            onProgress: (int progress) {
              // Update loading bar.
            },
            onPageStarted: (String url) {
              // isWebViewShowLoader.value = true;
              if (url.contains('providers')) {
                AppNavigationService.pop(webviewData.context);
              }
            },
            onPageFinished: (String url) {
              if (url.contains(
                    'https://staging.d171pkw491jul9.amplifyapp.com/payment/successful',
                  ) &&
                  _isPaymentRelatedUrl(url)) {
                WidgetsBinding.instance.addPostFrameCallback((_) async {
                  // Close webview first
                  unawaited(
                    AppNavigationService.pushAndRemoveAllPreviousRoute(
                      webviewData.context,
                      AppRoutes.homeBase,
                    ),
                  );
                  // Show success dialog
                  await _showPaymentSuccessDialog(webviewData.context);
                });
              } else if (url.contains(
                    'https://staging.d171pkw491jul9.amplifyapp.com/payment/failed/',
                  ) &&
                  _isPaymentRelatedUrl(url)) {
                '/// navigate hereee back failed'.logV;
                WidgetsBinding.instance.addPostFrameCallback((_) async {
                  // Close webview first
                  AppNavigationService.pop(webviewData.context);
                  // Show failure dialog
                  await _showPaymentFailureDialog(webviewData.context);
                });
              }

              // isWebViewShowLoader.value = false;
            },
            onHttpError: (HttpResponseError error) {},
            onWebResourceError: (WebResourceError error) {},
          ),
        )
        ..loadRequest(Uri.parse(webviewData.url ?? ''));
    } else {
      '/// noww webviewData : ${webviewData.bodyData}'.logV;
      '/// noww webviewData bookingId : ${webviewData.bookingId}'.logV;
      getBookingUrl(
        webviewData.context,
        bodyData: webviewData.bodyData,
        isExclusiveTrip: webviewData.isExclusiveTrip,
        isFromHome: webviewData.isFromHome,
        bookingId: webviewData.bookingId,
        isRemainAmountAPI: webviewData.isRemainAmountAPI,
      );
    }
  }

  final isWebViewShowLoader = ValueNotifier<bool>(false);
  String url = '';
  bool _isClosed = false;

  WebViewController? webViewController;

  /// Notify listeners if not closed
  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'acceptedTripProvider notify error: $e'.logE;
    }
  }

  /// get payment information
  CancelToken? getBookingCancelToken;
  final isShowLoader = ValueNotifier<bool>(false);
  Future<void> getBookingUrl(
    BuildContext context, {
    bool isExclusiveTrip = false,
    bool isFromHome = false,
    bool isRemainAmountAPI = false,
    String? bookingId,
    required Map<String, dynamic>? bodyData,
  }) async {
    getBookingCancelToken?.cancel();
    getBookingCancelToken = CancelToken();
    final homeRepository = Injector.instance<HomeRepository>();

    try {
      '/// noww bodyData : ${bodyData.toString()}'.logV;
      isShowLoader.value = true;
      final response = await homeRepository.getBookingUrl(
        ApiRequest(
          path: isRemainAmountAPI
              ? isExclusiveTrip
                    ? EndPoints.getRemainExclusiveBookingUrl
                    : EndPoints.getRemainBookingUrl
              : isExclusiveTrip
              ? EndPoints.getExclusiveBookingUrl
              : EndPoints.getBookingUrl,
          cancelToken: getBookingCancelToken,
          data: bodyData,
        ),
      );
      isShowLoader.value = false;
      response.when(
        success: (data) {
          '/// rrr bookingId : ${data}'.logV;
          '/// noww data[AppStrings.bookingId] : ${data[AppStrings.bookingId]}'
              .logV;
          if (_isClosed || (getBookingCancelToken?.isCancelled ?? true)) return;
          context.l10n.pleaseWaitWeAreRedirectingPayment.showSuccessAlert();
          webViewController = WebViewController()
            ..setJavaScriptMode(JavaScriptMode.unrestricted)
            ..setNavigationDelegate(
              NavigationDelegate(
                onProgress: (int progress) {
                  // Update loading bar.
                },
                onPageStarted: (String url) {
                  '/// noww bookingId // pageStarted= $bookingId'.logD;
                  '/// noww onPageStarttttt = $url'.logV;
                  '/// isFromHome = $isFromHome'.logV;
                  if (isFromHome) {
                    // if (url.contains('https://staging.d171pkw491jul9.amplifyapp.com/payment/successful')) {
                    //   '//// navigate through here 1'.logV;
                    //   '//// navigate through here 1 bookingId = ${data[AppStrings.bookingId]}'.logV;
                    //   AppNavigationService.pushAndRemoveAllPreviousRoute(
                    //     context,
                    //     AppRoutes.homeUserInfoScreen,
                    //     extra: (data[AppStrings.bookingId])
                    //         ?.toString(),
                    //   );
                    //   return;
                    // } else {
                    isWebViewShowLoader.value = true;
                    // }
                  } else if (url.contains('providers/')) {
                    if (isRemainAmountAPI) {
                      '//// navigate through here 2'.logV;
                      AppNavigationService.pushAndRemoveAllPreviousRoute(
                        context,
                        AppRoutes.homeUserInfoScreen,
                      );
                      return;
                    }
                    AppNavigationService.pop(context);
                    return;
                  } else {
                    isWebViewShowLoader.value = true;
                  }
                },
                onPageFinished: (String url) {
                  '/// noww bookingId // pageFinish= $bookingId'.logD;
                  '/// noww bookingId ?? = ${data[AppStrings.bookingId]}'.logD;
                  '/// noww webviewdata.bookingId '
                          '/// noww my url = $url'
                      .logD;
                  '/// noww isContain success = ${url.contains('https://staging.d171pkw491jul9.amplifyapp.com/payment/successful')}'
                      .logD;
                  '/// noww isContain failed = ${url.contains('https://staging.d171pkw491jul9.amplifyapp.com/payment/failed/')}'
                      .logD;
                  '/// noww isRemainAmountAPI = $isRemainAmountAPI'.logD;
                  if (url.contains(
                        'https://staging.d171pkw491jul9.amplifyapp.com/payment/successful',
                      ) &&
                      isRemainAmountAPI &&
                      _isPaymentRelatedUrl(url)) {
                    '/// navigate hereee home'.logV;
                    WidgetsBinding.instance.addPostFrameCallback((_) async {
                      // Close webview first
                      unawaited(
                        AppNavigationService.pushAndRemoveAllPreviousRoute(
                          context,
                          AppRoutes.homeBase,
                        ),
                      );
                      // Show success dialog
                      await _showPaymentSuccessDialog(context);
                    });
                  } else if (url.contains(
                        'https://staging.d171pkw491jul9.amplifyapp.com/payment/successful',
                      ) &&
                      !isRemainAmountAPI &&
                      _isPaymentRelatedUrl(url)) {
                    '/// navigate hereee userInfo'.logV;
                    WidgetsBinding.instance.addPostFrameCallback((_) async {
                      '//// navigate through here 3'.logV;
                      '//// navigate through here 3 bookingId = ${data[AppStrings.bookingId]}'
                          .logV;
                      '/// after bookingId = $bookingId'.logD;
                      '/// after bookingId ?? = ${data[AppStrings.bookingId]}'
                          .logD;
                      // Close webview first
                      unawaited(
                        AppNavigationService.pushAndRemoveAllPreviousRoute(
                          context,
                          AppRoutes.homeUserInfoScreen,
                          extra: data[AppStrings.bookingId]?.toString(),
                        ),
                      );
                      // Show success dialog
                      await _showPaymentSuccessDialog(context);
                    });
                  } else if (url.contains(
                        'https://staging.d171pkw491jul9.amplifyapp.com/payment/failed/',
                      ) &&
                      _isPaymentRelatedUrl(url)) {
                    '/// navigate hereee back failed'.logV;
                    WidgetsBinding.instance.addPostFrameCallback((_) async {
                      // Close webview first
                      AppNavigationService.pop(context);
                      // Show failure dialog
                      await _showPaymentFailureDialog(context);
                    });
                  }

                  isWebViewShowLoader.value = false;
                },
                onHttpError: (HttpResponseError error) {},
                onWebResourceError: (WebResourceError error) {},
              ),
            )
            ..loadRequest(Uri.parse(data['session_url']?.toString() ?? ''));
          // AppNavigationService.pushNamed(
          //   context,
          //   AppRoutes.commonWebViewScreen,
          //   extra: CommonWebViewParams(
          //     url: data['session_url'].toString(),
          //     isExclusiveTrip: isExclusiveTrip,
          //   ),

          //   //  CommonWebView(
          //   //   url: data['session_url'].toString(),
          //   //   isExclusiveTrip: isExclusiveTrip,
          //   // ),
          // );
          notify();
        },
        error: (error) {
          if (_isClosed || (getBookingCancelToken?.isCancelled ?? true)) return;
          if (error.code == 308) {
            AppNavigationService.pushAndRemoveAllPreviousRoute(
              context,
              AppRoutes.homeBase,
              isBaseRoute: true,
            );
          }
          AppNavigationService.pop(context);
          error.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (getBookingCancelToken?.isCancelled ?? true)) return;
      AppNavigationService.pop(context);
      context.l10n.somethingWentWrong.showErrorAlert();
      'getBookingUrl Error: $e'.logE;
    }
  }

  /// Show payment success dialog
  Future<void> _showPaymentSuccessDialog(BuildContext context) async {
    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => AlertDialog(
        insetPadding: EdgeInsets.all(AppSize.h24),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(AppSize.r12)),
        ),
        icon: AppAssets.iconsSuccess.image(
          height: AppSize.h70,
          fit: BoxFit.fitHeight,
        ),
        title: Text(
          context.l10n.paymentSuccessful,
          style: context.textTheme.titleLarge,
          textAlign: TextAlign.center,
        ),
        content: Padding(
          padding: EdgeInsets.symmetric(horizontal: AppSize.w8),
          child: Text(
            context.l10n.paymentCompletedSuccessfully,
            textAlign: TextAlign.center,
            style: context.textTheme.titleMedium?.copyWith(
              color: AppColors.ff6C757D,
            ),
          ),
        ),
        actions: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppSize.w16),
            child: AppButton(
              text: context.l10n.continues,
              onPressed: () {
                Navigator.of(dialogContext).pop();
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Show payment failure dialog
  Future<void> _showPaymentFailureDialog(BuildContext context) async {
    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => AlertDialog(
        insetPadding: EdgeInsets.all(AppSize.h24),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(AppSize.r12)),
        ),
        icon: Container(
          height: AppSize.h70,
          width: AppSize.h70,
          decoration: const BoxDecoration(
            color: AppColors.errorColor,
            shape: BoxShape.circle,
          ),
          child: Icon(Icons.close, color: AppColors.white, size: AppSize.h40),
        ),
        title: Text(
          context.l10n.paymentFailed,
          style: context.textTheme.titleLarge,
          textAlign: TextAlign.center,
        ),
        content: Padding(
          padding: EdgeInsets.symmetric(horizontal: AppSize.w8),
          child: Text(
            context.l10n.paymentFailedMessage,
            textAlign: TextAlign.center,
            style: context.textTheme.titleMedium?.copyWith(
              color: AppColors.ff6C757D,
            ),
          ),
        ),
        actions: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: AppSize.w16),
            child: AppButton(
              text: context.l10n.continues,
              onPressed: () {
                Navigator.of(dialogContext).pop();
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Check if URL is a payment-related URL (not portal, privacy policy, etc.)
  bool _isPaymentRelatedUrl(String url) {
    // Only show dialogs for payment success/failure URLs, not for other pages
    return url.contains('/payment/successful') ||
        url.contains('/payment/failed');
  }

  @override
  void dispose() {
    getBookingCancelToken?.cancel();
    _isClosed = true;

    super.dispose();
  }
}
