import 'package:flutter/material.dart';

class CommonWebViewParams {
  const CommonWebViewParams({
    this.bodyData,
    required this.context,
    this.url,
    this.bookingId,
    this.isExclusiveTrip = false,
    this.isFromHome = false,
    this.isRemainAmountAPI = false,
    this.isSettlementAmount = false,
  });

  final bool isExclusiveTrip;
  final bool isFromHome;
  /// for settlement amount url need to pass isSettlementAmount parameter true
  final String? url;
  final String? bookingId;
  final bool isRemainAmountAPI;
  final BuildContext context;
  final Map<String, dynamic>? bodyData;
  /// for settlement amount url need to pass true
  final bool isSettlementAmount;
}
