import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/common_pages/car_info_page/models/car_info_params.dart';
import 'package:transport_match/presentation/common_pages/car_info_page/provider/car_info_provider.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/app_string.dart';
import 'package:transport_match/widgets/app_image.dart';
import 'package:transport_match/widgets/app_textfield.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';
import 'package:transport_match/widgets/marqee_widget.dart';
import 'package:transport_match/widgets/widget_zoom/widget_zoom.dart';

class CarInfoScreen extends StatelessWidget {
  const CarInfoScreen({super.key, required this.carInfoParams});

  final CarInfoParams carInfoParams;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => CarInfoProvider(),
      builder: (context, child) => Scaffold(
        backgroundColor: AppColors.ffF8F9FA,
        appBar: CustomAppBar(
          title: context.l10n.vehicleInfo,
         /* actions: [
            if (carInfoParams.carDetail.isBookedCarCancellable)
              GestureDetector(
                onTap: () {
                  // tripDetailProvider.removeBookedCar(carData.id.toString(), context);
                  context.showAlertDialog(
                    defaultActionText: context.l10n.yes,
                    cancelActionText: context.l10n.no,
                    onCancelActionPressed: (dialogContext) {
                      context.pop();
                    },
                    onDefaultActionPressed: (dialogContext) {
                      context.read<CarInfoProvider>().removeBookedCar(
                        carInfoParams.carDetail.id.toString() ,
                        context,
                      );
                      context.pop();
                      'test '.logD;
                    },
                    titleWidget: Text(
                      '${context.l10n.cancel} ${context.l10n.car}',
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 22, fontWeight: FontWeight.w500, color: AppColors.ff6C757D),
                    ),
                    contentWidget: Text(
                      context.l10n.areUSureCancel,
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 15),
                    ),
                  );
                },
                child: Text(
                  '${context.l10n.cancel} ${context.l10n.car}',
                  style: context.textTheme.titleLarge?.copyWith(
                    color: AppColors.red,
                    fontSize: AppSize.sp14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],*/
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: AppSize.appPadding, vertical: AppSize.h20),
          child: Column(
            spacing: AppSize.h20,
            children: [
              DecoratedBox(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(AppSize.r10)),
                  color: AppColors.white,
                ),
                child: Padding(
                  padding: EdgeInsets.all(AppSize.h16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    spacing: AppSize.h16,
                    children: [
                      Text(
                        context.l10n.vehicleInfo,
                        style: context.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w700,
                          fontSize: AppSize.sp20,
                        ),
                      ),
                      if (carInfoParams.carDetail.brandName?.isNotEmpty ?? false)
                        _buildInfoRow(
                          title: context.l10n.vehicleBrand,
                          value: carInfoParams.carDetail.brandName ?? '',
                          context: context,
                        ),
                      if (carInfoParams.carDetail.carName?.isNotEmpty ?? false)
                        _buildInfoRow(
                          title: context.l10n.vehicleModel,
                          value: carInfoParams.carDetail.carName ?? '',
                          context: context,
                        ),
                      if (carInfoParams.carDetail.yearValue?.isNotEmpty ?? false)
                        _buildInfoRow(
                          title: context.l10n.vehicleYear,
                          value: carInfoParams.carDetail.yearValue?.toString() ?? '-',
                          context: context,
                        ),
                      if (carInfoParams.carDetail.carSize != null)
                        _buildInfoRow(
                          title: context.l10n.vehicleSize,
                          value: switch (carInfoParams.carDetail.carSize) {
                            1 => context.l10n.small,
                            1.5 => context.l10n.medium,
                            _ => context.l10n.large,
                          },
                          context: context,
                        ),
                      if (carInfoParams.carDetail.serialNumber?.isNotEmpty ?? false)
                        _buildInfoRow(
                          title: context.l10n.vehicleSerialNumber,
                          value: carInfoParams.carDetail.serialNumber ?? '',
                          context: context,
                        ),
                      if (carInfoParams.carDetail.carDescription?.isNotEmpty ?? false)
                        _buildInfoRow(
                          title: context.l10n.vehicleDescription,
                          value: carInfoParams.carDetail.carDescription ?? '',
                          context: context,
                          isMaxLine: true,
                        ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            context.l10n.isWinchRequired,
                            style: context.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                              fontSize: AppSize.sp14,
                              color: AppColors.black,
                            ),
                          ),
                          CircleAvatar(
                            radius: 10,
                            backgroundColor: carInfoParams.carDetail.isWinchRequired ? Colors.green : AppColors.red,
                            child: Icon(
                              carInfoParams.carDetail.isWinchRequired ? Icons.check : Icons.close,
                              size: 13,
                              color: AppColors.white,
                            ),
                          ),
                        ],
                      ),
                      if (carInfoParams.carDetail.isCarPickedUp)
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          spacing: AppSize.w10,
                          children: [
                            Flexible(
                              child: Text(
                                context.l10n.iNeedMyCarToBePickedUpAndTakenToTheStockLocation,
                                style: context.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w500,
                                  fontSize: AppSize.sp14,
                                  color: AppColors.black,
                                ),
                              ),
                            ),
                            const CircleAvatar(
                              radius: 10,
                              backgroundColor: Colors.green,
                              child: Icon(Icons.check, size: 13, color: AppColors.white),
                            ),
                          ],
                        ),
                    ],
                  ),
                ),
              ),
              if (carInfoParams.carDetail.images?.isNotEmpty ?? false)
                DecoratedBox(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(AppSize.r10)),
                    color: AppColors.white,
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(AppSize.h16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          context.l10n.vehicleImages,
                          style: context.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                            fontSize: AppSize.sp16,
                            color: AppColors.black,
                          ),
                        ),
                        GridView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          padding: EdgeInsets.only(top: AppSize.h10),
                          itemCount: carInfoParams.carDetail.images?.length ?? 0,
                          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 4,
                            mainAxisSpacing: 10,
                            crossAxisSpacing: 10,
                          ),
                          itemBuilder: (context, index) {
                            final image = carInfoParams.carDetail.images?[index].imageUrl;
                            return ClipRRect(
                              borderRadius: BorderRadius.circular(AppSize.r5),
                              child: ColoredBox(
                                color: AppColors.primaryColorLight.withValues(alpha: 0.2),
                                child: WidgetZoom(
                                  heroAnimationTag: image ?? '',
                                  zoomWidget: AppImage.network(image ?? '', fit: BoxFit.contain),
                                ),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              if (carInfoParams.carDetail.isCarPickedUp)
                DecoratedBox(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(AppSize.r10)),
                    color: AppColors.white,
                  ),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                      padding: EdgeInsets.all(AppSize.h16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            context.l10n.pickupService,
                            style: context.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                              fontSize: AppSize.sp16,
                              color: AppColors.black,
                            ),
                          ),
                          Gap(AppSize.h5),
                          Text(
                            '${AppStrings.bullet}${carInfoParams.carDetail.serviceType?.serviceType?.upToLower ?? ''}',
                            style: context.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              fontSize: AppSize.sp14,
                              color: AppColors.ff212529,
                            ),
                          ),
                          if (carInfoParams.carDetail.serviceType?.serviceFee != null)
                            Text(
                              '${AppStrings.arrow}${context.l10n.serviceFee}:'
                              ' ${(carInfoParams.carDetail.serviceType?.serviceFee ?? '').toString().smartFormat()}/km',
                              style: context.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w500,
                                fontSize: AppSize.sp14,
                                color: AppColors.ff495057,
                              ),
                            ),
                          if (carInfoParams.carDetail.serviceType?.minimumFee != null)
                            Text(
                              '${AppStrings.arrow}${context.l10n.minimumCost}: '
                              '${(carInfoParams.carDetail.serviceType?.minimumFee ?? '').toString().smartFormat()}',
                              style: context.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w500,
                                fontSize: AppSize.sp14,
                                color: AppColors.ff495057,
                              ),
                            ),
                          Gap(AppSize.h10),
                          Text(
                            context.l10n.pickupAddress,
                            style: context.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                              fontSize: AppSize.sp16,
                              color: AppColors.black,
                            ),
                          ),
                          Gap(AppSize.h5),
                          Text(
                            '${AppStrings.bullet}${carInfoParams.carDetail.fromCarToBePickedUpLocation?.street ?? ''}',
                            style: context.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                              fontSize: AppSize.sp14,
                              color: AppColors.ff495057,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              if (carInfoParams.carDetail.charges?.isNotEmpty ?? false)
                DecoratedBox(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(AppSize.r10)),
                    color: AppColors.white,
                  ),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                      padding: EdgeInsets.all(AppSize.h16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            context.l10n.vehicleCharges,
                            style: context.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                              fontSize: AppSize.sp16,
                              color: AppColors.black,
                            ),
                          ),
                          Gap(AppSize.h5),
                          ListView.separated(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: carInfoParams.carDetail.charges?.length ?? 0,
                            padding: EdgeInsets.zero,
                            separatorBuilder: (context, index) => Gap(AppSize.h10),
                            itemBuilder: (context, index) {
                              final charge = carInfoParams.carDetail.charges?[index];
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '• ${charge?.chargeType?.upToLower ?? ''}',
                                    style: context.textTheme.bodyMedium?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      fontSize: AppSize.sp14,
                                      color: AppColors.ff212529,
                                    ),
                                  ),
                                  Text(
                                    '${AppStrings.arrow}${context.l10n.charge}: '
                                    '${(charge?.charge ?? 0) < 0 ? '-' : ''}'
                                    '${(charge?.charge ?? '').toString().smartFormat()}',
                                    style: context.textTheme.bodyMedium?.copyWith(
                                      fontWeight: FontWeight.w500,
                                      fontSize: AppSize.sp14,
                                      color: (charge?.charge ?? 0) < 0 ? AppColors.errorColor : AppColors.ff495057,
                                    ),
                                  ),
                                  Text(
                                    '${AppStrings.arrow}${context.l10n.note}: ${charge?.chargeDescription ?? ''}',
                                    style: context.textTheme.bodyMedium?.copyWith(
                                      fontWeight: FontWeight.w500,
                                      fontSize: AppSize.sp14,
                                      color: AppColors.ff495057,
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow({
    required String title,
    required String value,
    required BuildContext context,
    bool isMaxLine = false,
  }) {
    return AppTextFormField(
      controller: TextEditingController(text: value),
      readOnly: true,
      title: title,
      maxLine: isMaxLine ? 3 : 1,
    );
  }
}

/// Vehicle info field
class VehiclesInfoField extends StatelessWidget {
  /// Constructor
  const VehiclesInfoField({required this.title, required this.value, super.key});

  /// title
  final String title;

  /// value
  final String value;

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MarqueeWidget(child: Text(title, style: context.textTheme.bodySmall)),
          MarqueeWidget(child: Text(value, maxLines: 1, style: context.textTheme.titleMedium)),
        ],
      ),
    );
  }
}
