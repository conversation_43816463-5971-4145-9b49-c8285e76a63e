import 'dart:async';

import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/models/payment_summary_model.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/shared/repositories/trip_repo.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/app_string.dart';
import 'package:transport_match/utils/logger.dart';

class CarInfoProvider extends ChangeNotifier {
  /// Flag to check if provider is closed
  final bool _isClosed = false;
  final tripRepo = Injector.instance<TripRepository>();
  TripModel? selectedTrip;

  /// Payment summary data
  ValueNotifier<PaymentSummaryData?> paymentData = ValueNotifier(null);

  /// Notify listeners if not closed
  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'acceptedTripProvider notify error: $e'.logE;
    }
  }

  CancelToken? canTripToken;
  bool isReqDataShowLoader = false;

  /// cancel booked car
  Future<dynamic> removeBookedCar(
    String id,
    BuildContext context,
    String reason,
  ) async {
    if (_isClosed) return;

    canTripToken?.cancel();
    canTripToken = CancelToken();
    try {
      final url = EndPoints.customerBookedCarCancel;
      isReqDataShowLoader = true;
      notify();
      final data = {
        AppStrings.bookedCar: int.tryParse(id),
        AppStrings.cancelReason: reason,
      };

      final response = await tripRepo.cancelBookedCar(
        ApiRequest(path: url, data: data, cancelToken: canTripToken),
      );
      response.when(
        success: (data) {
          if (_isClosed || (canTripToken?.isCancelled ?? true)) return;
          context.l10n.carCancelledSuccessfully.showSuccessAlert();
          AppNavigationService.pop(context);
          notify();
        },
        error: (error) {
          if (_isClosed || (canTripToken?.isCancelled ?? true)) return;
          error.message.showErrorAlert();
        },
      );

      isReqDataShowLoader = false;
      notify();
    } catch (e) {
      if (_isClosed || (canTripToken?.isCancelled ?? true)) return;
      isReqDataShowLoader = false;
      notify();
      'removeBookedCar error: $e'.logE;
    }
  }
}
