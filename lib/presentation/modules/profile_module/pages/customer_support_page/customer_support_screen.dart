// ignore_for_file: invalid_use_of_protected_member, invalid_use_of_visible_for_testing_member

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/customer_support_page/provider/customer_support_provider.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/validators/global_text_validator.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/app_padding.dart';
import 'package:transport_match/widgets/app_textfield.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

class CustomerSupportScreen extends StatelessWidget {
  const CustomerSupportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => CustomerSupportProvider(),
      child: Builder(
        builder: (context) {
          final customerSupportProvider = context
              .read<CustomerSupportProvider>();
          return Form(
            key: customerSupportProvider.formKeyCustomerSupport,
            child: Scaffold(
              backgroundColor: AppColors.pageBGColor,
              appBar: CustomAppBar(
                title: context.l10n.customer_support,
                actions: [
                  GestureDetector(
                    onTap: () => AppNavigationService.pushNamed(
                      context,
                      AppRoutes.profileComplaintDetailScreen,
                    ),
                    child: Text(
                      context.l10n.complaints,
                      style: context.textTheme.bodyLarge!.copyWith(
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
              body: ValueListenableBuilder(
                valueListenable: customerSupportProvider.isShowLoader,
                builder: (context, isShowLoader, _) {
                  return AppLoader(
                    isShowLoader: isShowLoader,
                    child: AppPadding.symmetric(
                      horizontal: AppSize.appPadding,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        spacing: AppSize.h10,
                        children: [
                          Gap(AppSize.h1),
                          AppTextFormField(
                            controller:
                                customerSupportProvider.descriptionController,
                            title: context.l10n.description,
                            hintText: context.l10n.pleaseDescribeIssue,
                            maxLine: 7,
                            validator: (p0) => descriptionValidator().call(p0),
                          ),
                          Gap(AppSize.h1),
                          Flexible(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '  ${context.l10n.photosOptional}',
                                  style: context.textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w500,
                                    fontSize: AppSize.sp14,
                                    color: AppColors.black,
                                  ),
                                ),
                                Gap(AppSize.h4),
                                ValueListenableBuilder(
                                  valueListenable:
                                      customerSupportProvider.selectedImages,
                                  builder: (context, imagesList, _) {
                                    return imagesList.isEmpty
                                        ? GestureDetector(
                                            onTap: () {
                                              customerSupportProvider
                                                  .chooseImages(
                                                    context: context,
                                                  );
                                            },
                                            child: Container(
                                              height: AppSize.h100,
                                              width: AppSize.w110,
                                              padding: EdgeInsets.all(
                                                AppSize.sp20,
                                              ),
                                              alignment: Alignment.center,
                                              decoration: BoxDecoration(
                                                color: AppColors.ffF5F0FF,
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                                border: Border.all(
                                                  color: Colors.grey.shade300,
                                                ),
                                              ),
                                              child: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Icon(
                                                    Icons
                                                        .add_photo_alternate_outlined,
                                                    size: AppSize.sp34,
                                                    color: const Color(
                                                      0xFF6A1B9A,
                                                    ), // Purple color for the icon
                                                  ),
                                                ],
                                              ),
                                            ),
                                          )
                                        : Container(
                                            padding: EdgeInsets.all(
                                              AppSize.sp10,
                                            ),
                                            alignment: Alignment.center,
                                            decoration: BoxDecoration(
                                              color: AppColors.ffF5F0FF,
                                              borderRadius:
                                                  BorderRadius.circular(
                                                    AppSize.r12,
                                                  ),
                                              border: Border.all(
                                                color: Colors.grey.shade300,
                                              ),
                                            ),
                                            child: GridView.builder(
                                              itemCount:
                                                  customerSupportProvider
                                                      .selectedImages
                                                      .value
                                                      .length +
                                                  1,
                                              physics:
                                                  const NeverScrollableScrollPhysics(),
                                              shrinkWrap: true,
                                              itemBuilder: (context, index) {
                                                final fileImagesCount =
                                                    customerSupportProvider
                                                        .selectedImages
                                                        .value
                                                        .length;

                                                if (index == fileImagesCount) {
                                                  return fileImagesCount == 7
                                                      ? const SizedBox()
                                                      : InkWell(
                                                          onTap: () {
                                                            customerSupportProvider
                                                                .chooseImages(
                                                                  context:
                                                                      context,
                                                                );
                                                          },
                                                          child: Container(
                                                            decoration: BoxDecoration(
                                                              color: AppColors
                                                                  .ffF5F0FF,
                                                              borderRadius:
                                                                  BorderRadius.circular(
                                                                    AppSize.r12,
                                                                  ),
                                                              border: Border.all(
                                                                color: Colors
                                                                    .grey
                                                                    .shade300,
                                                              ),
                                                            ),
                                                            child: ClipRRect(
                                                              borderRadius:
                                                                  BorderRadius.circular(
                                                                    AppSize.r6,
                                                                  ),
                                                              child: const Icon(
                                                                Icons.add,
                                                              ),
                                                            ),
                                                          ),
                                                        );
                                                } else {
                                                  return Stack(
                                                    children: [
                                                      ClipRRect(
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                              AppSize.r6,
                                                            ),
                                                        child: Image.file(
                                                          File(
                                                            customerSupportProvider
                                                                .selectedImages
                                                                .value[index]
                                                                .path,
                                                          ),
                                                          width: context.width,
                                                          height: context.width,
                                                          fit: BoxFit.cover,
                                                        ),
                                                      ),
                                                      Align(
                                                        alignment:
                                                            Alignment.topRight,
                                                        child: InkWell(
                                                          onTap: () {
                                                            customerSupportProvider
                                                                .selectedImages
                                                                .value
                                                                .removeAt(
                                                                  index,
                                                                );

                                                            // Notify listeners for both cases

                                                            customerSupportProvider
                                                                .selectedImages
                                                                .notifyListeners();
                                                          },
                                                          child: Container(
                                                            margin:
                                                                EdgeInsets.all(
                                                                  AppSize.sp4,
                                                                ),
                                                            height:
                                                                AppSize.sp20,
                                                            width: AppSize.sp20,
                                                            decoration: BoxDecoration(
                                                              color: AppColors
                                                                  .ffF5F0FF,
                                                              borderRadius:
                                                                  BorderRadius.circular(
                                                                    AppSize.r12,
                                                                  ),
                                                              border: Border.all(
                                                                color: Colors
                                                                    .grey
                                                                    .shade300,
                                                              ),
                                                            ),
                                                            child: Icon(
                                                              Icons.close,
                                                              color: AppColors
                                                                  .primaryColor,
                                                              size:
                                                                  AppSize.sp16,
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  );
                                                }
                                              },
                                              gridDelegate:
                                                  const SliverGridDelegateWithFixedCrossAxisCount(
                                                    crossAxisCount: 3,
                                                    mainAxisSpacing: 10,
                                                    crossAxisSpacing: 10,
                                                  ),
                                            ),
                                          );
                                  },
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
              bottomNavigationBar: AppButton(
                text: context.l10n.submit,
                onPressed: () {
                  if (customerSupportProvider
                      .formKeyCustomerSupport
                      .currentState!
                      .validate()) {
                    customerSupportProvider.addComplaint(context: context);
                  }
                },
              ),
            ),
          );
        },
      ),
    );
  }
}
