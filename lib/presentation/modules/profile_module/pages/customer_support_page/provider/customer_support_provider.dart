import 'dart:async';
import 'dart:io';
import 'dart:typed_data';

import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/customer_support_page/models/complaint_model.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/shared/repositories/account_repository.dart';
import 'package:transport_match/shared/rest_api/api_keys.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/utils/logger.dart';

class CustomerSupportProvider extends ChangeNotifier {
  CustomerSupportProvider() {
     getComplaintDetails(isWantShowLoader: true);
  }

  bool _isClosed = false;
  GlobalKey<FormState> formKeyCustomerSupport = GlobalKey<FormState>();
  TextEditingController descriptionController = TextEditingController();
  ValueNotifier<bool> isShowLoader = ValueNotifier(false);
  final selectedImages = ValueNotifier<List<XFile>>([]);
  CancelToken? addComplaintToken;
  List<Result> complaintsList = [];
  final refreshController = EasyRefreshController();

  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'notify error: $e'.logE;
    }
  }

  Future<void> chooseImages({
    required BuildContext context,
  }) async {
    if (_isClosed) return;
    try {
      final list = await AppCommonFunctions.showImagePickerPopup(
            context: context,
          ) ??
          [];

      if (list.isNotEmpty) {
        final remainingSlots = 7 - (selectedImages.value.length);

        if (remainingSlots > 0) {
          // Add only up to the remaining slots
          final imagesToAdd = list.take(remainingSlots).toList();
          selectedImages.value.addAll(imagesToAdd);
          selectedImages.notifyListeners();
        } else {
          // ignore: use_build_context_synchronously
          context.l10n.maxImagesError.showInfoAlert();
        }
      }
    } catch (e) {
      '==>> chooseImages error $e'.logE;
    }
  }

  Future<void> addComplaint({required BuildContext context}) async {
    if (_isClosed) return;
    try {
      addComplaintToken?.cancel();
      addComplaintToken = CancelToken();
      List<String>? imgList = [];

      if (selectedImages.value.isNotEmpty) {
        imgList = await generateEmptyListImages(
          imgList: selectedImages.value.map((e) => File(e.path)).toList(),
        );
      }

      if (imgList != null) {
        // Handle fields
        final data = {
          ApiKeys.description: descriptionController.text.trim(),
          ApiKeys.awsImageKey: imgList,
        };

        isShowLoader.value = true;
        final request = ApiRequest(
          path: EndPoints.customerSupport,
          data: data,
          cancelToken: addComplaintToken,
        );
        final res =
            await Injector.instance<AccountRepository>().addComplaint(request);
        await res.when(
          success: (data) async {
            if (_isClosed || (addComplaintToken?.isCancelled ?? true)) {
              return;
            }
            data.logD;
            isShowLoader.value = false;
            context.l10n.yourComplainSubmittedSuccessfully.showSuccessAlert();
            AppNavigationService.pop(context);
          },
          error: (exception) async {
            if (_isClosed || (addComplaintToken?.isCancelled ?? true)) {
              return;
            }
            isShowLoader.value = false;
            exception.message.showErrorAlert();
          },
        );
      }
    } catch (e) {
      if (_isClosed || (addComplaintToken?.isCancelled ?? true)) return;
      isShowLoader.value = false;
    }
  }

  Future<List<String>>? generateEmptyListImages({
    required List<File> imgList,
  }) async {
    if (_isClosed || isShowLoader.value) return [];

    try {
      final completer = Completer<List<String>>();
      isShowLoader.value = true;

      final data = FormData();
      data.fields.addAll([
        const MapEntry('file_extension', 'jpg'),
        MapEntry('folder_name', ImageTypes.COMPLAINT.name),
        MapEntry('number_of_url', imgList.length.toString()),
      ]);

      final request = ApiRequest(
        path: EndPoints.generateUrl,
        data: data,
      );

      final res =
          await Injector.instance<AccountRepository>().generateUrl(request);

      res.when(
        success: (data) async {
          if (_isClosed) return [];

          final uploadFutures = <Future<void>>[];

          // Loop through the picked images
          if (imgList.length == data.length) {
            for (var i = 0; i < imgList.length; i++) {
              uploadFutures.add(
                _uploadFile(
                  byteImages: imgList[i].readAsBytesSync(),
                  url: data[i]['put_url'].toString(),
                  mimeType: 'image/jpeg',
                ),
              );
            }
          } else {
            rootNavKey.currentContext!.l10n.imagesNotUploaded.showInfoAlert();
          }

          // Wait for all uploads to complete
          await Future.wait(uploadFutures);

          // Convert data['keys'] to List<String> and add to imagesList

          completer
              .complete(data.map((e) => e['key_name'].toString()).toList());
        },
        error: (exception) {
          if (_isClosed) return [];

          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
      return completer.future;
    } catch (e) {
      if (_isClosed) return [];
      isShowLoader.value = false;
      '==>> generateEmptyListImages error $e'.logE;

      return [];
    }
  }

  Future<bool> _uploadFile({
    required String url,
    required Uint8List byteImages,
    required String mimeType,
    void Function(double)? onSendProgress,
    CancelToken? cancelToken,
  }) {
    try {
      final baseOptions = BaseOptions(
        connectTimeout: const Duration(minutes: 10),
        sendTimeout: const Duration(minutes: 10),
        receiveTimeout: const Duration(minutes: 10),
        // contentType: mimeType,
        headers: {
          'Content-type': '',
        },
      );

      final dio = Dio(baseOptions);
      return dio.put<Map<String, dynamic>>(
        url,
        data: byteImages,
        cancelToken: cancelToken,
        onSendProgress: (count, total) {
          final progressPercent = count / total;
          onSendProgress?.call(progressPercent);
        },
      ).then(
        (value) => true,
        onError: (Object error) {
          '======= image url error == here == $error'.logE;
          if (error is DioException) {
            if (error.type == DioExceptionType.cancel) {
              return false;
            }
          }
          return false;
        },
      ).onError((error, stackTrace) {
        '======= image url error $error'.logE;
        return false;
      });
    } catch (e) {
      '==>> _uploadFile error $e'.logE;

      return Future.value(false);
    }
  }

  CancelToken? getComplaintsCancelToken;
  String? nextUrl;
  Future<void> getComplaintDetails({bool isWantShowLoader = false, bool isPagination = false}) async {
    if (_isClosed) return;
    try {
      getComplaintsCancelToken?.cancel();
      getComplaintsCancelToken = CancelToken();

      final request = ApiRequest(
        path: isPagination
            ? nextUrl ?? EndPoints.complaintDetails
            : EndPoints.complaintDetails,
        cancelToken: getComplaintsCancelToken,
      );

      if (isWantShowLoader) {
        isShowLoader.value = true;
      }

      final res = await Injector.instance<AccountRepository>().getComplaintDetails(request);

      if (isWantShowLoader) {
        isShowLoader.value = false;
      }

      await res.when(
        success: (data) async {
          if (_isClosed || (getComplaintsCancelToken?.isCancelled ?? true)) return;
          'Complaint Details : ${data.results}'.logD;
          nextUrl = data.next;
          isShowLoader.value = false;
          if(data.results!.isNotEmpty) {
            complaintsList.addAll(data.results!);
          }
          notify();
        },
        error: (exception) async {
          if (_isClosed || (getComplaintsCancelToken?.isCancelled ?? true)) return;
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch(error) {
      isShowLoader.value = false;
      'Exception : $error'.logE;
    }
  }

  @override
  void dispose() {
    _isClosed = true;
    addComplaintToken?.cancel();
    getComplaintsCancelToken?.cancel();
    isShowLoader.dispose();
    selectedImages.dispose();
    descriptionController.dispose();
    refreshController.dispose();
    super.dispose();
  }
}
