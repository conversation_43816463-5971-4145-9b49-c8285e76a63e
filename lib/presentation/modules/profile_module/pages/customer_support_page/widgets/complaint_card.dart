import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';

class ComplaintCard extends StatelessWidget {
  const ComplaintCard({
    super.key,
    required this.description,
    required this.status,
    required this.createdAt,
  });

  final String description;
  final String status;
  final DateTime createdAt;

  @override
  Widget build(BuildContext context) {

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: AppColors.ffDEE2E6,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            children: [
              Text(
                createdAt.dateFormate,
                style: context.textTheme.bodySmall!.copyWith(color: AppColors.ffADB5BD),
              ),
              const Spacer(),
              Container(
                decoration: BoxDecoration(
                  color: status == 'PENDING' ? AppColors.warningColor.withValues(alpha: 0.1) : AppColors.successColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppSize.r12),
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: AppSize.w10, vertical: AppSize.h2),
                  child: Text(
                    status,
                    style: context.theme.textTheme.labelSmall?.copyWith(
                      color: status == 'PENDING' ? AppColors.warningColor : AppColors.successColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const Gap(5),
          Text(
            description,
            style: context.textTheme.bodyMedium!.copyWith(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
