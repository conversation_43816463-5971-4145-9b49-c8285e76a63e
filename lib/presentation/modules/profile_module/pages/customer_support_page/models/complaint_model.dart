class ComplaintModel {

  ComplaintModel({this.count, this.next, this.previous, this.results});

  factory ComplaintModel.fromJson(Map<String, dynamic> json) {
    return ComplaintModel(
      count: json['count'] as int?,
      next: json['next'] as String?,
      previous: json['previous'] as String?,
      results: (json['results'] as List?)
          ?.map((item) => Result.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  int? count;
  String? next;
  String? previous;
  List<Result>? results;
}

class Result {

  Result({
    this.id,
    this.user,
    this.description,
    this.status,
    this.resolvedBy,
    this.resolutionNote,
    this.createdAt,
    this.images,
  });

  factory Result.fromJson(Map<String, dynamic> json) {
    return Result(
      id: json['id'] as int?,
      user: json['user'] != null ? User.fromJson(json['user'] as Map<String, dynamic>) : null,
      description: json['description'] as String?,
      status: json['status'] as String?,
      resolvedBy: json['resolved_by'] == "null" ? null : json['resolved_by'] as String?,
      resolutionNote: json['resolution_note'] == "null" ? null : json['resolution_note'] as String?,
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'] as String)
          : null,
      images: json['images'] != null
          ? List<dynamic>.from(json['images'] as Iterable)
          : null,
    );
  }

  int? id;
  User? user;
  String? description;
  String? status;
  String? resolvedBy;
  String? resolutionNote;
  DateTime? createdAt;
  List<dynamic>? images;
}

class User {

  User({this.firstName, this.lastName, this.email, this.role});

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] == 'null' ? null : json['last_name'] as String?,
      email: json['email'] as String?,
      role: json['role'] as String?,
    );
  }

  String? firstName;
  String? lastName;
  String? email;
  String? role;
}
