import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/customer_support_page/models/complaint_model.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/customer_support_page/provider/customer_support_provider.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/customer_support_page/widgets/complaint_card.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

class ComplaintDetailsScreen extends StatefulWidget {
  const ComplaintDetailsScreen({super.key});

  @override
  State<ComplaintDetailsScreen> createState() => _ComplaintDetailsScreenState();
}

class _ComplaintDetailsScreenState extends State<ComplaintDetailsScreen> {
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => CustomerSupportProvider(),
      child: Consumer<CustomerSupportProvider>(
        builder: (context, customerSupportProvider, child) {
          return Scaffold(
            backgroundColor: AppColors.pageBGColor,
            appBar: CustomAppBar(title: context.l10n.allComplaints),
            body: Selector<CustomerSupportProvider, (bool, List<Result>, String?)>(
              selector: (p0, p1) => (p1.isShowLoader.value, p1.complaintsList, p1.nextUrl),
              builder: (context, value, child) => Center(
                child: AppLoader(
                  isShowLoader: value.$1,
                  child: EasyRefresh(
                    header: AppCommonFunctions.getLoadingHeader(),
                    footer: AppCommonFunctions.getLoadingFooter(),
                    controller: customerSupportProvider.refreshController,
                    onRefresh: () {
                      customerSupportProvider.getComplaintDetails(isPagination: true, isWantShowLoader: true);
                    },
                    onLoad: value.$3.isNotEmptyAndNotNull
                        ? () => customerSupportProvider.getComplaintDetails(isPagination: true)
                        : null,
                    child: !value.$1 && value.$2.isEmpty
                        ? ListView(
                            children: [
                              SizedBox(
                                height: MediaQuery.of(context).size.height * 0.85,
                                child: Center(
                                  /// change
                                  child: Text(context.l10n.noComplaints),
                                ),
                              ),
                            ],
                          )
                        : ListView.builder(
                      itemCount: customerSupportProvider.complaintsList.length,
                      padding: EdgeInsets.symmetric(horizontal: AppSize.appPadding, vertical: AppSize.appPadding / 2),
                      itemBuilder: (context, index) {
                        final complaint = customerSupportProvider.complaintsList[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 12),
                          child: ComplaintCard(
                            createdAt: complaint.createdAt!,
                            description: complaint.description!,
                            status: complaint.status!,
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
