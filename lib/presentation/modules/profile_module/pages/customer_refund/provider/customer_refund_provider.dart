
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/customer_refund/model/customer_refund_model.dart';
import 'package:transport_match/shared/repositories/trip_repo.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/logger.dart';

class CustomerRefundProvider extends ChangeNotifier {
  CustomerRefundProvider() {
    getCustomerRefund(isWantShowLoader: true);
  }

  bool isClosed = false;
  List<RefundRequest> customerRefundList = [];
  bool isShowLoader = false;
  final refreshController = EasyRefreshController();

  void notify() {
    if (isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      '==>> notify error $e'.logE;
    }
  }

  CancelToken? customerRefundToken;
  CancelToken? cancelToken;
  String? nextUrl;

  Future<void> getCustomerRefund({bool isWantShowLoader = false, bool isPagination = false}) async {
    if (isClosed) return;
    try {
      customerRefundToken?.cancel();
      customerRefundToken = CancelToken();
      final request = ApiRequest(
        path: isPagination ? nextUrl ?? EndPoints.getRefundRequests : EndPoints.getRefundRequests,
        cancelToken: customerRefundToken,
      );
      if (isWantShowLoader) {
        isShowLoader = true;
      }
      final res = await Injector.instance<TripRepository>().getCustomerRefund(request);
      if (isClosed || (customerRefundToken?.isCancelled ?? true)) {
        return;
      }
      if (isWantShowLoader) {
        isShowLoader = false;
      }
      await res.when(
        success: (data) async {
          if (isClosed || (customerRefundToken?.isCancelled ?? true)) {
            return;
          }
          if(!isPagination){
            customerRefundList.clear();
          }
          nextUrl = data.next;
          for (final datas in data.results ?? <RefundRequest>[]) {
            if (!customerRefundList.any((e) => e.id == datas.id)) {
              customerRefundList.add(datas);
            }
          }
          notify();
        },
        error: (exception) async {
          if (isClosed || (customerRefundToken?.isCancelled ?? true)) {
            return;
          }
          isShowLoader = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (isClosed || (customerRefundToken?.isCancelled ?? true)) {
        return;
      }
      isShowLoader = false;
      e.logE;
      // e.toString().showErrorAlert();
    }
  }

  @override
  void dispose() {
    isClosed = true;
    customerRefundToken?.cancel();

    refreshController.dispose();
    super.dispose();
  }
}
