// ignore_for_file: public_member_api_docs

import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/customer_refund/model/customer_refund_model.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/customer_refund/provider/customer_refund_provider.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/customer_refund/widget/customer_refund_card.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

class CustomerRefundScreen extends StatelessWidget {
  const CustomerRefundScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => CustomerRefundProvider(),
      child: Consumer<CustomerRefundProvider>(
        builder: (context, customerRefundProvider, child) {
          return Scaffold(
            backgroundColor: AppColors.pageBGColor,
            appBar: CustomAppBar(title: context.l10n.customerRefundRequest),
            body: Selector<CustomerRefundProvider, (bool, List<RefundRequest>, String?)>(
              selector: (p0, p1) => (p1.isShowLoader, p1.customerRefundList, p1.nextUrl),
              builder: (context, value, child) => Center(
                child: AppLoader(
                  isShowLoader: value.$1,
                  child: EasyRefresh(
                    header: AppCommonFunctions.getLoadingHeader(),
                    footer: AppCommonFunctions.getLoadingFooter(),
                    controller: customerRefundProvider.refreshController,
                    onRefresh: () {
                      customerRefundProvider.getCustomerRefund();
                    },
                    onLoad: value.$3.isNotEmptyAndNotNull
                        ? () => customerRefundProvider.getCustomerRefund(isPagination: true)
                        : null,
                    child: !value.$1 && value.$2.isEmpty
                        ? ListView(
                            children: [
                              SizedBox(
                                height: MediaQuery.of(context).size.height * 0.85,
                                child: Center(
                                  /// change
                                  child: Text(context.l10n.noRefundDataFound),
                                ),
                              ),
                            ],
                          )
                        : ListView.builder(
                            itemCount: customerRefundProvider.customerRefundList.length,
                            itemBuilder: (context, index) {
                              return Padding(
                                padding: EdgeInsets.symmetric(horizontal: AppSize.w20),
                                child: Padding(
                                  padding: EdgeInsets.only(bottom: AppSize.h10),
                                  child: CustomerRefundCard(refund: customerRefundProvider.customerRefundList[index]),
                                ),
                              );
                            },
                          ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
