class CustomerRefundModel {
  CustomerRefundModel({
    this.count,
    this.next,
    this.previous,
    this.results,
  });

  factory CustomerRefundModel.fromJson(Map<String, dynamic> json) =>
      CustomerRefundModel(
        count: json['count'] as int?,
        next: json['next'] as String?,
        previous: json['previous'] as String?,
        results: json['results'] == null
            ? []
            : List<RefundRequest>.from(
            (json['results'] as List<dynamic>)
                .map((x) => RefundRequest.fromJson(x as Map<String, dynamic>))),
      );

  final int? count;
  final String? next;
  final String? previous;
  final List<RefundRequest>? results;

  Map<String, dynamic> toJson() => {
    'count': count,
    'next': next,
    'previous': previous,
    'results': results == null
        ? []
        : List<dynamic>.from(results!.map((x) => x.toJson())),
  };
}

class RefundRequest {
  RefundRequest({
    this.id,
    this.booking,
    this.refundCaseType,
    this.amount,
    this.status,
    this.cancellationReason,
    this.description,
    this.refundRequestId,
    this.createdAt,
    this.updatedBy,
  });

  factory RefundRequest.fromJson(Map<String, dynamic> json) => RefundRequest(
    id: json['id'] as int?,
    booking: json['booking'] == null
        ? null
        : Booking.fromJson(json['booking'] as Map<String, dynamic>),
    refundCaseType: json['refund_case_type'] == null
        ? []
        : List<RefundCaseType>.from(
        (json['refund_case_type'] as List<dynamic>)
            .map((x) => RefundCaseType.fromJson(x as Map<String, dynamic>))),
    amount: json['amount'] as num?,
    status: json['status'] as String?,
    cancellationReason: json['cancellation_reason'] as String?,
    description: json['description'] as String?,
    refundRequestId: json['refund_request_id'] as String?,
    createdAt: json['created_at'] == null
        ? null
        : DateTime.parse(json['created_at'] as String),
    updatedBy: json['updated_by'] == null
        ? null
        : User.fromJson(json['updated_by'] as Map<String, dynamic>),
  );

  final int? id;
  final Booking? booking;
  final List<RefundCaseType>? refundCaseType;
  final num? amount;
  final String? status;
  final String? cancellationReason;
  final String? description;
  final String? refundRequestId;
  final DateTime? createdAt;
  final User? updatedBy;

  Map<String, dynamic> toJson() => {
    'id': id,
    'booking': booking?.toJson(),
    'refund_case_type': refundCaseType == null
        ? []
        : List<dynamic>.from(refundCaseType!.map((x) => x.toJson())),
    'amount': amount,
    'status': status,
    'cancellation_reason': cancellationReason,
    'description': description,
    'refund_request_id': refundRequestId,
    'created_at': createdAt?.toUtc().toIso8601String(),
    'updated_by': updatedBy?.toJson(),
  };
}

class Booking {
  Booking({
    this.id,
    this.firstName,
    this.lastName,
    this.email,
  });

  factory Booking.fromJson(Map<String, dynamic> json) => Booking(
    id: json['id'] as int?,
    firstName: json['first_name'] as String?,
    lastName: json['last_name'] as String?,
    email: json['email'] as String?,
  );

  final int? id;
  final String? firstName;
  final String? lastName;
  final String? email;

  Map<String, dynamic> toJson() => {
    'id': id,
    'first_name': firstName,
    'last_name': lastName,
    'email': email,
  };
}

class RefundCaseType {
  RefundCaseType({
    this.id,
    this.caseType,
    this.description,
    this.condition,
  });

  factory RefundCaseType.fromJson(Map<String, dynamic> json) => RefundCaseType(
    id: json['id'] as int?,
    caseType: json['case'] as String?,
    description: json['description'] as String?,
    condition: json['condition'] as String?,
  );

  final int? id;
  final String? caseType;
  final String? description;
  final String? condition;

  Map<String, dynamic> toJson() => {
    'id': id,
    'case': caseType,
    'description': description,
    'condition': condition,
  };
}

class User {
  User({
    this.firstName,
    this.lastName,
    this.email,
  });

  factory User.fromJson(Map<String, dynamic> json) => User(
    firstName: json['first_name'] as String?,
    lastName: json['last_name'] as String?,
    email: json['email'] as String?,
  );

  final String? firstName;
  final String? lastName;
  final String? email;

  Map<String, dynamic> toJson() => {
    'first_name': firstName,
    'last_name': lastName,
    'email': email,
  };
}
