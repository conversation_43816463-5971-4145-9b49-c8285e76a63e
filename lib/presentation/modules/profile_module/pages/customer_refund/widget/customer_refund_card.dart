import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/customer_refund/model/customer_refund_model.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/customer_refund/widget/title_Info_row.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/logger.dart';

class CustomerRefundCard extends StatelessWidget {
  const CustomerRefundCard({super.key, required this.refund});

  final RefundRequest refund;

  @override
  Widget build(BuildContext context) {
    final refundCase = refund.refundCaseType?.isNotEmpty == true ? refund.refundCaseType!.first : null;
    final status = refund.status ?? '';
    // Map status → theme-aware color
    Color statusColor;
    switch (status) {
      case 'SUCCEEDED':
        statusColor = AppColors.successColor; // success = primary
      case 'PENDING':
        statusColor = AppColors.warningColor; // pending = tertiary (or amber)
      case 'FAILED':
        statusColor = AppColors.red;
      default:
        statusColor = context.theme.disabledColor;
    }
    statusColor.logD;
    return Container(
      margin: EdgeInsets.only(bottom: AppSize.h10),
      padding: EdgeInsets.all(AppSize.sp16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppSize.r8),
        boxShadow: [
          BoxShadow(color: AppColors.black.withValues(alpha: 0.05), blurRadius: 4, offset: const Offset(0, 2)),
        ],
      ),
      child: Column(
        spacing: AppSize.h10,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                refund.amount!.toString().smartFormat(),
                style: context.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: context.theme.primaryColor,
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppSize.r12),
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: AppSize.w10, vertical: AppSize.h2),
                  child: Text(
                    status,
                    style: context.theme.textTheme.labelSmall?.copyWith(
                      color: statusColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ),

          TitleInfoRow(title: context.l10n.name, subTitle: refund.booking?.firstName ?? '-'),
          if (refund.cancellationReason != null)
            TitleInfoRow(title: context.l10n.reason, subTitle: refund.cancellationReason.toString()),

          TitleInfoRow(title: context.l10n.refundId, subTitle: refund.refundRequestId.toString(),textStyle:  context.textTheme.bodyMedium?.copyWith(color: Colors.black,fontSize: AppSize.sp10),subTextStyle: context.textTheme.bodyMedium?.copyWith(color: Colors.black,fontSize: AppSize.sp10),),

          /// Refund Case
          if (refundCase != null)
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Icon(Icons.info, size: 18, color: AppColors.primaryColor),
                Gap(AppSize.w6),
                Expanded(child: Text(refundCase.description ?? '', style: context.textTheme.bodySmall)),
              ],
            ),

          /// Description
        ],
      ),
    );
  }
}
