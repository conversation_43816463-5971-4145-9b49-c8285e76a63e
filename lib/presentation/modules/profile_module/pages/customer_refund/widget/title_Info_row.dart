import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/utils/app_size.dart';

class TitleInfoRow extends StatelessWidget {
  const TitleInfoRow({super.key, required this.title, required this.subTitle, this.subTextStyle, this.textStyle});

  final String title;
  final String subTitle;
  final TextStyle? subTextStyle;
  final TextStyle? textStyle;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$title: ',
          style: textStyle ?? context.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w400, fontSize: AppSize.sp14),
        ),
        Flexible(
          child: Text(
            subTitle,
            style:
                subTextStyle ??
                context.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600, fontSize: AppSize.sp14),
          ),
        ),
      ],
    );
  }
}
