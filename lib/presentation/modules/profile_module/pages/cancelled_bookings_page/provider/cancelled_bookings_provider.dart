import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/shared/repositories/trip_repo.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/logger.dart';

class CancelledBookingsProvider extends ChangeNotifier {
  CancelledBookingsProvider() {
     getCancelledBookingsList(isWantShowLoader: true);

  }

  /// Flag to check if provider is closed
  bool _isClosed = false;

  /// Notify listeners if not closed
  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'notify error: $e'.logE;
    }
  }

  /// Trip repository instance
  final tripRepo = Injector.instance<TripRepository>();

  /// Cancelled bookings list
  final cancelledBookingsList = ValueNotifier<List<TripModel>>([]);

  final refreshController = EasyRefreshController();

  /// Loading state
  final isLoading = ValueNotifier<bool>(false);

  final cancelledTripList = ValueNotifier<List<TripModel>>([]);

  /// Cancel token for API requests
  CancelToken? cancelledBookingsToken;

  /// Next page URL for pagination
  final nextPageUrl = ValueNotifier<String?>(null);

  /// Get cancelled bookings list
  /// [isPagination] flag for pagination
  /// [isWantShowLoader] flag to show loader
  Future<void> getCancelledBookingsList({
    bool isPagination = false,
    bool isWantShowLoader = false,
  }) async {
    if (_isClosed) return;

    try {
      // Cancel previous request
      cancelledBookingsToken?.cancel();
      cancelledBookingsToken = CancelToken();

      // Reset next page URL if not pagination
      if (!isPagination) nextPageUrl.value = null;

      // Show loader if requested
      if (isWantShowLoader) isLoading.value = true;
      'loading value ${isLoading.value}'.logD;

      // Prepare API request
      final url = isPagination && nextPageUrl.value != null
          ? nextPageUrl.value!
          : EndPoints.getCancelledBookings;

      final request = ApiRequest(
        path: url,
        cancelToken: cancelledBookingsToken,
      );

      // Make API call
      final response = await tripRepo.getCancelledBookingsList(request);

      if (_isClosed || (cancelledBookingsToken?.isCancelled ?? true)) return;

      // Handle response
      response.when(
        success: (data) {
          if (_isClosed || (cancelledBookingsToken?.isCancelled ?? true)) {
            return;
          }

          isLoading.value = false;

          // Get current list
          // cancelledTripList.value = [...cancelledBookingsList.value];

          // Clear list if not pagination
          if (!isPagination)  cancelledTripList.value.clear();

          // Add new data
          cancelledTripList.value.addAll(data.results ?? []);

          // Update list and next page URL
          cancelledBookingsList.value = cancelledTripList.value;
          nextPageUrl.value = data.next;
          notify();
        },
        error: (error) {
          if (_isClosed || (cancelledBookingsToken?.isCancelled ?? true)) {
            return;
          }

          isLoading.value = false;
          error.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (cancelledBookingsToken?.isCancelled ?? true)) return;

      isLoading.value = false;
      'getCancelledBookingsList error: $e'.logE;
    }
  }

  /// Load more cancelled bookings (pagination)
  Future<void> loadMoreCancelledBookings() async {
    if (nextPageUrl.value != null && !isLoading.value) {
      await getCancelledBookingsList(isPagination: true);
    }
  }

  /// Refresh cancelled bookings list
  Future<void> refreshCancelledBookings() async {
    await getCancelledBookingsList(isWantShowLoader: true);
  }

  @override
  void dispose() {
    _isClosed = true;

    // Cancel API requests
    cancelledBookingsToken?.cancel();

    // Dispose ValueNotifiers
    cancelledBookingsList.dispose();
    isLoading.dispose();
    nextPageUrl.dispose();

    super.dispose();
  }
}
