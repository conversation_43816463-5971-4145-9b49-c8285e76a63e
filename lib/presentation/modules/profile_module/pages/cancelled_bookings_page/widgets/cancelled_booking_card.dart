import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/location_info_widget.dart';
import 'package:transport_match/widgets/no_of_vehicle_widget.dart';

class CancelledBookingCard extends StatelessWidget {
  const CancelledBookingCard({super.key, required this.tripData});

  final TripModel tripData;

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppSize.r4),
        border: Border.all(color: AppColors.errorColor.withValues(alpha: 0.3)),
      ),
      child: Padding(
        padding: EdgeInsets.all(AppSize.sp16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with vehicle count and cancelled status
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                NoOfVehicleWidget(
                  noOfVehicle: tripData.noOfCars?.toString() ?? '-',
                  isTitleWidget: true,
                ),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppSize.w8,
                    vertical: AppSize.h4,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.errorColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppSize.r4),
                  ),
                  child: Text(
                    context.l10n.cancelBooking,
                    style: context.textTheme.bodySmall?.copyWith(
                      color: AppColors.errorColor,
                      fontWeight: FontWeight.w600,
                      fontSize: AppSize.sp10,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: AppSize.h12),

            // Location information
            LocationInfoWidget(
              startLocationTitle: tripData.userStartLocation?.address ?? '',
              startLocationDate:
                  tripData.customerStartDate?.monthDateFormate ?? '',
              endLocationTitle: tripData.userEndLocation?.address ?? '',
              endLocationDate: tripData.customerEndDate?.monthDateFormate ?? '',
              startLatitude: tripData.userStartLocation?.latitude?.toString(),
              startLongitude: tripData.userStartLocation?.longitude?.toString(),
              endLatitude: tripData.userEndLocation?.latitude?.toString(),
              endLongitude: tripData.userEndLocation?.longitude?.toString(),
            ),
            SizedBox(height: AppSize.h12),

            // Booking type
            Row(
              children: [
                Icon(
                  Icons.category,
                  size: AppSize.h16,
                  color: AppColors.ffADB5BD,
                ),
                SizedBox(width: AppSize.w8),
                Text(
                  tripData.bookingType?.toCapitalized() ?? '',
                  style: context.textTheme.bodySmall?.copyWith(
                    color: AppColors.ffADB5BD,
                    fontSize: AppSize.sp12,
                  ),
                ),
              ],
            ),
            // Total distance if available
            if (tripData.totalTripDistance != null) ...[
              SizedBox(height: AppSize.h8),
              Row(
                children: [
                  Icon(
                    Icons.route,
                    size: AppSize.h16,
                    color: AppColors.ffADB5BD,
                  ),
                  SizedBox(width: AppSize.w8),
                  Text(
                    '${tripData.totalTripDistance?.toStringAsFixed(1).smartFormat()} km',
                    style: context.textTheme.bodySmall?.copyWith(
                      color: AppColors.ffADB5BD,
                      fontSize: AppSize.sp12,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}
