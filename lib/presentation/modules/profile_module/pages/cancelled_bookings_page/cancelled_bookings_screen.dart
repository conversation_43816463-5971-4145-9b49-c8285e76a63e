
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/pages/upcoming_trip_page/widgets/upcoming_common_card.dart';
import 'package:transport_match/presentation/modules/my_trips_module/widgets/no_trip_widget.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/cancelled_bookings_page/provider/cancelled_bookings_provider.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

class CancelledBookingsScreen extends StatelessWidget {
  const CancelledBookingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => CancelledBookingsProvider(),
      child: Consumer<CancelledBookingsProvider>(
        builder: (context, provider, child) {
          return Scaffold(
            backgroundColor: AppColors.pageBGColor,
            appBar: CustomAppBar(title: context.l10n.cancelled),
            body: Selector<CancelledBookingsProvider, (bool, List<TripModel>, String?)>(
              selector: (p0, p1) =>
              (p1.isLoading.value, p1.cancelledBookingsList.value, p1.nextPageUrl.value),
              builder: (context, value, child) {
                final isLoading = value.$1;
                final list = value.$2;
                final nextUrl = value.$3;

                return AppLoader(
                  isShowLoader: isLoading,
                  child: EasyRefresh(
                    controller: provider.refreshController,
                    header: AppCommonFunctions.getLoadingHeader(),
                    footer: AppCommonFunctions.getLoadingFooter(),
                    onRefresh: provider.refreshCancelledBookings,
                    onLoad: nextUrl.isNotEmptyAndNotNull ? provider.loadMoreCancelledBookings : null,
                    child: !isLoading && list.isEmpty
                        ? ListView(
                      children: [
                        SizedBox(
                          height: MediaQuery.of(context).size.height * 0.85,
                          child: const Center(
                            child: NoTripWidget(),
                          ),
                        ),
                      ],
                    )
                        : ListView.builder(
                      itemCount: list.length,
                      itemBuilder: (context, index) {
                        final data = list[index];
                        return Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: AppSize.h20, vertical: AppSize.h6),
                          child: UpcomingCommonCard(
                            tripData: data,
                            onBack: (note, {isRemove = false}) {
                              provider.cancelledBookingsList.value[index]
                                  .bookingDetails?.firstOrNull?.notes =
                              isRemove ? [] : (note != null ? [note] : []);
                              provider.notify();
                            },
                          ),
                        );
                      },
                    ),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
