import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/auth_module/check_otp_page/models/verify_otp_model.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/edit_profile_page/models/edit_profile_param.dart';
import 'package:transport_match/presentation/modules/profile_module/pages/language_change_sheet/language_change_sheet.dart';
import 'package:transport_match/presentation/modules/profile_module/provider/profiler_provider.dart';
import 'package:transport_match/presentation/modules/profile_module/widgets/custom_tile_widget.dart';
import 'package:transport_match/presentation/modules/profile_module/widgets/separator_widget.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/widgets/app_loader.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return ChangeNotifierProvider(
      create: (context) => ProfilerProvider(),
      child: Builder(
        builder: (context) {
          final profilerProvider = context.read<ProfilerProvider>();
          return ValueListenableBuilder(
            valueListenable: profilerProvider.isShowLoader,
            builder: (context, value, child) =>
                AppLoader(isShowLoader: value, child: child!),
            child: ListView(
              padding: EdgeInsets.all(AppSize.appPadding),
              children: [
                SeparatorWidget(
                  children: [
                    Selector<ProfilerProvider, UserModel?>(
                      selector: (p0, profileProvider) => profileProvider.user,
                      builder: (context, user, child) {
                        return CustomTileWidget(
                          onTap: () =>
                              AppNavigationService.pushNamed<ProfilerProvider>(
                                context,
                                AppRoutes.profileEditScreen,
                                extra: EditProfileParam(
                                  email: user?.email ?? '',
                                  name: user?.firstName ?? '',
                                  onEditCallBack: (userData) {
                                    profilerProvider
                                      ..user = userData
                                      ..notify();
                                  },
                                ),
                              ),
                          customWidget: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                user?.firstName ?? context.l10n.user,
                                style: context.textTheme.titleLarge?.copyWith(
                                  fontSize: AppSize.sp16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                user?.email ?? context.l10n.userEmail,
                                style: context.textTheme.bodyMedium?.copyWith(
                                  color: AppColors.ffADB5BD,
                                  fontSize: AppSize.sp12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                          suffix: AppAssets.iconsEditProfile.image(
                            height: AppSize.h24,
                            width: AppSize.h24,
                          ),
                        );
                      },
                    ),
                    CustomTileWidget(
                      onTap: () => AppNavigationService.pushNamed(
                        context,
                        AppRoutes.profileCustomerSupportScreen,
                      ),
                      name: l10n.customer_support,
                    ),
                    CustomTileWidget(
                      onTap: () => context
                          .read<ProfilerProvider>()
                          .stripeCustomerPortalUrl(context),
                      name: l10n.payments,
                    ),
                    CustomTileWidget(
                      onTap: () => AppNavigationService.pushNamed(
                        context,
                        AppRoutes.profileCustomerDetailScreen,
                        // const CustomerDetailScreen(),
                      ),
                      name: context.l10n.customerDetail,
                    ),  CustomTileWidget(
                      onTap: () => AppNavigationService.pushNamed(
                        context,
                        AppRoutes.profileCustomerRefundScreen,
                      ),
                      name: context.l10n.customerRefundRequest,
                    ),
                    CustomTileWidget(
                      onTap: () => AppNavigationService.pushNamed(
                        context,
                        AppRoutes.profileCancelledBookingsScreen,
                      ),
                      name: context.l10n.cancelledBookings,
                    ),
                    CustomTileWidget(
                      onTap: () => showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        builder: (context) => LanguageChangeSheet(
                          profilerProvider: profilerProvider,
                        ),
                      ),
                      name: l10n.language,
                    ),
                    CustomTileWidget(
                      onTap: () => context.showAlertDialog(
                        title: l10n.signOut,
                        content: l10n.signOutContent,
                        defaultActionText: l10n.signOut,
                        cancelActionText: l10n.cancel,
                        onCancelActionPressed: Navigator.pop,
                        onDefaultActionPressed: (dContext) {
                          Navigator.pop(dContext);
                          context.read<ProfilerProvider>().logout();
                        },
                      ),
                      name: l10n.sign_out,
                      fontColor: AppColors.errorColor,
                      suffix: AppAssets.iconsSignout.image(
                        height: AppSize.h22,
                        width: AppSize.h22,
                      ),
                    ),
                    Align(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        child: InkWell(
                          onTap: () => context.showAlertDialog(
                            title: l10n.deleteAccount,
                            content: l10n.deleteAccountContent,
                            defaultActionText: l10n.delete,
                            cancelActionText: l10n.cancel,
                            onCancelActionPressed: Navigator.pop,
                            onDefaultActionPressed: (dContext) {
                              Navigator.pop(dContext);
                              context.read<ProfilerProvider>().deleteAccount();
                            },
                          ),
                          child: Text(
                            l10n.deleteAccount,
                            style: context.textTheme.bodyMedium?.copyWith(
                              fontSize: AppSize.sp16,
                              fontWeight: FontWeight.w600,
                              color: AppColors.errorColor,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

/* SafeArea(
              child: AppPadding.all(
                padding: AppSize.sp16,
                child: Column(
                  spacing: AppSize.h16,
                  children: [
                    Selector<ProfilerProvider, UserModel?>(
                      selector: (p0, profileProvider) => profileProvider.user,
                      builder: (context, user, child) {
                        return CustomTileWidget(
                          onTap: () =>
                              AppNavigationService.pushNamed<ProfilerProvider>(
                                context,
                                AppRoutes.profileEditScreen,
                                extra: EditProfileParam(
                                  email: user?.email ?? '',
                                  name: user?.firstName ?? '',
                                  onEditCallBack: (userData) {
                                    profilerProvider
                                      ..user = userData
                                      ..notify();
                                  },
                                ),
                              ),
                          customWidget: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                user?.firstName ?? context.l10n.user,
                                style: context.textTheme.titleLarge?.copyWith(
                                  fontSize: AppSize.sp16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                user?.email ?? context.l10n.userEmail,
                                style: context.textTheme.bodyMedium?.copyWith(
                                  color: AppColors.ffADB5BD,
                                  fontSize: AppSize.sp12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                          suffix: AppAssets.iconsEditProfile.image(
                            height: AppSize.h24,
                            width: AppSize.h24,
                          ),
                        );
                      },
                    ),
                    CustomTileWidget(
                      onTap: () => AppNavigationService.pushNamed(
                        context,
                        AppRoutes.profileCustomerSupportScreen,
                      ),
                      name: l10n.customer_support,
                    ),
                    CustomTileWidget(
                      onTap: () => context
                          .read<ProfilerProvider>()
                          .stripeCustomerPortalUrl(context),
                      name: l10n.payments,
                    ),
                    CustomTileWidget(
                      onTap: () => AppNavigationService.pushNamed(
                        context,
                        AppRoutes.profileCustomerDetailScreen,
                        // const CustomerDetailScreen(),
                      ),
                      name: context.l10n.customerDetail,
                    ),
                    CustomTileWidget(
                      onTap: () => showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        builder: (context) => const LanguageChangeSheet(),
                      ),
                      name: l10n.language,
                    ),
                    CustomTileWidget(
                      onTap: () => context.showAlertDialog(
                        title: l10n.signOut,
                        content: l10n.signOutContent,
                        defaultActionText: l10n.signOut,
                        cancelActionText: l10n.cancel,
                        onCancelActionPressed: Navigator.pop,
                        onDefaultActionPressed: (dContext) {
                          Navigator.pop(dContext);
                          context.read<ProfilerProvider>().logout();
                        },
                      ),
                      name: l10n.sign_out,
                      fontColor: AppColors.errorColor,
                      suffix: AppAssets.iconsSignout.image(
                        height: AppSize.h22,
                        width: AppSize.h22,
                      ),
                    ),
                    const Spacer(),
                    InkWell(
                      onTap: () => context.showAlertDialog(
                        title: l10n.deleteAccount,
                        content: l10n.deleteAccountContent,
                        defaultActionText: l10n.delete,
                        cancelActionText: l10n.cancel,
                        onCancelActionPressed: Navigator.pop,
                        onDefaultActionPressed: (dContext) {
                          Navigator.pop(dContext);
                          context.read<ProfilerProvider>().deleteAccount();
                        },
                      ),
                      child: Text(
                        l10n.delete_account,
                        style: context.textTheme.bodyMedium?.copyWith(
                          fontSize: AppSize.sp16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.errorColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),*/
