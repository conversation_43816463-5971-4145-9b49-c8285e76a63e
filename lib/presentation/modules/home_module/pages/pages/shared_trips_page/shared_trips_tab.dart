import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/selected_vehicle_info.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/shared_trips_page/widgets/pickup_delivery_date_widget.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/shared_trips_page/widgets/pickup_location_widget.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/shared_trips_page/widgets/stop_end_location_widget.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/shared_trips_page/widgets/user_location_widget.dart';
import 'package:transport_match/presentation/modules/home_module/pages/widgets/vehicle_selection_list_widget.dart';
import 'package:transport_match/presentation/modules/home_module/provider/home_provider.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_confirm_check_box.dart';

/// Shared trips tab UI
class SharedTripsTab extends StatefulWidget {
  const SharedTripsTab({super.key});

  @override
  State<SharedTripsTab> createState() => _SharedTripsTabState();
}

class _SharedTripsTabState extends State<SharedTripsTab> {
  @override
  void initState() {
    final homeProvider = context.read<HomeProvider>();
    homeProvider.selectedVehicleInfo
      ..clear()
      ..add(SelectedVehicleInfoModel());
    homeProvider
      ..pickupDate.value = null
      ..deliveryDate.value = null;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final homeProvider = context.read<HomeProvider>();
    return Form(
      key: homeProvider.formKeySharedTrip,
      child: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: AppSize.appPadding),
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.only(top: AppSize.h16, bottom: AppSize.h24),
              child: Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: '${context.l10n.sharedTrip}: ',
                      style: context.textTheme.titleMedium?.copyWith(
                        fontSize: AppSize.sp12,
                        color: AppColors.ff6C757D,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    TextSpan(
                      text: context.l10n.transportUrVehicle,
                      style: context.textTheme.titleMedium?.copyWith(
                        fontSize: AppSize.sp12,
                        color: AppColors.ff6C757D,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            /// user start and end location widget
            UserLocationWidget(homeProvider: homeProvider),

            /// start and end stop admin location widget
            const StopEndLocationWidget(),

            /// vehicle selection widget
            const VehicleSelectionListWidget(),

            /// user pickup location fro stop location widget
            const PickupLocationWidget(),

            /// pickup and delivery date picker widget
            const PickupDeliveryDateWidget(),

            /// all in one vehicle check box widget
            Selector<HomeProvider, int>(
              selector: (p0, p1) => p1.selectedVehicleInfo.length,
              builder: (context, selectedVehicleInfo, child) {
                return selectedVehicleInfo > 1
                    ? Padding(
                        padding: EdgeInsets.symmetric(vertical: AppSize.h16),
                        child: AppConfirmCheckBox(
                          description:
                              context.l10n.transportAllVehicleInOneTruck,
                          value: homeProvider.isInAllVehicle.value,
                          onSelectionChanged: ({required bool value}) {
                            homeProvider
                              ..isInAllVehicle.value = value
                              ..notify();
                          },
                        ),
                      )
                    : Gap(AppSize.h16);
              },
            ),

            /// find transporter button widget
            Consumer<HomeProvider>(
              builder: (context, homeProvider, child) {
                return AppButton(
                  isBottomBtn: false,
                  onPressed: () async {
                    if(homeProvider.formKeySharedTrip.currentState?.validate() ?? false) {
                      final isValidRoute = await homeProvider.calculateDistance(
                          pickLat: homeProvider.selectedOriginStockLocation.value!.address!.latitude!,
                          pickLng: homeProvider.selectedOriginStockLocation.value!.address!.longitude!,
                          dropLat: homeProvider.selectedDropStockLocation.value!.address!.latitude!,
                          dropLng: homeProvider.selectedDropStockLocation.value!.address!.longitude!,
                          context: context
                      );
                      if(isValidRoute) {
                        await homeProvider.findTransporter(context, homeProvider);
                      } else {
                        context.l10n.invalidRoute.showErrorAlert();
                      }
                    }
                  },
                  text:
                      homeProvider.selectedVehicleInfo.any(
                        (e) => e.showManualInputs,
                      )
                      ? context.l10n.submitForApproval
                      : context.l10n.findTransporter,
                );
              },
            ),
            Gap(AppSize.h24),
          ],
        ),
      ),
    );
  }
}
