import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/req_model.dart';
import 'package:transport_match/presentation/modules/home_module/provider/home_provider.dart';
import 'package:transport_match/shared/repositories/home_repository.dart';
import 'package:transport_match/shared/rest_api/api_keys.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/logger.dart';

class TransporterListProvider extends ChangeNotifier {
  /// Refresh controller for pull to refresh
  final refresherController = EasyRefreshController();

  /// Home repository instance
  final homeRepository = Injector.instance<HomeRepository>();

  /// Loading state flag
  ValueNotifier<bool> isShowLoader = ValueNotifier(false);

  /// Flag to check if provider is closed
  bool _isClosed = false;

  /// Notify listeners if not closed
  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'notify error: $e'.logE;
    }
  }

  /// Booking session id for temp slot
  String? bookingSessionId;

  /// List of transporter details
  ValueNotifier<List<TransporterDetailModel>> transporterDetailList =
      ValueNotifier([]);

  /// Token for register temp slot API
  CancelToken? registerTempSlotSingleToken;

  /// Register temporary slot for a transporter
  /// [context] is the BuildContext
  /// [element] is the transporter detail model
  /// [isRemove] flag to remove slot
  /// [homeProvider] is the HomeProvider instance
  Future<bool> registerTempSlotSingle(
    BuildContext context,
    TransporterDetailModel element, {
    bool isRemove = false,
    required HomeProvider homeProvider,
  }) async {
    if (_isClosed) return false;

    registerTempSlotSingleToken?.cancel();
    registerTempSlotSingleToken = CancelToken();

    try {
      if (bookingSessionId.isEmptyOrNull) {
        final sessionId = await createBookingSession(context);
        if (sessionId.isEmptyOrNull || _isClosed) return false;
      }
      var isDone = false;
      final dataList = <Map<String, dynamic>>[];

      var size = 0.0;
      for (final c in element.carDetails) {
        if (!isRemove) {
          size += c.carSize?.toDouble() ?? 0;
        }
      }
      final deviceId = await AppCommonFunctions.getDeviceId();
      if (_isClosed) {
        return false;
      }
      if (registerTempSlotSingleToken?.isCancelled ?? true) {
        return false;
      }

      dataList.add({
        ApiKeys.slot: size,
        ApiKeys.trip: element.trip,
        ApiKeys.startStopLocation:
            homeProvider.selectedOriginStockLocation.value?.id,
        ApiKeys.endStopLocation:
            homeProvider.selectedDropStockLocation.value?.id,
        ApiKeys.deviceId: deviceId,
        ApiKeys.bookingSessionId: bookingSessionId,
      });

      isShowLoader.value = true;
      for (final data in dataList) {
        if (_isClosed) return false;

        try {
          final response = await homeRepository.registerTempSlot(
            ApiRequest(
              path: EndPoints.registerTempSlot,
              data: data,
              cancelToken: registerTempSlotSingleToken,
            ),
          );
          response.when(
            success: (data) {
              if (_isClosed) {
                return false;
              }
              if (registerTempSlotSingleToken?.isCancelled ?? true) {
                return false;
              }
              isDone = true;
              final min = data['time'];

              context.l10n
                  .pleaseAssignCarWithin(min.toString())
                  .showInfoAlert(duration: const Duration(seconds: 5));
              notify();
            },
            error: (error) {
              if (_isClosed) {
                return false;
              }
              if (registerTempSlotSingleToken?.isCancelled ?? true) {
                return false;
              }
              bookingSessionId = null;
              error.message.showErrorAlert();
            },
          );
        } catch (e) {
          if (_isClosed) {
            return false;
          }
          if (registerTempSlotSingleToken?.isCancelled ?? true) {
            return false;
          }
          bookingSessionId = null;
          'registerTempSlotSingle error: $e'.logE;
          e.toString().showErrorAlert();
          isDone = false;
          break;
        }
        isShowLoader.value = false;
      }
      return isDone;
    } catch (e) {
      'registerTempSlotSingle outer error: $e'.logE;
      return false;
    }
  }

  /// Token for create booking session API
  CancelToken? createBookingSessionToken;

  /// Create booking session
  /// [context] is the BuildContext
  Future<String?> createBookingSession(BuildContext context) async {
    if (_isClosed) return null;

    createBookingSessionToken?.cancel();
    createBookingSessionToken = CancelToken();
    String? sessionId;

    isShowLoader.value = true;
    try {
      final deviceId = await AppCommonFunctions.getDeviceId();
      if (_isClosed) return null;

      final response = await homeRepository.createBookingSession(
        ApiRequest(
          path: EndPoints.createBookingSession,
          data: {ApiKeys.deviceId: deviceId},
          cancelToken: createBookingSessionToken,
        ),
      );
      response.when(
        success: (data) {
          if (_isClosed) {
            return null;
          }
          if (createBookingSessionToken?.isCancelled ?? true) {
            return null;
          }
          bookingSessionId = data[ApiKeys.sessionId] as String?;
          // AppNavigationService.pushNamed(context, const PaymentPage());
          sessionId = data[ApiKeys.sessionId] as String?;
          notify();
        },
        error: (error) {
          if (_isClosed) {
            return null;
          }
          if (createBookingSessionToken?.isCancelled ?? true) {
            return null;
          }
          bookingSessionId = null;
          error.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed) {
        return null;
      }
      if (createBookingSessionToken?.isCancelled ?? true) {
        return null;
      }
      bookingSessionId = null;
      'createBookingSession error: $e'.logE;
      if (kDebugMode) e.toString().showErrorAlert();
    }
    isShowLoader.value = false;
    return sessionId;
  }

  @override
  void dispose() {
    _isClosed = true;
    isShowLoader.dispose();
    transporterDetailList.dispose();
    registerTempSlotSingleToken?.cancel();
    createBookingSessionToken?.cancel();
    super.dispose();
  }
}
