import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/shared/repositories/account_repository.dart'
    show AccountRepository;
import 'package:transport_match/shared/repositories/home_repository.dart';
import 'package:transport_match/shared/rest_api/api_keys.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/utils/logger.dart';

class UserInfoProvider extends ChangeNotifier {
  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'UserInfoProvider notify error: $e'.logE;
    }
  }

  /// is close variable
  bool _isClosed = false;
  ValueNotifier<bool> isShowLoader = ValueNotifier(false);
  final homeRepository = Injector.instance<HomeRepository>();
  final dropUserName = TextEditingController();
  String dropUserImage = '';
  String dropUserDocType = '';
  final pickUserName = TextEditingController();
  String pickUserImage = '';
  String pickUserDocType = '';

  CancelToken? createBookToken;
  CancelToken? generateImgToken;

  Future<void> createBookingAssignee(
    BuildContext context,
    String bookingId,
  ) async {
    if (_isClosed) return;

    if (dropUserName.text.isEmpty) {
      context.l10n.pleaseEnterDropUserName.showErrorAlert();
    } else if (dropUserDocType.isEmpty) {
      context.l10n.pleaseEnterDropUserDocType.showErrorAlert();
    } else if (dropUserImage.isEmpty) {
      context.l10n.pleaseEnterDropUserDoc.showErrorAlert();
    } else if (pickUserName.text.isEmpty) {
      context.l10n.pleaseEnterPickUserName.showErrorAlert();
    } else if (pickUserDocType.isEmpty) {
      context.l10n.pleaseEnterPickUserDocType.showErrorAlert();
    } else if (pickUserImage.isEmpty) {
      context.l10n.pleaseEnterPickUserDoc.showErrorAlert();
    } else {
      createBookToken?.cancel();
      createBookToken = CancelToken();
      var dataList = <Map<String, dynamic>>[];

      isShowLoader.value = true;
      try {
        if (_isClosed) return;
        final imageResult = await _generateEmptyListImages(
          imgList: [File(dropUserImage), File(pickUserImage)],
        );

        if (_isClosed) return;
        if (imageResult != null) {
          await _uploadFile(
            byteImages: File(dropUserImage).readAsBytesSync(),
            url: imageResult.$1.first,
            mimeType: 'image/jpeg',
          );

          if (_isClosed) return;
          await _uploadFile(
            byteImages: File(pickUserImage).readAsBytesSync(),
            url: imageResult.$1.last,
            mimeType: 'image/jpeg',
          );

          if (imageResult.$1.length == 2) {
            dataList = [
              {
                ApiKeys.name: dropUserName.text,
                ApiKeys.idProofType: dropUserDocType,
                ApiKeys.awsImageKey: imageResult.$2.first,
                ApiKeys.assigneeType: AssigneeType.DROP.name,
              },
              {
                ApiKeys.name: pickUserName.text,
                ApiKeys.idProofType: pickUserDocType,
                ApiKeys.awsImageKey: imageResult.$2.last,
                ApiKeys.assigneeType: AssigneeType.PICKUP.name,
              },
            ];

            if (_isClosed) return;
            final response = await homeRepository.createBookingAssign(
              ApiRequest(
                path: EndPoints.createBookingAssign,
                data: {
                  ApiKeys.booking: bookingId,
                  ApiKeys.assignees: dataList,
                },
                cancelToken: createBookToken,
              ),
            );

            response.when(
              success: (data) {
                if (_isClosed || (createBookToken?.isCancelled ?? true)) return;
                AppNavigationService.pushAndRemoveAllPreviousRoute(
                  context,
                  AppRoutes.homeBase,
                  isBaseRoute: true,
                );
                notifyListeners();
              },
              error: (error) {
                if (_isClosed || (createBookToken?.isCancelled ?? true)) return;
                error.message.showErrorAlert();
              },
            );
          }
        } else {
          '==>>> image list is null'.logFatal;
        }
      } catch (e) {
        if (_isClosed || (createBookToken?.isCancelled ?? true)) return;
        'createBookingAssignee error: $e'.logE;
      }
      isShowLoader.value = false;
    }
  }

  Future<bool> _uploadFile({
    required String url,
    required Uint8List byteImages,
    required String mimeType,
    void Function(double)? onSendProgress,
    CancelToken? cancelToken,
  }) {
    try {
      final baseOptions = BaseOptions(
        connectTimeout: const Duration(minutes: 10),
        sendTimeout: const Duration(minutes: 10),
        receiveTimeout: const Duration(minutes: 10),
        // contentType: mimeType,
        headers: {
          ApiKeys.contentType: '',
        },
      );

      final dio = Dio(baseOptions);
      '====>>>>>> upload image url = $url'.logE;

      return dio.put<Map<String, dynamic>>(
        url,
        data: byteImages,
        cancelToken: cancelToken,
        onSendProgress: (count, total) {
          final progressPercent = count / total;
          onSendProgress?.call(progressPercent);
        },
      ).then(
        (value) {
          '====>>>>>> upload image success $value'.logE;
          return true;
        },
        onError: (Object error) {
          '====>>>>>> upload image null $error'.logE;
          if (error is DioException) {
            if (error.type == DioExceptionType.cancel) {
              return false;
            }
          }
          return false;
        },
      ).onError((error, stackTrace) {
        '====>>>>>> upload image null on error $error'.logE;
        return false;
      });
    } catch (e) {
      '_uploadFile error: $e'.logE;
      return Future.value(false);
    }
  }

  /// to upload image we need to first generate empty list of image link then we have
  /// put image data into that generated link
  Future<(List<String>, List<String>)>? _generateEmptyListImages({
    required List<File> imgList,
  }) async {
    // if (isShowLoader.value) return [];
    generateImgToken?.cancel();
    generateImgToken = CancelToken();
    isShowLoader.value = true;
    final imagesList = <String>[];
    final keysList = <String>[];
    final data = FormData();
    data.fields.addAll([
      const MapEntry(ApiKeys.fileExtension, 'jpg'),
      MapEntry(ApiKeys.folderName, ImageTypes.BOOKED_CAR.name),
      MapEntry(ApiKeys.numberOfUrl, imgList.length.toString()),
    ]);
    final request = ApiRequest(
      path: EndPoints.generateUrl,
      data: data,
      cancelToken: generateImgToken,
    );

    final res =
        await Injector.instance<AccountRepository>().generateUrl(request);

    res.when(
      success: (data) async {
        if (generateImgToken?.isCancelled ?? true) return [];

        imagesList.addAll(
          data.map((e) => e[ApiKeys.putUrl]?.toString() ?? ''),
        );
        keysList.addAll(
          data.map((e) => e[ApiKeys.keyName]?.toString() ?? ''),
        );

        '==>> ===== $imagesList == $keysList'.logE;
      },
      error: (exception) {
        if (generateImgToken?.isCancelled ?? true) return [];
      },
    );
    return (imagesList, keysList);
  }

  @override
  void dispose() {
    _isClosed = true;
    super.dispose();
  }
}
