import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/exclusive_trip_screen/models/stock_data_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/search_address_screen/models/address_search_params.dart';
import 'package:transport_match/presentation/modules/home_module/provider/home_provider.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/utils/validators/global_text_validator.dart';
import 'package:transport_match/widgets/app_textfield.dart';

class PickupLocationWidget extends StatelessWidget {
  const PickupLocationWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final homeProvider = context.read<HomeProvider>();
    return Selector<HomeProvider, bool>(
      selector: (p0, homeProvider) => homeProvider.isPickUpSelected,
      builder: (context, isPickUpSelected, child) {
        return isPickUpSelected
            ? Padding(
                padding: EdgeInsets.only(bottom: AppSize.sp16),
                child: ValueListenableBuilder(
                  valueListenable: homeProvider.pickUpAddress,
                  builder: (context, address, child) {
                    return Column(
                      spacing: AppSize.h16,
                      children: [
                        Row(
                          spacing: AppSize.w8,
                          children: [
                            AppAssets.iconsLocation.image(
                              height: AppSize.h24,
                              width: AppSize.h24,
                              color: AppColors.primaryColor,
                            ),
                            Text(
                              context.l10n.pickupLocation,
                              style: context.textTheme.titleSmall?.copyWith(
                                color: AppColors.primaryColor,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                        AppTextFormField(
                          hintText: context.l10n.enterPickupAddress,
                          controller: TextEditingController(
                            text: address?.address ?? address?.street,
                          ),
                          validator: (p0) => commonValidator(
                            inputValue: p0,
                            errorMessage: context.l10n.enterPickupAddress,
                          ),
                          readOnly: true,
                          onTap: () => AppNavigationService.pushNamed(
                            context,
                            AppRoutes.homeAddressSearchScreen,
                            extra: AddressSearchParams(
                              hintText: context.l10n.enterPickupAddress,
                              homeProvider: homeProvider,
                              isSearchStockLocation: false,
                              isOther: true,
                              isBack: false,
                              onTap: (address) {
                                  homeProvider
                                  ..pickUpAddress.value = AddressModel(
                                    address: address.address,
                                    latitude: address.latitude,
                                    longitude: address.longitude,
                                    postalCode: address.postalCode,
                                    city: address.city,
                                    state: address.state,
                                    country: address.country,
                                    countryCode: address.countryCode,
                                    street: address.street,
                                  )
                                  ..notify();
                                // homeProvider
                                //     .calculateDistance(
                                //       lat: address.latitude ?? '',
                                //       long: address.longitude ?? '',
                                //       context: context,
                                //     )
                                //     .then((value) {
                                //       if (value) {
                                //         homeProvider
                                //           ..pickUpAddress.value = AddressModel(
                                //             address: address.address,
                                //             latitude: address.latitude,
                                //             longitude: address.longitude,
                                //             postalCode: address.postalCode,
                                //             city: address.city,
                                //             state: address.state,
                                //             country: address.country,
                                //             countryCode: address.countryCode,
                                //             street: address.street,
                                //           )
                                //           ..notify();
                                //       }
                                //     });
                              },
                            ),

                            //  AddressSearchScreen(
                            //   homeProvider: homeProvider,
                            //   isSearchStockLocation: false,
                            //   hintText: context.l10n.enterPickupAddress,
                            //   isOther: true,
                            //   isBack: false,
                            //   onTap: (address) {
                            //     homeProvider
                            //         .calculateDistance(
                            //       lat: address.latitude ?? '',
                            //       long: address.longitude ?? '',
                            //       context: context,
                            //     )
                            //         .then((value) {
                            //       if (value) {
                            //         homeProvider
                            //           ..pickUpAddress.value = AddressModel(
                            //             address: address.address,
                            //             latitude: address.latitude,
                            //             longitude: address.longitude,
                            //             postalCode: address.postalCode,
                            //             city: address.city,
                            //             state: address.state,
                            //             country: address.country,
                            //             countryCode: address.countryCode,
                            //             street: address.street,
                            //           )
                            //           ..notify();
                            //       }
                            //     });
                            //   },
                            // ),
                          ),
                        ),
                        AppTextFormField(
                          hintText: context.l10n.enterZipCode,
                          readOnly: true,
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                          ],
                          validator: (p0) => commonValidator(
                            inputValue: p0,
                            errorMessage: context.l10n.enterZipCode,
                          ),
                          controller: TextEditingController(
                            text: address?.postalCode,
                          ),
                        ),
                      ],
                    );
                  },
                ),
              )
            : const SizedBox();
      },
    );
  }
}
