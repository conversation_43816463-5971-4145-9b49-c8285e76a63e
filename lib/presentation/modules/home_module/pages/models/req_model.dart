import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/presentation/modules/booking_module/models/requested_booking_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/exclusive_trip_screen/models/stock_data_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';

class ProviderData {
  const ProviderData({
    required this.customerStartDate,
    required this.customerEndDate,
    required this.allVehicleInOneTrip,
    required this.startStopLocation,
    required this.endStopLocation,
    required this.transporterDetails,
    this.deviceId,
    this.booking,
    this.oldId,
    this.bookingSessionId,
    this.bookingNumber,
    this.bookingCountryCode,
  });

  factory ProviderData.fromJson(Map<String, dynamic> json) => ProviderData(
    customerStartDate: json['customer_start_date'] == null
        ? DateTime.now()
        : DateTime.parse(json['customer_start_date'] as String),
    customerEndDate: json['customer_end_date'] == null
        ? DateTime.now()
        : DateTime.parse(json['customer_end_date'] as String),
    allVehicleInOneTrip: json['all_vehicle_in_one_trip'] as bool? ?? false,
    startStopLocation: json['start_stop_location'] as int?,
    endStopLocation: json['end_stop_location'] as int?,
    deviceId: json['device_id'] as String?,
    booking: json['booking'] as String?,
    oldId: json['old_booking'] as int?,
    bookingNumber: json['booking_contact_number'] as String?,
    bookingCountryCode: json['booking_contact_number_country_code'] as String?,
    bookingSessionId: json['booking_session_id'] as String?,
    transporterDetails: (json['transporter_details'] as List<dynamic>)
        .map(
          (e) => ReqTransporterDetailModel.fromJson(e as Map<String, dynamic>),
        )
        .toList(),
  );

  final DateTime customerStartDate;
  final DateTime customerEndDate;
  final bool allVehicleInOneTrip;
  final String? deviceId;
  final String? booking;
  final String? bookingSessionId;
  final int? startStopLocation;
  final int? endStopLocation;
  final int? oldId;
  final List<ReqTransporterDetailModel> transporterDetails;
  final String? bookingNumber;
  final String? bookingCountryCode;

  Map<String, dynamic> toJson() => {
    'customer_start_date': customerStartDate.toUtc().passDateFormateWithTime,
    'customer_end_date': customerEndDate.toUtc().passDateFormateWithTime,
    if (allVehicleInOneTrip) 'all_vehicle_in_one_trip': allVehicleInOneTrip,
    if (startStopLocation != null) 'start_stop_location': startStopLocation,
    if (endStopLocation != null) 'end_stop_location': endStopLocation,
    if (deviceId != null) 'device_id': deviceId,
    if (booking != null) 'booking': booking,
    if (oldId != null) 'old_booking': oldId,
    if (bookingNumber.isNotEmptyAndNotNull)
      'booking_contact_number': bookingNumber,
    if (bookingCountryCode.isNotEmptyAndNotNull)
      'booking_contact_number_country_code': bookingCountryCode,
    if (bookingSessionId != null) 'booking_session_id': bookingSessionId,
    'transporter_details': List<dynamic>.from(
      transporterDetails.map((x) => x.toJson()),
    ),
  };
}

class TransporterDetailModel {
  const TransporterDetailModel({
    required this.trip,
    required this.carDetails,
    required this.providerPickUpLocation,
  });
  factory TransporterDetailModel.fromJson(Map<String, dynamic> json) =>
      TransporterDetailModel(
        trip: json['trip'] as int,
        carDetails: (json['car_details'] as List<dynamic>)
            .map((e) => CarDetailModel.fromJson(e as Map<String, dynamic>))
            .toList(),
        providerPickUpLocation: DateTime.parse(
          json['provider_pick_up_location'] as String,
        ),
      );

  final int trip;
  final List<CarDetailModel> carDetails;
  final DateTime providerPickUpLocation;

  Map<String, dynamic> toJson() => {
    'trip': trip,
    'car_details': List<dynamic>.from(carDetails.map((x) => x.toJson())),
  };
}

class CarDetailModel {
  CarDetailModel({
    this.id, // ✅ added
    required this.serialNumber,
    required this.car,
    required this.carName,
    required this.brand,
    required this.year,
    required this.carSize,
    this.isCarPickedUp = false,
    this.pickUpServiceAndDropOffService,
    this.fromCarToBePickedUpLocation,
    this.totalDistanceFromCustomerLocationToStopLocation,
    this.isWinchRequired = false,
    this.carDescription,
    this.isInsuranceIncluded = false,
    this.isVerification = false,
    this.dropOffDate,
    this.selectedIndex,
    this.insurance,
    this.yearValue,
    this.brandName,
    this.charges,
    this.serviceType,
    this.awsImageKeys,
    this.fileImage,
    this.images,
    this.isBookedCarCancellable = false,
  });

  factory CarDetailModel.fromJson(Map<String, dynamic> json) => CarDetailModel(
    id: json['id'] as int?, // ✅ from JSON
    serialNumber: json['serial_number'] as String?,
    car: json['car'] as int?,
    selectedIndex: json['selected_index'] as int?,
    carName: json['model'] as String?,
    year: json['year'] as String?,
    yearValue: json['year_value'] as String?,
    brand: json['brand'] as String?,
    brandName: json['brand_name'] as String?,
    carSize: json['size'] as num?,
    isCarPickedUp: json['is_car_picked_up_to_stop_location'] as bool? ?? false,
    pickUpServiceAndDropOffService:
    json['pick_up_service_and_drop_off_service'] as int?,
    fromCarToBePickedUpLocation:
    json['from_car_to_be_picked_up_location'] == null
        ? null
        : AddressModel.fromJson(
      json['from_car_to_be_picked_up_location']
      as Map<String, dynamic>,
    ),
    totalDistanceFromCustomerLocationToStopLocation:
    json['distance_from_customer_location_to_stop_location'] as double?,
    isWinchRequired: json['is_winch_required'] as bool? ?? false,
    carDescription: json['car_description'] as String?,
    insurance: json['insurance'] as String?,
    isInsuranceIncluded: json['is_insured'] as bool? ?? false,
    isVerification:
    json['is_car_verification_required'] as bool? ?? false,
    dropOffDate: json['customer_drop_date_at_stop_location'] == null
        ? null
        : DateTime.parse(
        json['customer_drop_date_at_stop_location'] as String),
    charges: (json['charges'] as List<dynamic>?)
        ?.map((e) => ChargesModel.fromJson(e as Map<String, dynamic>))
        .toList(),
    serviceType: json['service_type'] == null
        ? null
        : ServiceTypeModel.fromJson(
      json['service_type'] as Map<String, dynamic>,
    ),
    images: json['images'] == null
        ? null
        : (json['images'] as List)
        .map((e) => CarImageModel.fromJson(e as Map<String, dynamic>))
        .toList(),
    isBookedCarCancellable:
    json['is_booked_car_cancellable'] as bool? ?? false,
  );

  final int? id; // ✅ added
  final String? serialNumber;
  final int? car;
  int? selectedIndex;
  final String? carName;
  final String? year;
  final String? yearValue;
  final String? brand;
  final String? brandName;
  final num? carSize;
  final bool isCarPickedUp;
  final int? pickUpServiceAndDropOffService;
  final ServiceTypeModel? serviceType;
  final AddressModel? fromCarToBePickedUpLocation;
  final double? totalDistanceFromCustomerLocationToStopLocation;
  final bool isWinchRequired;
  final String? carDescription;
  DateTime? dropOffDate;
  bool isInsuranceIncluded;
  String? insurance;
  final bool isVerification;
  final List<ChargesModel>? charges;
  List<String>? fileImage;
  List<String>? awsImageKeys;
  List<CarImageModel>? images;
  final bool isBookedCarCancellable;

  Map<String, dynamic> toJson() => {
    if (id != null) 'id': id, // ✅ in toJson
    if (serialNumber != null) 'serial_number': serialNumber,
    if (car != null) 'car': car,
    if (car == null && year != null) 'year': year,
    if (car == null && brand != null) 'brand': brand,
    if (car == null && carName != null) 'model': carName,
    if (car == null && carSize != null) 'size': carSize,
    if (dropOffDate != null)
      'customer_drop_date_at_stop_location':
      dropOffDate?.toUtc().passDateFormateWithTime,
    if (isCarPickedUp) 'is_car_picked_up_to_stop_location': isCarPickedUp,
    if (isCarPickedUp)
      'pick_up_service_and_drop_off_service':
      pickUpServiceAndDropOffService,
    if (fromCarToBePickedUpLocation != null && isCarPickedUp)
      'from_car_to_be_picked_up_location':
      fromCarToBePickedUpLocation?.toJson(),
    if (isCarPickedUp &&
        totalDistanceFromCustomerLocationToStopLocation != 0)
      'distance_from_customer_location_to_stop_location':
      totalDistanceFromCustomerLocationToStopLocation,
    if (isWinchRequired) 'is_winch_required': isWinchRequired,
    if (carDescription != null) 'car_description': carDescription,
    if (insurance != null && insurance!.isNotEmpty) 'insurance': insurance,
    if (isInsuranceIncluded) 'is_insured': isInsuranceIncluded,
    if (isVerification) 'is_car_verification_required': isVerification,
    if (charges?.isNotEmpty ?? false)
      'charges': charges?.map((e) => e.toJson()),
    if (awsImageKeys?.isNotEmpty ?? false)
      'aws_image_keys': awsImageKeys?.map((e) => e),
    if (images?.isNotEmpty ?? false)
      'images': images?.map((e) => e.toJson()),
    'is_booked_car_cancellable': isBookedCarCancellable,
  };
}


class ExclusiveReqModel {
  const ExclusiveReqModel({
    this.userStartLocation,
    this.userEndLocation,
    required this.customerStartDate,
    required this.customerEndDate,
    required this.allVehicleInOneTrip,
    required this.bookingType,
    required this.carDetails,
    this.startStopLocation,
    this.endStopLocation,
    this.deviceId,
    this.oldId,
  });

  factory ExclusiveReqModel.fromJson(Map<String, dynamic> json) =>
      ExclusiveReqModel(
        userStartLocation: json['user_start_location'] == null
            ? null
            : AddressModel.fromJson(
                json['user_start_location'] as Map<String, dynamic>,
              ),
        userEndLocation: json['user_end_location'] == null
            ? null
            : AddressModel.fromJson(
                json['user_end_location'] as Map<String, dynamic>,
              ),
        customerStartDate: DateTime.parse(
          json['customer_start_date'] as String,
        ),
        customerEndDate: DateTime.parse(json['customer_end_date'] as String),
        allVehicleInOneTrip: json['all_vehicle_in_one_trip'] as bool,
        startStopLocation: json['start_stop_location'] as String?,
        endStopLocation: json['end_stop_location'] as String?,
        bookingType: json['booking_type'] as String,
        deviceId: json['device_id'] as String?,
        oldId: json['old_booking'] as int?,
        carDetails: (json['car_details'] as List<dynamic>)
            .map((e) => CarDetailModel.fromJson(e as Map<String, dynamic>))
            .toList(),
      );

  final AddressModel? userStartLocation;
  final AddressModel? userEndLocation;
  final DateTime customerStartDate;
  final DateTime customerEndDate;
  final bool allVehicleInOneTrip;
  final String bookingType;
  final String? startStopLocation;
  final String? endStopLocation;
  final String? deviceId;
  final int? oldId;
  final List<CarDetailModel> carDetails;

  Map<String, dynamic> toJson() => {
    if (userStartLocation != null)
      'user_start_location': userStartLocation?.toJson(),
    if (userEndLocation != null) 'user_end_location': userEndLocation?.toJson(),
    'customer_start_date': customerStartDate.toUtc().toIso8601String(),
    'customer_end_date': customerEndDate.toUtc().toIso8601String(),
    if (oldId != null) 'old_booking': oldId,
    if (allVehicleInOneTrip) 'all_vehicle_in_one_trip': allVehicleInOneTrip,
    if (startStopLocation != null) 'start_stop_location': startStopLocation,
    if (endStopLocation != null) 'end_stop_location': endStopLocation,
    'booking_type': bookingType,
    if (deviceId != null) 'device_id': deviceId,
    'car_details': List<dynamic>.from(carDetails.map((x) => x.toJson())),
  };
}

class ServiceTypeModel {
  const ServiceTypeModel({
    required this.id,
    required this.serviceType,
    required this.serviceFee,
    required this.minimumFee,
  });

  factory ServiceTypeModel.fromJson(Map<String, dynamic> json) =>
      ServiceTypeModel(
        id: json['id'] as int?,
        serviceType: json['service_type'] as String?,
        serviceFee: json['service_fee'] as num?,
        minimumFee: json['minimum_fee'] as num?,
      );
  final int? id;
  final String? serviceType;
  final num? serviceFee;
  final num? minimumFee;

  Map<String, dynamic> toJson() => {
    'id': id,
    'service_type': serviceType,
    'service_fee': serviceFee,
    'minimum_fee': minimumFee,
  };
}

class CarImageModel {
  const CarImageModel({required this.id, required this.imageUrl});

  factory CarImageModel.fromJson(Map<String, dynamic> json) => CarImageModel(
    id: json['id'] as int?,
    imageUrl: json['image_url'] as String?,
  );
  final int? id;
  final String? imageUrl;

  Map<String, dynamic> toJson() => {'id': id, 'image_url': imageUrl};
}
