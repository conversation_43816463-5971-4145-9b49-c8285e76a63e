import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/common_pages/car_info_page/car_info_screen.dart';
import 'package:transport_match/presentation/common_pages/car_info_page/models/car_info_params.dart';
import 'package:transport_match/presentation/modules/booking_module/provider/booking_provider.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/req_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/exclusive_trip_screen/models/insurance_model.dart';
import 'package:transport_match/presentation/modules/home_module/provider/home_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/provider/requested_trips_provider.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/widgets/app_confirm_check_box.dart';
import 'package:transport_match/widgets/app_dropdown.dart';
import 'package:transport_match/widgets/app_textfield.dart';
import 'package:transport_match/widgets/keep_alive_wrapper.dart';
import 'package:transport_match/widgets/transport_details_card_widget.dart';
import 'package:transport_match/widgets/vehicle_image_selection_widget.dart';

/// Stock screen vehicle information
class StockVehiclesInfoWidget extends StatefulWidget {
  /// Constructor
  const StockVehiclesInfoWidget({
    super.key,
    this.onClose,
    required this.providerData,
    this.isFirstScreen = false,
    this.isTrip = false,
    required this.mainIndex,
    required this.bookingProvider,
    required this.homeProvider,
  });

  /// On close button tap (optional, button will only show up if callback is not null)
  final VoidCallback? onClose;
  final TransporterDetailModel providerData;
  final bool isFirstScreen;
  final bool isTrip;
  final int mainIndex;
  final BookingProvider bookingProvider;
  final HomeProvider homeProvider;

  @override
  State<StockVehiclesInfoWidget> createState() =>
      _StockVehiclesInfoWidgetState();
}

class _StockVehiclesInfoWidgetState extends State<StockVehiclesInfoWidget> {
  late PageController _pageController;
  bool isOpen = false;
  final controllerList = <TextEditingController>[];
  final insuranceDropdownControllers = <SingleValueDropDownController>[];

  @override
  void initState() {
    _pageController = PageController();
    for (var i = 0; i < widget.providerData.carDetails.length; i++) {
      controllerList.add(TextEditingController());
      insuranceDropdownControllers.add(SingleValueDropDownController());
    }

    super.initState();
  }

  @override
  void dispose() {
    _pageController.dispose();
    for (final controller in controllerList) {
      controller.dispose();
    }
    for (final controller in insuranceDropdownControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final isTrip = widget.isTrip;
    final requestedTripsProvider = context.read<RequestedTripsProvider?>();
    return ChangeNotifierProvider.value(
      value: widget.bookingProvider,
      builder: (context, child) {
        return Column(
          children: [
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(AppSize.r10)),
                color: AppColors.white,
              ),
              padding: EdgeInsets.all(AppSize.h12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  TransportDetailsCard(
                    homeController: widget.homeProvider,
                    isTrip: isTrip,
                    isFirstScreen: widget.isFirstScreen,
                    providerData: isTrip
                        ? requestedTripsProvider!.selectedProvider!
                        : widget.homeProvider.providerList.value.firstWhere(
                            (e) => e.id == widget.providerData.trip,
                          ),
                    totalCar: widget.providerData.carDetails.length,
                    isOpen: isOpen,
                    onOpen: () {
                      setState(() {
                        isOpen = !isOpen;
                      });
                    },
                    homeProvider: widget.homeProvider,
                  ),
                  if (widget.isFirstScreen || isOpen) ...[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          context.l10n.vehicleInfo,
                          style: context.textTheme.bodyLarge?.copyWith(
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        AnimatedBuilder(
                          animation: _pageController,
                          builder: (context, child) {
                            return Text(
                              '${(_pageController.positions.isEmpty ? 0 : _pageController.page?.round() ?? 0) + 1}'
                              '/${widget.providerData.carDetails.length}',
                              style: context.textTheme.bodyLarge?.copyWith(
                                fontSize: AppSize.sp16,
                                fontWeight: FontWeight.w400,
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                    Gap(AppSize.h8),
                    Builder(
                      builder: (context) {
                        final secondScreenHeight =
                            widget.providerData.carDetails.any(
                              (e) => e.isInsuranceIncluded,
                            )
                            ? 215.h
                            : 145.h;
                        return SizedBox(
                          height: widget.isFirstScreen
                              ? 450.h
                              : widget
                                    .bookingProvider
                                    .transporterDetailList
                                    .value[widget.mainIndex]
                                    .carDetails
                                    .any((e) => e.fileImage?.isNotEmpty ?? false)
                              ? secondScreenHeight + 85.h
                              : secondScreenHeight,
                          child: PageView.builder(
                            controller: _pageController,
                            itemCount: widget.providerData.carDetails.length,
                            itemBuilder: (context, index) {
                              final data = widget.providerData.carDetails[index];
            
                              return KeepAliveWrapper(
                                child: SingleChildScrollView(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        padding: EdgeInsets.all(AppSize.sp12),
                                        margin: EdgeInsets.symmetric(
                                          horizontal: AppSize.w4,
                                        ),
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                            color: AppColors.ffADB5BD,
                                          ),
                                          borderRadius: BorderRadius.circular(
                                            AppSize.r5,
                                          ),
                                        ),
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            if (data.brandName != null &&
                                                data.carName != null)
                                              Padding(
                                                padding: EdgeInsets.only(
                                                  bottom: AppSize.h10,
                                                ),
                                                child: Row(
                                                  spacing: AppSize.w10,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.spaceBetween,
                                                  children: [
                                                    if (data.brandName != null)
                                                      VehiclesInfoField(
                                                        title: context.l10n.carBrand,
                                                        value: data.brandName ?? '',
                                                      ),
                                                    GestureDetector(
                                                      onTap: () =>
                                                          AppNavigationService.pushNamed(
                                                            context,
                                                            AppRoutes.carInfoSheet,
                                                            extra: CarInfoParams(
                                                              carDetail: data,
                                                            ),
                                                          ),
                                                      child: Text(
                                                        context.l10n.details,
                                                        style: context
                                                            .textTheme
                                                            .bodyLarge
                                                            ?.copyWith(
                                                              fontSize: AppSize.sp16,
                                                              color: AppColors
                                                                  .primaryColor,
                                                              fontWeight:
                                                                  FontWeight.w600,
                                                            ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.spaceBetween,
                                              spacing: AppSize.w10,
                                              children: [
                                                if (data.carName != null)
                                                  VehiclesInfoField(
                                                    title: context.l10n.carModel,
                                                    value: data.carName ?? '',
                                                  ),
                                                if (data.serialNumber != null)
                                                  VehiclesInfoField(
                                                    title: context.l10n.carSerial,
                                                    value: data.serialNumber ?? '',
                                                    // isLast: true,
                                                  ),
                                              ],
                                            ),
                                            Gap(AppSize.sp12),
                                            Row(
                                              spacing: AppSize.w10,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.spaceBetween,
                                              children: [
                                                if (data.carSize != null)
                                                  VehiclesInfoField(
                                                    title: context.l10n.carSize,
                                                    value: switch (data.carSize) {
                                                      1 => l10n.small,
                                                      1.5 => l10n.medium,
                                                      _ => l10n.large,
                                                    },
                                                  ),
                                                if (!widget.isFirstScreen &&
                                                    data.dropOffDate != null)
                                                  VehiclesInfoField(
                                                    title: context.l10n.dropOffDate,
                                                    value:
                                                        data.dropOffDate!.dateFormate,
                                                    // isLast: true,
                                                  ),
                                              ],
                                            ),
                                            if (!widget.isFirstScreen &&
                                                data.isInsuranceIncluded)
                                              Gap(AppSize.sp12),
                                            if (!widget.isFirstScreen &&
                                                data.isInsuranceIncluded)
                                              Row(
                                                children: [
                                                  Builder(
                                                    builder: (context) {
                                                      final insurance = widget
                                                          .bookingProvider
                                                          .insuranceList
                                                          .value
                                                          .firstWhere(
                                                            (element) =>
                                                                element.id
                                                                    .toString() ==
                                                                data.insurance,
                                                            orElse:
                                                                InsuranceModel.new,
                                                          );
                                                      return VehiclesInfoField(
                                                        title: context.l10n.insurance,
                                                        value:
                                                            '${insurance.name} (${insurance.amount?.toString().smartFormat()})',
                                                      );
                                                    },
                                                  ),
                                                ],
                                              ),
                                            if (widget.isFirstScreen)
                                              Padding(
                                                padding: EdgeInsets.only(
                                                  top: AppSize.sp10,
                                                  bottom: AppSize.sp5,
                                                ),
                                                child: Text(
                                                  data.isCarPickedUp
                                                      ? context
                                                            .l10n
                                                            .selectDateForPickupCar
                                                      : context
                                                            .l10n
                                                            .selectVehicleDrop,
                                                  style: context.textTheme.bodySmall
                                                      ?.copyWith(
                                                        fontSize: AppSize.sp11,
                                                        color: AppColors.ff6C757D,
                                                      ),
                                                ),
                                              ),
                                            if (widget.isFirstScreen)
                                              AppTextFormField(
                                                readOnly: true,
                                                controller: TextEditingController(
                                                  text: data.dropOffDate?.dateFormate,
                                                ),
                                                onTap: () {
                                                  final providerStartDate = widget
                                                      .bookingProvider
                                                      .transporterDetailList
                                                      .value[widget.mainIndex]
                                                      .providerPickUpLocation;
                                                  showDatePicker(
                                                    context: context,
                                                    initialDate: DateTime.now(),
                                                    firstDate: DateTime.now(),
                                                    lastDate: providerStartDate
                                                        .subtract(
                                                          const Duration(days: 1),
                                                        ),
                                                  ).then((value) {
                                                    if (value != null) {
                                                      setState(() {
                                                        final isSame = value
                                                            .isSameDay(
                                                              DateTime.now(),
                                                            );
                                                        widget.bookingProvider
                                                          ..transporterDetailList
                                                              .value[widget.mainIndex]
                                                              .carDetails[index]
                                                              .dropOffDate = isSame
                                                              ? DateTime.now().add(
                                                                  const Duration(
                                                                    hours: 1,
                                                                  ),
                                                                )
                                                              : value
                                                          ..notify();
                                                      });
                                                    }
                                                  });
                                                },
                                                hintText: 'DD/MM/YYYY',
                                                suffixIcon: AppAssets.iconsCalendar
                                                    .image(
                                                      height: AppSize.sp20,
                                                      width: AppSize.sp20,
                                                      color: AppColors.ff495057,
                                                      colorBlendMode: BlendMode.srcIn,
                                                    ),
                                                // controller: pickupDateController,
                                              ),
                                            if (widget.isFirstScreen)
                                              Padding(
                                                padding: EdgeInsets.only(
                                                  top: AppSize.sp5,
                                                  bottom: AppSize.sp12,
                                                ),
                                                child: Text(
                                                  '${context.l10n.storageFee} '
                                                  '${widget.homeProvider.selectedOriginStockLocation.value?.stopLocationCharge?.toStringAsFixed(1).smartFormat()}'
                                                  ' ${context.l10n.perDay}',
                                                  style: context.textTheme.bodySmall
                                                      ?.copyWith(
                                                        fontSize: AppSize.sp11,
                                                        color: AppColors.ff6C757D,
                                                      ),
                                                ),
                                              ),
                                            Selector<
                                              BookingProvider,
                                              (bool, String?)
                                            >(
                                              selector: (p0, bookingProvider) => (
                                                data.isInsuranceIncluded,
                                                data.insurance,
                                              ),
                                              builder: (context, value, child) {
                                                return ValueListenableBuilder(
                                                  valueListenable: widget
                                                      .bookingProvider
                                                      .insuranceList,
                                                  builder: (context, insuranceList, child) {
                                                    return Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment.start,
                                                      children: [
                                                        if (widget.isFirstScreen)
                                                          Row(
                                                            spacing: AppSize.w8,
                                                            children: [
                                                              AppConfirmCheckBox(
                                                                onSelectionChanged: ({required value}) {
                                                                  widget
                                                                      .bookingProvider
                                                                    ..transporterDetailList
                                                                            .value[widget
                                                                                .mainIndex]
                                                                            .carDetails[index]
                                                                            .isInsuranceIncluded =
                                                                        value
                                                                    ..notify();
                                                                  if (!value) {
                                                                    widget
                                                                        .bookingProvider
                                                                      ..transporterDetailList
                                                                              .value[widget
                                                                                  .mainIndex]
                                                                              .carDetails[index]
                                                                              .insurance =
                                                                          ''
                                                                      ..notify();
                                                                    controllerList[index]
                                                                        .clear();
                                                                  } else {
                                                                    widget
                                                                        .bookingProvider
                                                                        .getInsurance();
                                                                  }
                                                                },
                                                                value: value.$1,
                                                              ),
                                                              Flexible(
                                                                fit: FlexFit.tight,
                                                                child: Text(
                                                                  l10n.includeInsurance,
                                                                  style: context
                                                                      .textTheme
                                                                      .bodyMedium
                                                                      ?.copyWith(
                                                                        fontSize:
                                                                            AppSize
                                                                                .sp14,
                                                                        fontWeight:
                                                                            FontWeight
                                                                                .w400,
                                                                      ),
                                                                ),
                                                              ),
                                                              Text(
                                                                '${insuranceList.firstWhere((element) => element.id.toString() == value.$2, orElse: InsuranceModel.new).amount ?? 0}'
                                                                    .smartFormat(),
                                                                style: context
                                                                    .textTheme
                                                                    .bodyMedium
                                                                    ?.copyWith(
                                                                      color: AppColors
                                                                          .ff67509C,
                                                                      fontSize:
                                                                          AppSize
                                                                              .sp14,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w700,
                                                                    ),
                                                              ),
                                                            ],
                                                          ),
                                                        if (widget.isFirstScreen)
                                                          Gap(AppSize.sp15),
                                                        if (widget.isFirstScreen)
                                                          AbsorbPointer(
                                                            absorbing: !value.$1,
                                                            child: Builder(
                                                              builder: (context) {
                                                                final selectedInsurance =
                                                                    insuranceList
                                                                        .where(
                                                                          (element) =>
                                                                              element
                                                                                  .id
                                                                                  .toString() ==
                                                                              data.insurance,
                                                                        )
                                                                        .firstOrNull;
            
                                                                // Update controller when insurance changes
                                                                WidgetsBinding.instance.addPostFrameCallback((
                                                                  _,
                                                                ) {
                                                                  if (index <
                                                                      insuranceDropdownControllers
                                                                          .length) {
                                                                    if (selectedInsurance !=
                                                                        null) {
                                                                      insuranceDropdownControllers[index].setDropDown(
                                                                        DropDownValueModel(
                                                                          value: selectedInsurance
                                                                              .id
                                                                              ?.toString(),
                                                                          name:
                                                                              '${selectedInsurance.name} '
                                                                              "(${(selectedInsurance.amount ?? '').toString().smartFormat()})",
                                                                        ),
                                                                      );
                                                                    } else {
                                                                      insuranceDropdownControllers[index]
                                                                          .clearDropDown();
                                                                    }
                                                                  }
                                                                });
            
                                                                return AppDropdown(
                                                                  controller:
                                                                      index <
                                                                          insuranceDropdownControllers
                                                                              .length
                                                                      ? insuranceDropdownControllers[index]
                                                                      : SingleValueDropDownController(),
                                                                  items: insuranceList
                                                                      .map(
                                                                        (
                                                                          e,
                                                                        ) => DropDownValueModel(
                                                                          name:
                                                                              "${e.name} (\$${e.amount ?? ''})",
                                                                          value: e.id
                                                                              ?.toString(),
                                                                        ),
                                                                      )
                                                                      .toList(),
                                                                  labelText: l10n
                                                                      .insuranceProvider,
                                                                  onChanged: (p0) {
                                                                    if (p0
                                                                            is DropDownValueModel &&
                                                                        p0.value !=
                                                                            null) {
                                                                      widget
                                                                          .bookingProvider
                                                                        ..transporterDetailList
                                                                                .value[widget
                                                                                    .mainIndex]
                                                                                .carDetails[index]
                                                                                .insurance =
                                                                            p0.value
                                                                                as String
                                                                        ..notify();
                                                                    }
                                                                  },
                                                                );
                                                              },
                                                            ),
                                                          ),
            
                                                        /// add images widget
                                                        if ((widget
                                                                    .bookingProvider
                                                                    .transporterDetailList
                                                                    .value[widget
                                                                        .mainIndex]
                                                                    .carDetails[index]
                                                                    .fileImage
                                                                    ?.isNotEmpty ??
                                                                false) ||
                                                            widget.isFirstScreen) ...[
                                                          Divider(
                                                            height: AppSize.h30,
                                                            color: AppColors.ffF2EEF8,
                                                          ),
                                                          Text(
                                                            '* ${widget.isFirstScreen ? context.l10n.attachPhotos : context.l10n.attachedPhotos}',
                                                            style: TextStyle(
                                                              fontSize:
                                                                  widget.isFirstScreen
                                                                  ? AppSize.sp10
                                                                  : AppSize.sp12,
                                                              fontWeight:
                                                                  FontWeight.w400,
                                                            ),
                                                          ),
                                                          Selector<
                                                            BookingProvider,
                                                            List<String>?
                                                          >(
                                                            selector: (p0, p1) => p1
                                                                .transporterDetailList
                                                                .value[widget
                                                                    .mainIndex]
                                                                .carDetails[index]
                                                                .fileImage,
                                                            builder:
                                                                (
                                                                  context,
                                                                  fileImageList,
                                                                  child,
                                                                ) {
                                                                  return VehicleImageSelectionWidget(
                                                                    mainIndex: widget
                                                                        .mainIndex,
                                                                    carIndex: index,
                                                                    isReadOnly: !widget
                                                                        .isFirstScreen,
                                                                    onImageListChange:
                                                                        (
                                                                          imgList,
                                                                        ) => widget.bookingProvider
                                                                          ..transporterDetailList
                                                                                  .value[widget.mainIndex]
                                                                                  .carDetails[index]
                                                                                  .fileImage =
                                                                              imgList
                                                                          ..notify(),
                                                                    fileImageList:
                                                                        fileImageList,
                                                                );
                                                              },
                                                        ),
                                                      ],
                                                    ],
                                                  );
                                                },
                                              );
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                )
                              );
                            },
                          ),
                        );
                      },
                    ),
                    Gap(AppSize.w16),
                    AnimatedBuilder(
                      animation: _pageController,
                      builder: (context, child) {
                        return Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            for (
                              var i = 0;
                              i < widget.providerData.carDetails.length;
                              i++
                            )
                              Container(
                                margin: EdgeInsets.symmetric(
                                  horizontal: AppSize.w4,
                                ),
                                width: AppSize.h8,
                                height: AppSize.h8,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: i == (_pageController.page?.round() ?? 0)
                                      ? Colors.blue
                                      : Colors.grey[400],
                                ),
                              ),
                          ],
                        );
                      },
                    ),
                    Gap(AppSize.w12),
                    if (widget.onClose != null)
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            isOpen = !isOpen;
                          });
                        },
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          spacing: AppSize.w6,
                          children: [
                            const Icon(Icons.close, color: AppColors.primaryColor),
                            Text(
                              context.l10n.close,
                              style: context.textTheme.bodyLarge?.copyWith(
                                color: context.theme.primaryColor,
                                fontWeight: FontWeight.w600,
                                fontSize: AppSize.sp16,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ],
              ),
            ),
            Gap(AppSize.h10)
          ],
        );
      },
    );
  }
}
