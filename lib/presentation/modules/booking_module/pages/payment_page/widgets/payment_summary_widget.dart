import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';

/// Payment summary row
class SummaryRow extends StatelessWidget {
  /// Constructor
  const SummaryRow({
    required this.title,
    required this.amount,
    this.isSubItem = false,
    this.isNetAmount = false,
    super.key,
  });

  /// Title
  final String title;

  /// Amount
  final String amount;

  /// Whether this is a sub-item (indented)
  final bool isSubItem;

  /// Whether this is a net amount (highlighted)
  final bool isNetAmount;

  @override
  Widget build(BuildContext context) {
    return amount.isEmpty
        ? const SizedBox()
        : Padding(
            padding: EdgeInsets.only(
              top: AppSize.h8,
              left: isSubItem ? AppSize.w16 : 0,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              spacing: AppSize.w10,
              children: [
                Flexible(
                  child: Text(
                    title,
                    style: context.textTheme.bodyMedium?.copyWith(
                      fontWeight: isNetAmount
                          ? FontWeight.w600
                          : FontWeight.w500,
                      fontSize: isSubItem ? AppSize.sp13 : AppSize.sp14,
                      color: isNetAmount
                          ? AppColors.primaryColor
                          : isSubItem
                          ? AppColors.ff6C757D
                          : null,
                    ),
                  ),
                ),
                Text(
                  amount,
                  style: context.textTheme.bodyMedium?.copyWith(
                    fontSize: isSubItem ? AppSize.sp14 : AppSize.sp16,
                    fontWeight: isNetAmount ? FontWeight.w600 : FontWeight.w400,
                    color: isNetAmount ? AppColors.primaryColor : null,
                  ),
                ),
              ],
            ),
          );
  }
}

/// Detailed payment breakdown widget with explanations
class DetailedPaymentBreakdown extends StatelessWidget {
  /// Constructor
  const DetailedPaymentBreakdown({
    required this.title,
    required this.grossAmount,
    required this.netAmount,
    this.explanation,
    this.details,
    this.showGrossAmount = true,
    super.key,
  });

  /// Title of the breakdown section
  final String title;

  /// Gross amount
  final String grossAmount;

  /// Net amount (what customer pays)
  final String netAmount;

  /// Explanation of what this cost covers
  final String? explanation;

  /// Additional details (e.g., days, rates, etc.)
  final List<PaymentDetail>? details;

  /// Whether to show gross amount
  final bool showGrossAmount;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SummaryRow(title: title, amount: ''),

        // Explanation of what this cost covers
        if (explanation != null && explanation!.isNotEmpty)
          Padding(
            padding: EdgeInsets.only(left: AppSize.w16, top: AppSize.h4),
            child: Text(
              explanation!,
              style: context.textTheme.bodySmall?.copyWith(
                color: AppColors.ff6C757D,
                fontSize: AppSize.sp12,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),

        // Additional details (days, rates, etc.)
        if (details != null && details!.isNotEmpty)
          ...details!.map(
            (detail) => Padding(
              padding: EdgeInsets.only(left: AppSize.w24, top: AppSize.h2),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Text(
                      detail.label,
                      style: context.textTheme.bodySmall?.copyWith(
                        color: AppColors.ff495057,
                        fontSize: AppSize.sp11,
                      ),
                    ),
                  ),
                  Text(
                    detail.value,
                    style: context.textTheme.bodySmall?.copyWith(
                      color: AppColors.ff495057,
                      fontSize: AppSize.sp11,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),

        if (showGrossAmount && grossAmount.isNotEmpty)
          SummaryRow(
            title: '• Gross Amount',
            amount: grossAmount,
            isSubItem: true,
          ),
        if (netAmount.isNotEmpty)
          SummaryRow(
            title: '• Net Amount (You Pay)',
            amount: netAmount,
            isSubItem: true,
            isNetAmount: true,
          ),
      ],
    );
  }
}

/// Model for payment detail items
class PaymentDetail {
  const PaymentDetail({required this.label, required this.value});

  final String label;
  final String value;
}

/// Enhanced payment section with company info and detailed breakdown
class CompanyPaymentSection extends StatelessWidget {
  /// Constructor
  const CompanyPaymentSection({
    required this.companyName,
    required this.children,
    this.netTripCharge,
    super.key,
  });

  /// Company name
  final String companyName;

  /// Payment breakdown widgets
  final List<Widget> children;

  /// Net trip charge for this company
  final String? netTripCharge;

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppSize.r8),
        color: Colors.white,
        border: Border.all(color: AppColors.ffDEE2E6),
      ),
      child: Padding(
        padding: EdgeInsets.all(AppSize.h16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Company header
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: AppSize.w12,
                vertical: AppSize.h8,
              ),
              decoration: BoxDecoration(
                color: AppColors.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppSize.r6),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.local_shipping,
                    size: AppSize.sp16,
                    color: AppColors.primaryColor,
                  ),
                  SizedBox(width: AppSize.w8),
                  Expanded(
                    child: Text(
                      companyName,
                      style: context.textTheme.titleMedium?.copyWith(
                        fontSize: AppSize.sp16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: AppSize.h12),

            // Payment breakdown
            ...children,

            // Net trip charge for this company
            if (netTripCharge != null && netTripCharge!.isNotEmpty)
              Padding(
                padding: EdgeInsets.only(top: AppSize.h16),
                child: Container(
                  padding: EdgeInsets.all(AppSize.h12),
                  decoration: BoxDecoration(
                    color: AppColors.successColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppSize.r6),
                    border: Border.all(color: AppColors.successColor),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        size: AppSize.sp18,
                        color: AppColors.successColor,
                      ),
                      SizedBox(width: AppSize.w8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Total for $companyName',
                              style: context.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: AppColors.successColor,
                              ),
                            ),
                            Text(
                              netTripCharge!,
                              style: context.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w700,
                                color: AppColors.successColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
