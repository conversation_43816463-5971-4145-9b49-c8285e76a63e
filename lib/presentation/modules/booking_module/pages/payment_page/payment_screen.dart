import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/extensions/num_extension.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/payment_page/provider/payment_provider.dart';

import 'package:transport_match/presentation/modules/home_module/pages/models/payment_summary_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/req_model.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_padding.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';
import 'package:transport_match/widgets/marqee_widget.dart';

/// Payment page
class PaymentScreen extends StatelessWidget {
  /// Constructor
  const PaymentScreen({
    super.key,
    required this.isExclusiveTrip,
    required this.bookingId,
    required this.paymentDataModel,
    required this.bookingProviderData,
  }) : assert(bookingProviderData != null || paymentDataModel != null, 'Please pass payment and booking data');
  final bool isExclusiveTrip;
  final String? bookingId;
  final PaymentDataModel? paymentDataModel;
  final ProviderData? bookingProviderData;

  /// Build provider section with expansion tile
  Widget _buildProviderSection(BuildContext context, PaymentSummaryModel provider) {
    return Container(
      margin: EdgeInsets.only(bottom: AppSize.h16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppSize.r12),
        color: Colors.white,
        boxShadow: [BoxShadow(color: Colors.black.withValues(alpha: 0.05), blurRadius: 8, offset: const Offset(0, 2))],
      ),
      child: ExpansionTile(
        childrenPadding: EdgeInsets.zero,
        title: Row(
          children: [
            Container(
              padding: EdgeInsets.all(AppSize.h8),
              decoration: BoxDecoration(
                color: AppColors.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppSize.r8),
              ),
              child: const Icon(Icons.local_shipping, color: AppColors.primaryColor, size: 20),
            ),
            SizedBox(width: AppSize.w12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    provider.companyName ?? 'Transport Company',
                    style: context.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600, fontSize: AppSize.sp16),
                  ),
                  Text(
                    '${context.l10n.tripId}: ${provider.tripId ?? 'N/A'}',
                    style: context.textTheme.bodySmall?.copyWith(color: AppColors.ff6C757D, fontSize: AppSize.sp12),
                  ),
                ],
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: AppSize.w12, vertical: AppSize.h6),
              decoration: BoxDecoration(
                color: AppColors.successColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppSize.r6),
                border: Border.all(color: AppColors.successColor),
              ),
              child: Text(
                provider.totalTripCost?.toString().smartFormat() ?? '',
                style: context.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w700,
                  color: AppColors.successColor,
                ),
              ),
            ),
          ],
        ),
        children: [
          Container(
            padding: EdgeInsets.all(AppSize.sp16),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(AppSize.r12),
                bottomRight: Radius.circular(AppSize.r12),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: AppSize.h4,
              children: [
                // Cost Breakdown Header
                Text(
                  context.l10n.costBreakdown,
                  style: context.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.primaryColor,
                  ),
                ),

                // Transportation Cost
                if (provider.transportationCharge.isNotNullNotZero)
                  _buildDetailedCostRow(
                    context,
                    context.l10n.transportationCost,
                    context.l10n.transportationCostDescription,
                    provider.transportationCharge!.toString().smartFormat(),
                    Icons.directions_car,
                  ),

                // Storage Fees
                if (provider.startStopLocationStorageCharge.isNotNullNotZero)
                  _buildDetailedCostRow(
                    context,
                    context.l10n.startLocationStorage,
                    context.l10n.startLocationStorageDescription,
                    provider.startStopLocationStorageCharge!.toString().smartFormat(),
                    Icons.warehouse,
                  ),

                if (provider.endStopLocationStorageCharge.isNotNullNotZero)
                  _buildDetailedCostRow(
                    context,
                    context.l10n.endLocationStorage,
                    context.l10n.endLocationStorageDescription,
                    provider.endStopLocationStorageCharge!.toString().smartFormat(),
                    Icons.warehouse_outlined,
                  ),

                // Customer Location Service
                if (provider.customerLocationToStartLocationServiceCharge.isNotNullNotZero)
                  _buildDetailedCostRow(
                    context,
                    context.l10n.customerLocationService,
                    context.l10n.customerLocationServiceDescription,
                    provider.customerLocationToStartLocationServiceCharge!.toString().smartFormat(),
                    Icons.location_on,
                  ),

                // Insurance
                if (provider.insuranceCharge.isNotNullNotZero)
                  _buildDetailedCostRow(
                    context,
                    context.l10n.insuranceCoverage,
                    context.l10n.insuranceCoverageDescription,
                    provider.insuranceCharge!.toString().smartFormat(),
                    Icons.security,
                  ),

                // Car-wise charges
                if (provider.carPaymentData?.carCharges?.isNotEmpty ?? false) ...[
                  Gap(AppSize.h10),
                  ExpansionTile(
                    tilePadding: EdgeInsets.zero,
                    title: Row(
                      children: [
                        Icon(Icons.directions_car_filled, color: AppColors.primaryColor, size: AppSize.sp18),
                        SizedBox(width: AppSize.w8),
                        Expanded(
                          child: Text(
                            context.l10n.vehicleWiseCharges(provider.carPaymentData!.carCharges!.length),
                            style: context.textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: AppColors.primaryColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                    children: provider.carPaymentData!.carCharges!.entries
                        .map((entry) => _buildCarChargeSection(context, entry.key, entry.value))
                        .toList(),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build detailed cost row with icon and description
  Widget _buildDetailedCostRow(BuildContext context, String title, String description, String amount, IconData icon) {
    return Container(
      margin: EdgeInsets.only(bottom: AppSize.h6),
      padding: EdgeInsets.all(AppSize.sp8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSize.r8),
        border: Border.all(color: AppColors.ffDEE2E6),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(AppSize.h8),
            decoration: BoxDecoration(
              color: AppColors.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppSize.r6),
            ),
            child: Icon(icon, color: AppColors.primaryColor, size: AppSize.sp16),
          ),
          SizedBox(width: AppSize.w12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: context.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600, fontSize: AppSize.sp14),
                ),
                Text(
                  description,
                  style: context.textTheme.bodySmall?.copyWith(color: AppColors.ff6C757D, fontSize: AppSize.sp12),
                ),
              ],
            ),
          ),
          Text(
            amount,
            style: context.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w700, color: AppColors.primaryColor),
          ),
        ],
      ),
    );
  }

  /// Build car charge section
  Widget _buildCarChargeSection(BuildContext context, String serialNumber, CarChargeData carData) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: AppSize.h8),
      padding: EdgeInsets.all(AppSize.h16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSize.r8),
        border: Border.all(color: AppColors.ffDEE2E6),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Car header
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(AppSize.h6),
                decoration: BoxDecoration(
                  color: AppColors.successColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppSize.r4),
                ),
                child: Icon(Icons.directions_car, color: AppColors.successColor, size: AppSize.sp16),
              ),
              SizedBox(width: AppSize.w8),
              Expanded(
                child: Text(
                  '${context.l10n.vehicle}: $serialNumber',
                  style: context.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.successColor,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: AppSize.h12),

          // Car charges breakdown
          if (carData.transportationCharge.isNotNullNotZero)
            _buildCarCostRow(
              context,
              context.l10n.transportation,
              carData.transportationCharge!.toString().smartFormat(),
              null, // Net charge not available in new model
            ),

          if (carData.startStopLocationCharge.isNotNullNotZero)
            _buildCarCostRow(
              context,
              context.l10n.startStorage,
              carData.startStopLocationCharge!.toString().smartFormat(),
              null, // Net charge not available in new model
            ),

          if (carData.endStopLocationCharge.isNotNullNotZero)
            _buildCarCostRow(
              context,
              context.l10n.endStorage,
              carData.endStopLocationCharge!.toString().smartFormat(),
              null, // Net charge not available in new model
            ),

          if (carData.customerLocationToStartLocationServiceCharge.isNotNullNotZero)
            _buildCarCostRow(
              context,
              context.l10n.customerService,
              carData.customerLocationToStartLocationServiceCharge!.toString().smartFormat(),
              null, // Net charge not available in new model
            ),

          // Insurance charge not available in new car data model
        ],
      ),
    );
  }

  /// Build car cost row with gross and net amounts
  Widget _buildCarCostRow(BuildContext context, String title, String grossAmount, String? netAmount) {
    return Padding(
      padding: EdgeInsets.only(bottom: AppSize.h8),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: TextStyle(fontSize: AppSize.sp13, fontWeight: FontWeight.w500),
              ),
              Text(
                grossAmount,
                style: TextStyle(fontSize: AppSize.sp13, fontWeight: FontWeight.w600),
              ),
            ],
          ),
          if (netAmount != null && netAmount.isNotEmpty)
            Padding(
              padding: EdgeInsets.only(top: AppSize.h2, left: AppSize.w8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '• ${context.l10n.netYouPay}',
                    style: TextStyle(
                      fontSize: AppSize.sp11,
                      fontWeight: FontWeight.w500,
                      color: AppColors.primaryColor,
                    ),
                  ),
                  Text(
                    netAmount,
                    style: TextStyle(
                      fontSize: AppSize.sp11,
                      fontWeight: FontWeight.w600,
                      color: AppColors.primaryColor,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// Build summary row with optional net amount
  Widget _buildSummaryRow(BuildContext context, String title, String amount, {String? netAmount}) {
    return Padding(
      padding: EdgeInsets.only(top: AppSize.h8),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: TextStyle(fontSize: AppSize.sp14, fontWeight: FontWeight.w500),
              ),
              Text(
                amount,
                style: TextStyle(fontSize: AppSize.sp14, fontWeight: FontWeight.w600),
              ),
            ],
          ),
          if (netAmount != null && netAmount.isNotEmpty)
            Padding(
              padding: EdgeInsets.only(top: AppSize.h4, left: AppSize.w16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '• ${context.l10n.netYouPay}',
                    style: TextStyle(
                      fontSize: AppSize.sp12,
                      fontWeight: FontWeight.w500,
                      color: AppColors.primaryColor,
                    ),
                  ),
                  Text(
                    netAmount,
                    style: TextStyle(
                      fontSize: AppSize.sp12,
                      fontWeight: FontWeight.w600,
                      color: AppColors.primaryColor,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final paymentData = paymentDataModel;
    return ChangeNotifierProvider(
      create: (context) => PaymentProvider(paymentData),
      builder: (context, child) {
        final paymentProvider = context.watch<PaymentProvider>();
        return Scaffold(
          appBar: CustomAppBar(title: context.l10n.payments),
          backgroundColor: AppColors.ffF8F9FA,
          bottomNavigationBar: AppButton(
            onPressed: () async {
              if (paymentData?.netTotalWithTax != null && paymentData!.netTotalWithTax! > 999999.99) {
                r"Amount cannot exceed Stripe's max limit of $999,999.99".showErrorAlert();
              } else {
                await paymentProvider.navigateToWebViewForPayment(
                  context,
                  bookingData: bookingProviderData,
                  bookingId: bookingId,
                  isExclusiveTrip: isExclusiveTrip,
                );
              }
            },
            text: '${paymentData?.netTotalWithTax}'.smartFormat(),
          ),
          body: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: AppSize.appPadding).add(EdgeInsets.only(bottom: AppSize.h16)),
            child: Theme(
              data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: AppSize.h8,
                children: [
                  // Provider Breakdown Section
                  if (paymentData?.paymentSummary?.isNotEmpty ?? false)
                    ExpansionTile(
                      initiallyExpanded: true,
                      title: Text(
                        context.l10n.providerBreakdownTitle,
                        style: context.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600),
                      ),
                      children: paymentData!.paymentSummary!
                          .map((provider) => _buildProviderSection(context, provider))
                          .toList(),
                    ),

                  DecoratedBox(
                    decoration: BoxDecoration(borderRadius: BorderRadius.circular(AppSize.r8), color: Colors.white),
                    child: AppPadding.symmetric(
                      horizontal: AppSize.w16,
                      vertical: AppSize.h8,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,

                        children: [
                          Text(
                            context.l10n.overallSummary,
                            style: context.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600),
                          ),
                          // Transportation Cost with both total and net
                          if (paymentData?.totalTransportationCharge.isNotNullNotZero ?? false)
                            _buildSummaryRow(
                              context,
                              context.l10n.transportationCost,
                              paymentData!.totalTransportationCharge!.toString().smartFormat(),
                              netAmount: paymentData.totalNetTransportationCharge?.toString().smartFormat(),
                            ),

                          // Storage Charges
                          if (paymentData?.totalStartLocationStorageCharge.isNotNullNotZero ?? false)
                            _buildSummaryRow(
                              context,
                              context.l10n.startLocationStorage,
                              paymentData!.totalStartLocationStorageCharge!.toString().smartFormat(),
                              netAmount: paymentData.totalNetStartLocationStorageCharge?.toString().smartFormat(),
                            ),

                          if (paymentData?.totalEndLocationStorageCharge.isNotNullNotZero ?? false)
                            _buildSummaryRow(
                              context,
                              context.l10n.endLocationStorage,
                              paymentData!.totalEndLocationStorageCharge!.toString().smartFormat(),
                              netAmount: paymentData.totalNetEndLocationStorageCharge?.toString().smartFormat(),
                            ),
                          // if(paymentData?.totalCustomerLocationToStartStopLocationServiceCharge.isNotNullNotZero ?? false)
                          //   _buildSummaryRow(
                          //     context,
                          //     context.l10n.endLocationStorage,
                          //     paymentData!.totalCustomerLocationToStartStopLocationServiceCharge!
                          //         .toString()
                          //         .smartFormat(),
                          //     netAmount: paymentData
                          //         .totalNetCustomerLocationToStartStopLocationServiceCharge
                          //         ?.toString()
                          //         .smartFormat(),
                          //   ),

                          // Insurance with both total and net
                          if (paymentData?.totalInsuranceCharge.isNotNullNotZero ?? false)
                            _buildSummaryRow(
                              context,
                              context.l10n.insuranceCost,
                              paymentData!.totalInsuranceCharge!.toString().smartFormat(),
                              netAmount: paymentData.totalNetInsuranceCharge?.toString().smartFormat(),
                            ),

                          // App Fee with both total and net
                          if (paymentData?.totalAppFee.isNotNullNotZero ?? false)
                            _buildSummaryRow(
                              context,
                              context.l10n.serviceFee,
                              paymentData!.totalAppFee!.toString().smartFormat(),
                              netAmount: paymentData.netTotalAppFee?.toString().smartFormat(),
                            ),

                          // Customer Location Service
                          if (paymentData?.totalCustomerLocationToStartStopLocationServiceCharge.isNotNullNotZero ??
                              false)
                            _buildSummaryRow(
                              context,
                              context.l10n.customerLocationService,
                              paymentData!.totalCustomerLocationToStartStopLocationServiceCharge!
                                  .toString()
                                  .smartFormat(),
                              netAmount: paymentData.totalNetCustomerLocationToStartStopLocationServiceCharge
                                  ?.toString()
                                  .smartFormat(),
                            ),

                          // Tax Information
                          // if (paymentData?.taxAmount.isNotNullNotZero ?? false)
                          //   Column(
                          //     children: [
                          //       Divider(height: AppSize.h24),
                          //       _buildSummaryRow(
                          //         context,
                          //         '${context.l10n.tax} (${paymentData!.taxRate?.toString() ?? '0'}%)',
                          //         paymentData.taxAmount!
                          //             .toString()
                          //             .smartFormat(),
                          //       ),
                          //     ],
                          //   ),

                          ///total
                          if (paymentData?.taxAmount.isNotNullNotZero ?? false)
                            Column(
                              children: [
                                Divider(height: AppSize.h24),
                                _buildSummaryRow(
                                  context,
                                  '${context.l10n.total}',
                                  paymentProvider.totalAmount.toString().smartFormat(),
                                ),
                              ],
                            ),
                        ],
                      ),
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.all(AppSize.h16),
                    margin: EdgeInsets.only(top: AppSize.h8),
                    decoration: BoxDecoration(borderRadius: BorderRadius.circular(AppSize.r8), color: Colors.white),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(context.l10n.netAmount, style: context.textTheme.titleLarge),
                            Flexible(
                              child: MarqueeWidget(
                                child: Text(
                                  paymentProvider.netTotalAmount.toString().smartFormat(),
                                  style: context.textTheme.titleLarge,
                                ),
                              ),
                            ),
                          ],
                        ),
                        Padding(
                          padding: EdgeInsets.only(top: AppSize.h4, left: AppSize.w16),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                '• ${context.l10n.tax} (${paymentData!.taxRate?.toString() ?? '0'}%)',
                                style: TextStyle(
                                  fontSize: AppSize.sp14,
                                  fontWeight: FontWeight.w500,
                                  color: AppColors.primaryColor,
                                ),
                              ),
                              Text(
                                paymentData.taxAmount.toString().smartFormat(),
                                style: TextStyle(
                                  fontSize: AppSize.sp14,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.primaryColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Builder(
                    builder: (context) {
                      final pay = paymentProvider.netTotalAmount * 100 / paymentProvider.totalAmount;
                      final remain = 100 - pay;
                      return Text(
                        context.l10n.noteUHaveToPay(pay.toStringAsFixed(1), remain.toStringAsFixed(1)).smartFormat(),
                        style: context.textTheme.bodySmall?.copyWith(
                          color: AppColors.ff6C757D,
                          fontWeight: FontWeight.w400,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
