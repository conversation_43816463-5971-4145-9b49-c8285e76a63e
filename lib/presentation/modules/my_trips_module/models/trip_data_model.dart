import 'package:flutter/material.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/req_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/pages/exclusive_trip_screen/models/stock_data_model.dart';
import 'package:transport_match/utils/app_common_functions.dart';

class TripDataModel {
  TripDataModel({this.count, this.next, this.previous, this.results});

  factory TripDataModel.fromJson(Map<String, dynamic> json) => TripDataModel(
    count: json['count'] as int?,
    next: json['next'] as String?,
    previous: json['previous'] as String?,
    results: json['results'] == null
        ? []
        : List<TripModel>.from(
            (json['results'] as List?)?.map(
                  (x) => TripModel.fromJson(x as Map<String, dynamic>),
                ) ??
                [],
          ),
  );
  final int? count;
  final String? next;
  final String? previous;
  final List<TripModel>? results;

  Map<String, dynamic> toJson() => {
    'count': count,
    'next': next,
    'previous': previous,
    'results': results == null
        ? []
        : List<dynamic>.from(results!.map((x) => x.toJson())),
  };
}

class VehicleInfoModel {
  VehicleInfoModel({
    this.id,
    this.brand,
    this.year,
    this.model,
    this.size,
    this.serialNumber,
    this.car,
    this.isCarPickedUpToStopLocation = false,
    this.pickUpServiceAndDropOffService,
    this.serviceType,
    this.fromCarToBePickedUpLocation,
    this.isCarVerificationRequired = false,
    this.verificationStatus,
    this.verificationStatusUpdatedAt,
    this.isWinchRequired = false,
    this.carDescription,
    this.assignIndex,
    this.insuranceNumber,
    this.charges,
    this.status,
    this.isCarAssigned,
    this.isInsuranceIncluded = false,
    this.images,
    this.isBookedCarCancellable = false, // ✅ new field
  });

  factory VehicleInfoModel.fromJson(
    Map<String, dynamic> json,
  ) => VehicleInfoModel(
    id: json['id'] as int?,
    brand: json['brand'] as String?,
    year: json['year'] as int?,
    model: json['model'] as String?,
    size: json['size'] as num?,
    serialNumber: json['serial_number'] as String?,
    car: json['car'] == null
        ? null
        : CarDataModel.fromJson(json['car'] as Map<String, dynamic>),
    charges: json['charges'] == null
        ? []
        : List.from(
            (json['charges'] as List?)?.map(
                  (e) => ChargesModel.fromJson(e as Map<String, dynamic>),
                ) ??
                [],
          ),
    isCarPickedUpToStopLocation:
        (json['is_car_picked_up_to_stop_location'] as bool?) ?? false,
    pickUpServiceAndDropOffService: json['pick_up_service_and_drop_off_service']
        ?.toString(),
    serviceType: json['pick_up_service_and_drop_off_service'] is Map
        ? ServiceTypeModel.fromJson(
            json['pick_up_service_and_drop_off_service']
                as Map<String, dynamic>,
          )
        : null,
    fromCarToBePickedUpLocation:
        json['from_car_to_be_picked_up_location'] == null
        ? null
        : AddressModel.fromJson(
            json['from_car_to_be_picked_up_location'] as Map<String, dynamic>,
          ),
    isCarVerificationRequired:
        (json['is_car_verification_required'] as bool?) ?? false,
    verificationStatus: json['verification_status'] as String?,
    verificationStatusUpdatedAt: json['verification_status_updated_at'] == null
        ? null
        : DateTime.parse(json['verification_status_updated_at'] as String),
    isWinchRequired: (json['is_winch_required'] as bool?) ?? false,
    carDescription: json['car_description'] as String?,
    status: json['status'] as String?,
    images: json['images'] == null
        ? null
        : (json['images'] as List)
              .map((e) => CarImageModel.fromJson(e as Map<String, dynamic>))
              .toList(),
    isBookedCarCancellable:
        json['is_booked_car_cancellable'] as bool? ?? false, // ✅ mapping
  );

  final int? id;
  int? assignIndex;
  final String? brand;
  final int? year;
  final String? model;
  final num? size;
  final String? serialNumber;
  final CarDataModel? car;
  final bool isCarPickedUpToStopLocation;
  final String? pickUpServiceAndDropOffService;
  final ServiceTypeModel? serviceType;
  final AddressModel? fromCarToBePickedUpLocation;
  final bool isCarVerificationRequired;
  final String? verificationStatus;
  final DateTime? verificationStatusUpdatedAt;
  final bool isWinchRequired;
  final String? carDescription;
  final List<ChargesModel>? charges;
  String? insuranceNumber;
  String? status;
  bool isInsuranceIncluded;
  List<CarImageModel>? images;

  /// variable is to check if the car is assigned or not
  ValueNotifier<bool>? isCarAssigned;

  /// ✅ new field
  bool isBookedCarCancellable;

  Map<String, dynamic> toJson() => {
    'id': id,
    'brand': brand,
    'year': year,
    'model': model,
    'size': size,
    'serial_number': serialNumber,
    'car': car?.toJson(),
    'is_car_picked_up_to_stop_location': isCarPickedUpToStopLocation,
    'pick_up_service_and_drop_off_service': pickUpServiceAndDropOffService,
    'from_car_to_be_picked_up_location': fromCarToBePickedUpLocation,
    'is_car_verification_required': isCarVerificationRequired,
    'verification_status': verificationStatus,
    'charges': charges == null
        ? []
        : List<dynamic>.from(charges!.map((x) => x.toJson())),
    'verification_status_updated_at': verificationStatusUpdatedAt
        ?.toUtc()
        .toIso8601String(),
    'is_winch_required': isWinchRequired,
    'car_description': carDescription,
    'status': status,
    if (images?.isNotEmpty ?? false) 'images': images?.map((e) => e.toJson()),
    'is_vehicle_cancellable': isBookedCarCancellable, // ✅ added in toJson
  };
}

class CarDataModel {
  CarDataModel({
    this.id,
    this.size,
    this.model,
    this.year,
    this.brand,
    this.createdAt,
    this.isDeleted = false,
    this.deletedAt,
  });

  factory CarDataModel.fromJson(Map<String, dynamic> json) => CarDataModel(
    id: json['id'] as int?,
    size: json['size'] as num?,
    model: json['model'] as String?,
    year: json['year'] as String?,
    brand: json['brand'] as String?,
    createdAt: json['created_at'] == null
        ? null
        : DateTime.parse(json['created_at'] as String),
    isDeleted: json['is_deleted'] as bool? ?? false,
    deletedAt: json['deleted_at'],
  );
  final int? id;
  final num? size;
  final String? model;
  final String? year;
  final String? brand;
  final DateTime? createdAt;
  final bool isDeleted;
  final dynamic deletedAt;

  Map<String, dynamic> toJson() => {
    'id': id,
    'size': size,
    'model': model,
    'year': year,
    'brand': brand,
    'created_at': createdAt?.toUtc().toIso8601String(),
    'is_deleted': isDeleted,
    'deleted_at': deletedAt,
  };
}

class ChargesModel {
  ChargesModel({
    this.id,
    this.chargeType,
    this.charge,
    this.chargeDescription,
    this.bookedCarChargeId,
  });

  factory ChargesModel.fromJson(Map<String, dynamic> json) => ChargesModel(
    id: json['id'] as int?,
    chargeType: json['charge_type'] as String?,
    charge: json['charge'] as num?,
    chargeDescription: json['charge_description'] as String?,
    bookedCarChargeId: json['booked_car_charge_id'] as String?,
  );
  final int? id;
  final String? chargeType;
  final num? charge;
  final String? chargeDescription;
  final String? bookedCarChargeId;

  Map<String, dynamic> toJson() => {
    'id': id,
    'charge_type': chargeType,
    'charge': charge,
    'charge_description': chargeDescription,
    'booked_car_charge_id': bookedCarChargeId,
  };
}

class AssigneeModel {
  AssigneeModel({
    this.id,
    this.name,
    this.idProofType,
    this.idProofImageUrl,
    this.assigneeType,
  });

  factory AssigneeModel.fromJson(Map<String, dynamic> json) => AssigneeModel(
    id: json['id'] as int,
    name: json['name'] as String,
    idProofType: json['id_proof_type'] as String,
    idProofImageUrl: json['id_proof_image_url'] as String,
    assigneeType: json['assignee_type'] as String,
  );
  int? id;
  String? name;
  String? idProofType;
  String? idProofImageUrl;
  String? assigneeType;

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'id_proof_type': idProofType,
    'id_proof_image_url': idProofImageUrl,
    'assignee_type': assigneeType,
  };
}

class TripModel {
  TripModel({
    this.id,
    this.customerStartDate,
    this.customerEndDate,
    this.startStopLocation,
    this.endStopLocation,
    this.bookingType,
    this.bookingStatus,
    this.totalTripDistance,
    this.waitingType,
    this.createdAt,
    this.noOfCars,
    this.bookingDetails,
    this.carDetails,
    this.userStartLocation,
    this.userEndLocation,
    this.startStopDetailLocation,
    this.endStopDetailLocation,
    this.bookingAssignee,
    this.allVehicleInOneTrip,
  });

  factory TripModel.fromJson(Map<String, dynamic> json) => TripModel(
    id: json['id'] as int?,
    customerStartDate: json['customer_start_date'] == null
        ? null
        : DateTime.parse(json['customer_start_date'] as String).toLocal(),
    customerEndDate: json['customer_end_date'] == null
        ? null
        : DateTime.parse(json['customer_end_date'] as String).toLocal(),
    userStartLocation: json['user_start_location'] == null
        ? null
        : AddressModel.fromJson(
            json['user_start_location'] as Map<String, dynamic>,
          ),
    userEndLocation: json['user_end_location'] == null
        ? null
        : AddressModel.fromJson(
            json['user_end_location'] as Map<String, dynamic>,
          ),
    startStopLocation: json['start_stop_location'] == null
        ? null
        : TripStopLocation.fromJson(
            json['start_stop_location'] as Map<String, dynamic>,
          ),
    endStopLocation: json['end_stop_location'] == null
        ? null
        : TripStopLocation.fromJson(
            json['end_stop_location'] as Map<String, dynamic>,
          ),
    startStopDetailLocation: json['start_stop_location_charge'] == null
        ? null
        : StopDetailLocation.fromJson(
            json['start_stop_location_charge'] as Map<String, dynamic>,
          ),
    endStopDetailLocation: json['end_stop_location_charge'] == null
        ? null
        : StopDetailLocation.fromJson(
            json['end_stop_location_charge'] as Map<String, dynamic>,
          ),
    bookingType: json['booking_type'] as String?,
    bookingStatus: json['booking_status'] as String?,
    totalTripDistance:
        ((json['total_trip_distance'] as num?) ??
                json['net_booking_charge'] as num?)
            ?.round(),
    waitingType: json['waiting_type'] as String?,
    allVehicleInOneTrip: json['all_vehicle_in_one_trip'] as bool?,
    createdAt: json['created_at'] == null
        ? null
        : DateTime.parse(json['created_at'] as String),
    noOfCars: json['total_confirmed_car'] as int?,
    bookingDetails: json['booking_details'] == null
        ? []
        : List<BookingDetail>.from(
            (json['booking_details'] as List<dynamic>).map(
              (x) => BookingDetail.fromJson(x as Map<String, dynamic>),
            ),
          ),
    bookingAssignee: json['booking_assignee'] == null
        ? []
        : List<AssigneeModel>.from(
            (json['booking_assignee'] as List<dynamic>).map(
              (x) => AssigneeModel.fromJson(x as Map<String, dynamic>),
            ),
          ),
    carDetails: json['car_details'] == null
        ? []
        : List<VehicleInfoModel>.from(
            (json['car_details'] as List<dynamic>).map(
              (x) => VehicleInfoModel.fromJson(x as Map<String, dynamic>),
            ),
          ),
  );
  int? id;
  bool? allVehicleInOneTrip;
  DateTime? customerStartDate;
  DateTime? customerEndDate;
  final AddressModel? userStartLocation;
  final AddressModel? userEndLocation;
  TripStopLocation? startStopLocation;
  TripStopLocation? endStopLocation;
  StopDetailLocation? startStopDetailLocation;
  StopDetailLocation? endStopDetailLocation;
  String? bookingType;
  String? bookingStatus;
  num? totalTripDistance;
  String? waitingType;
  DateTime? createdAt;
  int? noOfCars;
  List<BookingDetail>? bookingDetails;
  final List<VehicleInfoModel>? carDetails;
  List<AssigneeModel>? bookingAssignee;

  Map<String, dynamic> toJson() => {
    'id': id,
    'customer_start_date': customerStartDate?.toUtc().toIso8601String(),
    'customer_end_date': customerEndDate?.toUtc().toIso8601String(),
    'user_start_location': userStartLocation?.toJson(),
    'user_end_location': userEndLocation?.toJson(),
    'start_stop_location': startStopLocation?.toJson(),
    'end_stop_location': endStopLocation?.toJson(),
    'start_stop_location_charge': startStopDetailLocation?.toJson(),
    'end_stop_location_charge': endStopDetailLocation?.toJson(),
    'booking_type': bookingType,
    'all_vehicle_in_one_trip': allVehicleInOneTrip,
    'booking_status': bookingStatus,
    'total_trip_distance': totalTripDistance,
    'net_booking_charge': totalTripDistance,
    'waiting_type': waitingType,
    'created_at': createdAt?.toUtc().toIso8601String(),
    'no_of_cars': noOfCars,
    'booking_assignee': bookingAssignee == null
        ? []
        : List<dynamic>.from(bookingAssignee!.map((x) => x.toJson())),
    'booking_details': bookingDetails == null
        ? []
        : List<dynamic>.from(bookingDetails!.map((x) => x.toJson())),
    'car_details': carDetails == null
        ? []
        : List<dynamic>.from(carDetails!.map((x) => x.toJson())),
  };
}

class BookingDetail {
  BookingDetail({
    this.id,
    this.status,
    this.sharedBookings,
    this.exclusiveBookings,
    this.carDetails,
    this.tripData,
    this.netTripCharge,
    this.notes,
    this.isRemainingPaymentButtonEnabled = false,
    this.isRefundSettlementPaymentButtonEnabled = false,
    this.bookingDetailId,
    this.ratings,
    this.providerUserId,
    this.providerChatRoomDetail,
    this.driverChatRoomDetail,
    this.isBookingCancellable = false, // ✅ new field
  });

  factory BookingDetail.fromJson(Map<String, dynamic> json) => BookingDetail(
    id: json['id'] as int?,
    status: json['status'] as String?,
    netTripCharge: json['net_trip_charge'] as num?,
    providerUserId: json['provider_user_id'] as int?,
    isRemainingPaymentButtonEnabled:
        (json['is_remaining_payment_button_enabled'] as bool?) ?? false,
    isRefundSettlementPaymentButtonEnabled:
        (json['is_refund_settlement_payment_button_enabled'] as bool?) ?? false,
    bookingDetailId: json['booking_detail_id'] as String?,
    tripData: json['trip'] == null
        ? null
        : TripData.fromJson(json['trip'] as Map<String, dynamic>),
    providerChatRoomDetail: json['provider_chat_room_detail'] == null
        ? null
        : ChatRoomDetailModel.fromJson(
            json['provider_chat_room_detail'] as Map<String, dynamic>,
          ),
    driverChatRoomDetail: json['driver_chat_room_detail'] == null
        ? null
        : ChatRoomDetailModel.fromJson(
            json['driver_chat_room_detail'] as Map<String, dynamic>,
          ),
    sharedBookings: json['shared_bookings'] == null
        ? []
        : List<SharedBooking>.from(
            (json['shared_bookings'] as List<dynamic>).map(
              (x) => SharedBooking.fromJson(x as Map<String, dynamic>),
            ),
          ),
    notes: json['notes'] == null
        ? []
        : List<NoteModel>.from(
            (json['notes'] as List<dynamic>).map(
              (x) => NoteModel.fromJson(x as Map<String, dynamic>),
            ),
          ),
    ratings: json['ratings'] == null
        ? []
        : List<RatingModel>.from(
            (json['ratings'] as List<dynamic>).map(
              (x) => RatingModel.fromJson(x as Map<String, dynamic>),
            ),
          ),
    carDetails: json['booked_cars'] == null
        ? []
        : List<BookedCar>.from(
            (json['booked_cars'] as List<dynamic>).map(
              (x) => BookedCar.fromJson(x as Map<String, dynamic>),
            ),
          ),
    exclusiveBookings: json['exclusive_bookings'] == null
        ? []
        : List<ExclusiveBooking>.from(
            (json['exclusive_bookings'] as List<dynamic>).map(
              (x) => ExclusiveBooking.fromJson(x as Map<String, dynamic>),
            ),
          ),
    isBookingCancellable:
        json['is_booking_cancellable'] as bool? ?? false, // ✅ mapping
  );

  int? id;
  num? netTripCharge;
  String? status;
  String? bookingDetailId;
  int? providerUserId;
  List<SharedBooking>? sharedBookings;
  List<ExclusiveBooking>? exclusiveBookings;
  List<BookedCar>? carDetails;
  List<NoteModel>? notes;
  List<RatingModel>? ratings;
  TripData? tripData;
  ChatRoomDetailModel? providerChatRoomDetail;
  ChatRoomDetailModel? driverChatRoomDetail;
  bool isRemainingPaymentButtonEnabled;
  bool isRefundSettlementPaymentButtonEnabled;

  /// ✅ new field
  bool isBookingCancellable;

  Map<String, dynamic> toJson() => {
    'id': id,
    'status': status,
    'provider_user_id': providerUserId,
    'trip': tripData?.toJson(),
    'provider_chat_room_detail': providerChatRoomDetail?.toJson(),
    'driver_chat_room_detail': driverChatRoomDetail?.toJson(),
    'net_trip_charge': netTripCharge,
    'booking_detail_id': bookingDetailId,
    'is_remaining_payment_button_enabled': isRemainingPaymentButtonEnabled,
    'is_refund_settlement_payment_button_enabled':
        isRefundSettlementPaymentButtonEnabled,
    'shared_bookings': sharedBookings == null
        ? []
        : List<dynamic>.from(sharedBookings!.map((x) => x.toJson())),
    'exclusive_bookings': exclusiveBookings == null
        ? []
        : List<dynamic>.from(exclusiveBookings!.map((x) => x.toJson())),
    'booked_cars': carDetails == null
        ? []
        : List<dynamic>.from(carDetails!.map((x) => x.toJson())),
    'notes': notes == null
        ? []
        : List<dynamic>.from(notes!.map((x) => x.toJson())),
    'ratings': ratings == null
        ? []
        : List<dynamic>.from(ratings!.map((x) => x.toJson())),
    'is_booking_cancellable': isBookingCancellable, // ✅ added toJson
  };
}

class SharedBooking {
  SharedBooking({
    this.id,
    this.totalTripDistance,
    this.intermediateStartStopLocation,
    this.intermediateEndStopLocation,
    this.startLocationChatRoomDetail,
    this.endLocationChatRoomDetail,
  });

  factory SharedBooking.fromJson(Map<String, dynamic> json) => SharedBooking(
    id: json['id'] as int?,
    totalTripDistance: json['total_trip_distance'] as num?,
    intermediateStartStopLocation:
        json['intermediate_start_stop_location'] == null
        ? null
        : IntermediateStopLocation.fromJson(
            json['intermediate_start_stop_location'] as Map<String, dynamic>,
          ),
    intermediateEndStopLocation: json['intermediate_end_stop_location'] == null
        ? null
        : IntermediateStopLocation.fromJson(
            json['intermediate_end_stop_location'] as Map<String, dynamic>,
          ),
    endLocationChatRoomDetail:
        json['end_stop_location_chat_room_detail'] == null
        ? null
        : ChatRoomDetailModel.fromJson(
            json['end_stop_location_chat_room_detail'] as Map<String, dynamic>,
          ),
    startLocationChatRoomDetail:
        json['start_stop_location_chat_room_detail'] == null
        ? null
        : ChatRoomDetailModel.fromJson(
            json['start_stop_location_chat_room_detail']
                as Map<String, dynamic>,
          ),
  );
  int? id;
  num? totalTripDistance;
  IntermediateStopLocation? intermediateStartStopLocation;
  IntermediateStopLocation? intermediateEndStopLocation;
  ChatRoomDetailModel? startLocationChatRoomDetail;
  ChatRoomDetailModel? endLocationChatRoomDetail;

  Map<String, dynamic> toJson() => {
    'id': id,
    'total_trip_distance': totalTripDistance,
    'intermediate_start_stop_location': intermediateStartStopLocation?.toJson(),
    'intermediate_end_stop_location': intermediateEndStopLocation?.toJson(),
  };
}

class IntermediateStopLocation {
  IntermediateStopLocation({
    this.stopLocation,
    this.estimatedArrivalDate,
    this.stopLocationIndex,
    this.distance,
  });

  factory IntermediateStopLocation.fromJson(Map<String, dynamic> json) =>
      IntermediateStopLocation(
        stopLocation: json['stop_location'] as int?,
        estimatedArrivalDate: json['estimated_arrival_date'] == null
            ? null
            : DateTime.parse(json['estimated_arrival_date'] as String),
        stopLocationIndex: json['stop_location_index'] as int?,
        distance: json['distance'] as num?,
      );
  int? stopLocation;
  DateTime? estimatedArrivalDate;
  int? stopLocationIndex;
  num? distance;

  Map<String, dynamic> toJson() => {
    'stop_location': stopLocation,
    'estimated_arrival_date': estimatedArrivalDate?.toUtc().toIso8601String(),
    'stop_location_index': stopLocationIndex,
    'distance': distance,
  };
}

class TripStopLocation {
  TripStopLocation({
    this.id,
    this.name,
    this.adminUserId,
    this.address,
    this.storageCapacity,
  });

  factory TripStopLocation.fromJson(Map<String, dynamic> json) =>
      TripStopLocation(
        id: json['id'] as int?,
        name: json['name'] as String?,
        adminUserId: json['stop_admin_user_id'] as int?,
        address: json['address'] == null
            ? null
            : AddressModel.fromJson(json['address'] as Map<String, dynamic>),
        storageCapacity: json['storage_capacity'] as num?,
      );
  int? id;
  String? name;
  int? adminUserId;
  AddressModel? address;
  num? storageCapacity;

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'stop_admin_user_id': adminUserId,
    'address': address?.toJson(),
    'storage_capacity': storageCapacity,
  };
}

class NoteModel {
  NoteModel({this.id, this.description, this.bookingDetail, this.createdAt});

  factory NoteModel.fromJson(Map<String, dynamic> json) => NoteModel(
    id: json['id'] as int?,
    description: json['description'] as String?,
    bookingDetail: json['booking_detail'] as int?,
    createdAt: json['created_at'] == null
        ? null
        : DateTime.parse(json['created_at'] as String),
  );
  int? id;
  String? description;
  int? bookingDetail;
  DateTime? createdAt;

  Map<String, dynamic> toJson() => {
    'id': id,
    'description': description,
    'booking_detail': bookingDetail,
    'created_at': createdAt?.toUtc().toIso8601String(),
  };
}

class RatingModel {
  RatingModel({this.id, this.rating, this.suggestion});

  factory RatingModel.fromJson(Map<String, dynamic> json) => RatingModel(
    id: json['id'] as int?,
    rating: json['rating'] as num?,
    suggestion: json['suggestion'] as String?,
  );
  int? id;
  num? rating;
  String? suggestion;

  Map<String, dynamic> toJson() => {
    'id': id,
    'rating': rating,
    'suggestion': suggestion,
  };
}

class ExclusiveBooking {
  ExclusiveBooking({
    this.id,
    this.isOutForDelivery,
    this.outForDeliveryAt,
    this.exclusiveBookingId,
  });

  factory ExclusiveBooking.fromJson(Map<String, dynamic> json) =>
      ExclusiveBooking(
        id: json['id'] as int?,
        isOutForDelivery: json['is_out_for_delivery'] as bool?,
        outForDeliveryAt: json['out_for_delivery_at'] as String?,
        exclusiveBookingId: json['exclusive_booking_id'] as String?,
      );
  int? id;
  bool? isOutForDelivery;
  String? outForDeliveryAt;
  String? exclusiveBookingId;

  Map<String, dynamic> toJson() => {
    'id': id,
    'is_out_for_delivery': isOutForDelivery,
    'out_for_delivery_at': outForDeliveryAt,
    'exclusive_booking_id': exclusiveBookingId,
  };
}

class ChatRoomDetailModel {
  ChatRoomDetailModel({
    this.id,
    this.chatRoomId,
    this.isActive,
    this.unreadMessageCount,
  });

  factory ChatRoomDetailModel.fromJson(Map<String, dynamic> json) =>
      ChatRoomDetailModel(
        id: json['id'] as int?,
        chatRoomId: json['chat_room_id'] as String?,
        isActive: json['is_active'] as bool?,
        unreadMessageCount: json['unread_message_count'] as int?,
      );
  int? id;
  String? chatRoomId;
  bool? isActive;
  int? unreadMessageCount;

  Map<String, dynamic> toJson() => {
    'id': id,
    'chat_room_id': chatRoomId,
    'is_active': isActive,
    'unread_message_count': unreadMessageCount,
  };
}

class BookedCar {
  BookedCar({
    this.id,
    this.serialNumber,
    this.car,
    this.requiredWinch,
    this.isCarPickedUpToStopLocation,
    this.pickUpServiceAndDropOffServiceType,
    this.distanceFromCustomerLocationToStopLocation,
    this.isCarUpdated,
    this.carUpdatedAt,
    this.isPairable,
    this.isPaired,
    this.netCarCharge,
    this.carDescription,
    this.status,
    this.refundStatus,
    this.isPairedChargeSettled,
    this.bookedCarId,
    this.pickUpServiceAndDropOffService,
    this.fromCarToBePickedUpLocation,
    this.charges,
    this.images,
    this.isBookedCarCancellable = false, // ✅ new field with default false
  });

  factory BookedCar.fromJson(Map<String, dynamic> json) => BookedCar(
    id: json['id'] as int?,
    serialNumber: json['serial_number'] as String?,
    car: json['car'] == null
        ? null
        : CarDataModel.fromJson(json['car'] as Map<String, dynamic>),
    requiredWinch: json['required_winch'] as bool?,
    isCarPickedUpToStopLocation:
        json['is_car_picked_up_to_stop_location'] as bool?,
    pickUpServiceAndDropOffServiceType:
        json['pick_up_service_and_drop_off_service_type'] as String?,
    distanceFromCustomerLocationToStopLocation:
        json['distance_from_customer_location_to_stop_location'] as num?,
    isCarUpdated: json['is_car_updated'] as bool?,
    carUpdatedAt: json['car_updated_at'] as String?,
    isPairable: json['is_pairable'] as bool?,
    isPaired: json['is_paired'] as bool?,
    netCarCharge: json['net_car_charge'] as num?,
    carDescription: json['car_description'] as String?,
    status: json['status'] as String?,
    refundStatus: json['refund_status'] as String?,
    isPairedChargeSettled: json['is_paired_charge_settled'] as bool?,
    bookedCarId: json['booked_car_id'] as String?,
    pickUpServiceAndDropOffService:
        json['pick_up_service_and_drop_off_service'] == null
        ? null
        : ServiceTypeModel.fromJson(
            json['pick_up_service_and_drop_off_service']
                as Map<String, dynamic>,
          ),
    fromCarToBePickedUpLocation:
        json['from_car_to_be_picked_up_location'] == null
        ? null
        : AddressModel.fromJson(
            json['from_car_to_be_picked_up_location'] as Map<String, dynamic>,
          ),
    charges: json['charges'] == null
        ? []
        : List<ChargesModel>.from(
            (json['charges'] as List?)?.map(
                  (x) => ChargesModel.fromJson(x as Map<String, dynamic>),
                ) ??
                [],
          ),
    images: json['images'] == null
        ? null
        : (json['images'] as List)
              .map((e) => CarImageModel.fromJson(e as Map<String, dynamic>))
              .toList(),
    isBookedCarCancellable:
        json['is_booked_car_cancellable'] as bool? ??
        false, // ✅ mapping with default
  );

  int? id;
  String? serialNumber;
  CarDataModel? car;
  bool? requiredWinch;
  bool? isCarPickedUpToStopLocation;
  dynamic pickUpServiceAndDropOffServiceType;
  dynamic distanceFromCustomerLocationToStopLocation;
  bool? isCarUpdated;
  dynamic carUpdatedAt;
  bool? isPairable;
  bool? isPaired;
  num? netCarCharge;
  String? carDescription;
  String? status;
  dynamic refundStatus;
  bool? isPairedChargeSettled;
  String? bookedCarId;
  ServiceTypeModel? pickUpServiceAndDropOffService;
  AddressModel? fromCarToBePickedUpLocation;
  List<ChargesModel>? charges;
  List<CarImageModel>? images;

  /// ✅ new field
  bool isBookedCarCancellable;

  Map<String, dynamic> toJson() => {
    'id': id,
    'serial_number': serialNumber,
    'car': car?.toJson(),
    'required_winch': requiredWinch,
    'is_car_picked_up_to_stop_location': isCarPickedUpToStopLocation,
    'pick_up_service_and_drop_off_service_type':
        pickUpServiceAndDropOffServiceType,
    'distance_from_customer_location_to_stop_location':
        distanceFromCustomerLocationToStopLocation,
    'is_car_updated': isCarUpdated,
    'car_updated_at': carUpdatedAt,
    'is_pairable': isPairable,
    'is_paired': isPaired,
    'net_car_charge': netCarCharge,
    'car_description': carDescription,
    'status': status,
    'refund_status': refundStatus,
    'is_paired_charge_settled': isPairedChargeSettled,
    'booked_car_id': bookedCarId,
    'pick_up_service_and_drop_off_service': pickUpServiceAndDropOffService
        ?.toJson(),
    'from_car_to_be_picked_up_location': fromCarToBePickedUpLocation?.toJson(),
    'charges': charges == null
        ? []
        : List<dynamic>.from(charges!.map((x) => x.toJson())),
    if (images?.isNotEmpty ?? false)
      'images': List<dynamic>.from(images?.map((e) => e.toJson()) ?? []),
    'is_booked_car_cancellable': isBookedCarCancellable, // ✅ added toJson
  };
}

class StopDetailLocation {
  StopDetailLocation({this.id, this.stopLocation, this.perDayCharge});

  factory StopDetailLocation.fromJson(Map<String, dynamic> json) =>
      StopDetailLocation(
        id: json['id'] as int?,
        stopLocation: json['stop_location'] == null
            ? null
            : TripStopLocation.fromJson(
                json['stop_location'] as Map<String, dynamic>,
              ),
        perDayCharge: json['per_day_charge'] as num?,
      );
  int? id;
  TripStopLocation? stopLocation;
  num? perDayCharge;

  Map<String, dynamic> toJson() => {
    'id': id,
    'stop_location': stopLocation?.toJson(),
    'per_day_charge': perDayCharge,
  };
}

class TripData {
  TripData({
    this.id,
    this.provider,
    this.companyName,
    this.driverUserId,
    this.tripStartDate,
    this.tripEndDate,
    this.costPerKilometer,
    this.tripId,
    this.reportList,
    this.status,
  });

  factory TripData.fromJson(Map<String, dynamic> json) => TripData(
    id: json['id'] as int?,
    provider: json['provider'] as int?,
    companyName: json['company_name'] as String?,
    status: json['status'] as String?,
    driverUserId: json['driver_user_id'] as int?,
    tripStartDate: json['trip_start_date'] == null
        ? null
        : DateTime.parse(json['trip_start_date'] as String),
    tripEndDate: json['trip_end_date'] == null
        ? null
        : DateTime.parse(json['trip_end_date'] as String),
    costPerKilometer: json['cost_per_kilometer'] as num?,
    tripId: json['trip_id'] as String?,
    reportList: json['reports'] == null
        ? []
        : List<ReportModel>.from(
            (json['reports'] as List<dynamic>).map(
              (x) => ReportModel.fromJson(x as Map<String, dynamic>),
            ),
          ),
  );
  int? id;
  int? provider;
  String? companyName;
  int? driverUserId;
  DateTime? tripStartDate;
  DateTime? tripEndDate;
  num? costPerKilometer;
  String? tripId;
  String? status;
  List<ReportModel>? reportList;

  Map<String, dynamic> toJson() => {
    'id': id,
    'provider': provider,
    'company_name': companyName,
    'status': status,
    'trip_start_date': tripStartDate?.toUtc().toIso8601String(),
    'trip_end_date': tripEndDate?.toUtc().toIso8601String(),
    'cost_per_kilometer': costPerKilometer,
    'trip_id': tripId,
    'reports': reportList?.map((e) => e.toJson()).toList(),
  };
}

extension Add on TripStopLocation {
  String? get fullAddress {
    final addressVal = AppCommonFunctions.cleanUpAddress(
      '$name${AppCommonFunctions.addComma(address?.street)}'
      '${AppCommonFunctions.addComma(address?.city)}'
      '${AppCommonFunctions.addComma(address?.state)}'
      '${AppCommonFunctions.addComma(address?.postalCode)}',
    );
    return addressVal;
  }
}

class ReportModel {
  ReportModel({this.id, this.description, this.affectedTime});

  factory ReportModel.fromJson(Map<String, dynamic> json) => ReportModel(
    id: json['id'] as int?,
    description: json['description'] as String?,
    affectedTime: json['affected_time'] as String?,
  );

  int? id;
  String? description;
  String? affectedTime;
  Map<String, dynamic> toJson() => {
    'id': id,
    'description': description,
    'affected_time': affectedTime,
  };
}
