import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/db/app_db.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/models/trip_shipment_confirmation_params.dart';
import 'package:transport_match/presentation/modules/booking_module/provider/booking_provider.dart';
import 'package:transport_match/presentation/modules/booking_module/widgets/contact_detail_widget/contact_detail_widget.dart';
import 'package:transport_match/presentation/modules/my_trips_module/widgets/trip_vehicle.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_confirm_check_box.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';
import 'package:transport_match/widgets/location_info_widget.dart';
import 'package:transport_match/widgets/no_of_vehicle_widget.dart';

/// Shipment confirmation page
class TripShipmentConfirmationScreen extends StatelessWidget {
  /// Default constructor
  const TripShipmentConfirmationScreen({
    super.key,
    required this.tripShipmentConfirmationParams,
  });
  final TripShipmentConfirmationParams tripShipmentConfirmationParams;

  @override
  Widget build(BuildContext context) {
    final selectedTrip = tripShipmentConfirmationParams.selectedTrip;

    return ChangeNotifierProvider(
      create: (context) => BookingProvider(
        tripShipmentConfirmationParams.transporterDetailList,
        bookingSessionId: tripShipmentConfirmationParams.bookingSessionId,
      ),
      child: Builder(
        builder: (context) {
          final bookingProvider = context.read<BookingProvider>();
          return Scaffold(
            backgroundColor: AppColors.ffF8F9FA,
            appBar: CustomAppBar(title: context.l10n.shipmentConfirmation),
            body: ValueListenableBuilder(
              valueListenable: bookingProvider.isShowLoader,
              builder: (context, isShowLoader, child) {
                return AppLoader(isShowLoader: isShowLoader, child: child!);
              },
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: AppSize.appPadding),
                child: Column(
                  children: <Widget>[
                    Container(
                      padding: EdgeInsets.all(AppSize.h16),
                      margin: EdgeInsets.only(bottom: AppSize.h16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(AppSize.r8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        spacing: AppSize.h16,
                        children: [
                          /// Header row with "Your Shipment" and "Edit"
                          Text(
                            context.l10n.yourShipment,
                            style: context.textTheme.titleLarge,
                          ),

                          /// Number of total vehicles
                          NoOfVehicleWidget(
                            noOfVehicle:
                                selectedTrip?.carDetails?.length.toString() ??
                                '0',
                            titleSize: AppSize.sp16,
                            subTitleSize: AppSize.sp16,
                          ),

                          /// Locations and dates row
                          Builder(
                            builder: (context) {
                              final startAddress =
                                  selectedTrip?.userStartLocation;
                              final endAddress = selectedTrip?.userEndLocation;
                              return LocationInfoWidget(
                                /// user start and end location
                                startLatitude: startAddress?.latitude,
                                startLongitude: startAddress?.longitude,
                                endLatitude: endAddress?.latitude,
                                endLongitude: endAddress?.longitude,

                                ///date and place
                                startLocationTitle:
                                    selectedTrip?.userStartLocation?.city ??selectedTrip?.userStartLocation?.street ?? ''
                                    '',
                                startLocationDate:
                                    selectedTrip
                                        ?.customerStartDate
                                        ?.monthDateFormate ??
                                    '',
                                endLocationTitle:
                                    selectedTrip?.userEndLocation?.city ?? selectedTrip?.userEndLocation?.street ?? '',
                                endLocationDate:
                                    selectedTrip
                                        ?.customerEndDate
                                        ?.monthDateFormate ??
                                    '',
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                    ValueListenableBuilder(
                      valueListenable: bookingProvider.isDisclaimerAccepted,
                      builder: (context, isDisclaimerAccepted, child) {
                        return Row(
                          spacing: AppSize.w8,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: EdgeInsets.only(top: AppSize.h2),
                              child: AppConfirmCheckBox(
                                onSelectionChanged: ({required value}) =>
                                    bookingProvider.isDisclaimerAccepted.value =
                                        value,
                                value: isDisclaimerAccepted,
                              ),
                            ),
                            Flexible(
                              fit: FlexFit.tight,
                              child: RichText(
                                text: TextSpan(
                                  text: context.l10n.anyUndeclared,
                                  style: context.textTheme.bodyMedium?.copyWith(
                                    fontSize: AppSize.sp12,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  children: [
                                    TextSpan(
                                      text: context.l10n.withoutRefund,
                                      style: context.textTheme.bodyMedium
                                          ?.copyWith(
                                            fontSize: AppSize.sp12,
                                            fontWeight: FontWeight.w600,
                                          ),
                                    ),
                                    TextSpan(
                                      text: context.l10n.itIsUserResponsibility,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                    Gap(AppSize.h16),
                    const ContactDetailWidget(),
                    Column(
                      children: List.generate(
                        bookingProvider.transporterDetailList.value.length,
                        (index) {
                          final data = bookingProvider
                              .transporterDetailList
                              .value[index];
                          return Padding(
                            padding:  EdgeInsets.only(bottom: AppSize.h14),
                            child: TripVehiclesInfoWidget(
                              selectedTripData:
                                  tripShipmentConfirmationParams.selectedTrip,
                              bookingProvider: bookingProvider,
                              providerListData: tripShipmentConfirmationParams
                                  .providerList
                                  .firstWhere(
                                    (e) =>
                                        data.trip.toString() == e.id.toString(),
                                  ),
                              isTrip: true,
                              mainIndex: index,
                              onClose: () {},
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
            bottomNavigationBar: AppButton(
              text: context.l10n.proceedToPayment,
              onPressed: () {
                final userDetail =
                    Injector.instance<AppDB>().userModel?.user?.userDetailData;
                if (userDetail?.bookingNumber.isEmptyOrNull ?? true) {
                  context.l10n.pleaseEnterUrMobileNumber.showErrorAlert();
                  return;
                }
                bookingProvider.getPayment(
                  context,
                  selectedTrip: tripShipmentConfirmationParams.selectedTrip,
                );
              },
            ),
          );
        },
      ),
    );
  }
}
