import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/models/provider_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/req_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/shared/repositories/home_repository.dart';
import 'package:transport_match/shared/repositories/trip_repo.dart';
import 'package:transport_match/shared/rest_api/api_keys.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/logger.dart';

class TripTransporterListProvider extends ChangeNotifier {
  TripTransporterListProvider(BuildContext context, TripModel? tripData) {
    transporterDetailList.value.clear();
    if (tripData != null) {
      findTransporter(context, tripData: tripData);
    }
  }

  /// Refresh controller for pull to refresh
  final refresherController = EasyRefreshController();

  /// Flag to check if provider is closed
  bool _isClosed = false;

  /// Repository instance
  final homeRepository = Injector.instance<HomeRepository>();

  /// Loading state flag
  ValueNotifier<bool> isShowLoader = ValueNotifier(false);
  ValueNotifier<bool> isFindShowLoader = ValueNotifier(false);

  /// filter sheet variables
  ValueNotifier<int> team = ValueNotifier(0);
  ValueNotifier<String> rate = ValueNotifier('');
  ValueNotifier<bool> isLowest = ValueNotifier(false);
  ValueNotifier<bool> is2Days = ValueNotifier(false);
  ValueNotifier<bool> isWinch = ValueNotifier(false);
  ValueNotifier<bool> isWinchFix = ValueNotifier(false);
  ValueNotifier<bool> isException = ValueNotifier(false);
  ValueNotifier<bool> isExceptionFix = ValueNotifier(false);
  int totalAssignCar = 0;
  int assignCarValue = 0;

  final tripRepo = Injector.instance<TripRepository>();

  /// list for requested trip data
  TripModel? selectedTrip;

  ValueNotifier<List<ProviderListData>> providerList = ValueNotifier([]);

  /// Notify listeners if not closed
  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'notify error: $e'.logE;
    }
  }

  /// booking session id for temp slot
  String? bookingSessionId;

  final transporterDetailList = ValueNotifier<List<TransporterDetailModel>>([]);

  CancelToken? registerTempSlotSingleToken;

  /// Register temporary slot for a transporter
  /// [context] is the BuildContext
  /// [element] is the transporter detail model
  /// [isRemove] flag to remove slot
  /// [reqTripController] is the requestedTripsProvider instance
  Future<bool> registerTempSlotSingle(
    BuildContext context,
    TransporterDetailModel element, {
    bool isRemove = false,
    required TripTransporterListProvider reqTripController,
  }) async {
    if (_isClosed) return false;

    registerTempSlotSingleToken?.cancel();
    registerTempSlotSingleToken = CancelToken();

    try {
      if (bookingSessionId.isEmptyOrNull) {
        final sessionId = await createBookingSession(context);
        if (sessionId.isEmptyOrNull || _isClosed) return false;
      }
      var isDone = false;
      final dataList = <Map<String, dynamic>>[];

      var size = 0.0;
      for (final c in element.carDetails) {
        if (!isRemove) {
          size += c.carSize?.toDouble() ?? 0;
        }
      }
      final deviceId = await AppCommonFunctions.getDeviceId();
      if (_isClosed) return false;

      dataList.add({
        ApiKeys.slot: size,
        ApiKeys.trip: element.trip,
        ApiKeys.deviceId: deviceId,
        ApiKeys.bookingSessionId: bookingSessionId,
      });

      isShowLoader.value = true;
      for (final data in dataList) {
        if (_isClosed) return false;

        try {
          final response = await homeRepository.registerTempSlot(
            ApiRequest(
              path: EndPoints.registerTempSlot,
              data: data,
              cancelToken: registerTempSlotSingleToken,
            ),
          );
          response.when(
            success: (data) {
              if (_isClosed ||
                  (registerTempSlotSingleToken?.isCancelled ?? true)) {
                return false;
              }
              isDone = true;

              final min = data[ApiKeys.time];
              context.l10n
                  .pleaseAssignCarWithin(min.toString())
                  .showInfoAlert(duration: const Duration(seconds: 5));
              notify();
            },
            error: (error) {
              if (_isClosed ||
                  (registerTempSlotSingleToken?.isCancelled ?? true)) {
                bookingSessionId = null;
                return false;
              }
              error.message.showErrorAlert();
            },
          );
        } catch (e) {
          if (_isClosed || (registerTempSlotSingleToken?.isCancelled ?? true)) {
            return false;
          }
          isShowLoader.value = false;
          bookingSessionId = null;
          'registerTempSlotSingle error: $e'.logE;
          isDone = false;
          break;
        }
        isShowLoader.value = false;
      }
      return isDone;
    } catch (e) {
      'registerTempSlotSingle outer error: $e'.logE;
      return false;
    }
  }

  /// Create booking session
  /// [context] is the BuildContext
  CancelToken? createBookingSessionToken;

  Future<String?> createBookingSession(BuildContext context) async {
    if (_isClosed) return null;

    createBookingSessionToken?.cancel();
    createBookingSessionToken = CancelToken();
    String? sessionId;

    isShowLoader.value = true;
    try {
      final deviceId = await AppCommonFunctions.getDeviceId();
      if (_isClosed) return null;

      final response = await homeRepository.createBookingSession(
        ApiRequest(
          path: EndPoints.createBookingSession,
          data: {ApiKeys.deviceId: deviceId},
          cancelToken: createBookingSessionToken,
        ),
      );
      response.when(
        success: (data) {
          if (_isClosed || (createBookingSessionToken?.isCancelled ?? true)) {
            return null;
          }
          bookingSessionId = data[ApiKeys.sessionId] as String?;
          // AppNavigationService.pushNamed(context, const PaymentPage());
          sessionId = data[ApiKeys.sessionId] as String?;
          notify();
        },
        error: (error) {
          if (_isClosed || (createBookingSessionToken?.isCancelled ?? true)) {
            return null;
          }
          bookingSessionId = null;
          error.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (createBookingSessionToken?.isCancelled ?? true)) {
        return null;
      }
      bookingSessionId = null;
      'createBookingSession error: $e'.logE;
    }
    isShowLoader.value = false;
    return sessionId;
  }

  /// Change filter rate value
  /// [value] is the new rate value
  void changeFilterRate(String value) {
    if (_isClosed) return;

    if (value == rate.value) {
      rate.value = '';
    } else {
      rate.value = value;
    }
    notify();
  }

  /// Clear all filter values
  void clearFilter() {
    if (_isClosed) return;

    rate.value = '';
    is2Days.value = false;
    isLowest.value = false;
    isWinch.value = false;
    isException.value = false; //change by R
    team.value = 0;
    notify();
  }

  /// Find transporter for a trip
  /// [context] is the BuildContext
  /// [isFilter] flag for filtering
  /// [isWinch] flag for winch requirement
  /// [isFromHome] flag if coming from home
  /// [isPagination] flag for pagination
  /// [isWantShowLoader] flag to show loader
  /// [trip] is the trip model
  CancelToken? findTripToken;
  final findTransporterNextUrl = ValueNotifier<String?>(null);
  Future<void> findTransporter(
    BuildContext context, {
    bool isFilter = false,
    bool isWinch = false,
    bool isException = false,
    bool isFromHome = false,
    bool isPagination = false,
    bool isWantShowLoader = true,
    required TripModel tripData,
  }) async {
    if (_isClosed) return;

    '==>>>> ${team.value}'.logE;

    findTripToken?.cancel();
    findTripToken = CancelToken();
    if (isWantShowLoader) isFindShowLoader.value = true;
    if (isWinch) this.isWinch.value = isWinch;
    if (isFromHome) isWinchFix.value = isWinch;
    if (isException) this.isException.value = isException;
    if (isFromHome) isExceptionFix.value = isException;
    notify();
    try {
      final endPoint = EndPoints.findTransporterForExclusive(
        bookingId: tripData.id?.toString() ?? '',
        rating: rate.value,
        team: team.value == 0 ? null : team.value.toString(),
        time: is2Days.value ? is2Days.value.toString() : null,
        lowestPrice: isLowest.value ? isLowest.value.toString() : null,
        winch: isWinch ? 'true' : null,
        isExceptionsSupported: isException ? 'true' : null,
      );

      final response = await tripRepo.findTransporter(
        ApiRequest(
          path: isPagination
              ? findTransporterNextUrl.value ?? endPoint
              : endPoint,
          cancelToken: findTripToken,
        ),
      );
      isFindShowLoader.value = false;
      if (_isClosed) return;
      notify();
      response.when(
        success: (data) {
          if (_isClosed || (findTripToken?.isCancelled ?? true)) return;

          if (!isPagination) providerList.value.clear();
          providerList.value.addAll(data.results ?? []);
          findTransporterNextUrl.value = data.next;
          if (!isFilter) {
            clearSelectedVehicle();
            if (data.results?.isNotEmpty ?? false) {
              assignCarValue = 0;
              totalAssignCar = 0;
              // context.read<requestedTripsProvider>();
              selectTrip(tripData, context);
            } else {
              context.l10n.noTransporterFoundYet.showErrorAlert();
            }
          }
          notify();
        },
        error: (error) {
          if (_isClosed || (findTripToken?.isCancelled ?? true)) return;
          error.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (findTripToken?.isCancelled ?? true)) return;
      isFindShowLoader.value = false;
      notify();
      'findTransporter error: $e'.logE;
    }
  }

  /// Select trip and navigate to requested trip screen
  /// [tripData] is the selected trip model
  /// [context] is the BuildContext
  void selectTrip(TripModel tripData, BuildContext context) {
    if (_isClosed) return;

    try {
      selectedTrip = TripModel.fromJson(tripData.toJson());
    } catch (e) {
      'selectTrip error: $e'.logE;
    }
  }

  /// below function is use to assign isAssignedCar
  /// variable to true if that car is selected
  void assignCar(List<CarDetailModel> selectedCarList) {
    if (_isClosed || selectedTrip?.carDetails == null) return;
    for (final car in selectedCarList) {
      final index = selectedTrip?.carDetails?.indexWhere(
        (e) => e.car?.id == car.car,
      );
      if (index != -1 && index != null) {
        selectedTrip?.carDetails?[index].isCarAssigned = ValueNotifier(true);
      }
    }
    notify();
  }

  /// below function is use for clear all selected vehicle
  /// isAssignedCar assign by default as false
  void clearSelectedVehicle() {
    if (_isClosed || selectedTrip?.carDetails == null) return;
    for (var i = 0; i < (selectedTrip?.carDetails?.length ?? 0); i++) {
      selectedTrip?.carDetails?[i].isCarAssigned = ValueNotifier(false);
    }
  }

  /// below function is use to disable provider based
  /// on the user' selected vehicles, number of assigned
  /// vehicle, remain vehicle sizes, vehicle' winch and exception support.
  (bool, String) disableProvider(
    BuildContext context,
    ProviderListData data, {
    required bool isSelected,
  }) {
    var isDisabled = false;
    var message = '';

    /// get remaining vehicle list
    final remainVehicleList =
        selectedTrip?.carDetails
            ?.where((e) => !(e.isCarAssigned?.value ?? false))
            .toList() ??
        [];

    /// disable provider based on the vehicle and provider winch required
    if (!data.isWinch &&
        !remainVehicleList.any((element) => !element.isWinchRequired) &&
        !isSelected) {
      isDisabled = true;

      message = context.l10n.transporterDoesNotHave;
    }

    // /// disable provider based on the vehicle and provider exception support
    // if (!data.isExceptionsSupported &&
    //     !remainVehicleList.any(
    //       (element) => !(element.selectedCarModel?.sizes?.exceptions ?? true),
    //     ) &&
    //     !isSelected) {
    //   isDisabled = true;

    //   message = context.l10n.transporterDoesNotMeet;
    // }

    /// disable provider based on the vehicle and provider available size
    if (!remainVehicleList.any((vehicle) {
          return (vehicle.car?.size ?? 0) <= (data.availableSlot ?? 0);
        }) &&
        !isSelected) {
      isDisabled = true;

      message = context.l10n.noAvailableSlot;
    }

    /// disable provider based on the user selected vehicle
    if (totalAssignCar == (selectedTrip?.carDetails?.length ?? 0) &&
        !isSelected) {
      isDisabled = true;
      message = context.l10n.allVehicleAreAlready;
    }
    return (isDisabled, message);
  }

  @override
  void dispose() {
    _isClosed = true;
    isShowLoader.dispose();
    registerTempSlotSingleToken?.cancel();
    createBookingSessionToken?.cancel();
    findTripToken?.cancel();
    refresherController.dispose();
    is2Days.dispose();
    isLowest.dispose();
    isWinch.dispose();
    rate.dispose();
    team.dispose();
    isException.dispose();
    isWinchFix.dispose();
    isExceptionFix.dispose();
    super.dispose();
  }
}
