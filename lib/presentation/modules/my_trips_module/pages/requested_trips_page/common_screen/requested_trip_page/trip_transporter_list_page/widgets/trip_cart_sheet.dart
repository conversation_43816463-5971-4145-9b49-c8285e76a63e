import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/models/provider_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/req_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/models/trip_data_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/requested_trips_page/common_screen/requested_trip_page/trip_transporter_list_page/provider/trip_transporter_list_provider.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/add_to_car_widget.dart';
import 'package:transport_match/widgets/app_loader.dart';

/// Open add to cart sheet
Future<void> openTripCartSheet(
  BuildContext context,
  ProviderListData providerListData,
  TripTransporterListProvider tripTransporterListProvider, {
  bool isTrip = false,
}) async {
  return showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(AppSize.r20),
        topRight: Radius.circular(AppSize.r20),
      ),
    ),
    builder: (context) {
      return _AddToCarSheet(
        providerListData: providerListData,
        tripTransporterListProvider: tripTransporterListProvider,
        isTrip: isTrip,
      );
    },
  );
}

class _AddToCarSheet extends StatefulWidget {
  const _AddToCarSheet({
    required this.providerListData,
    required this.tripTransporterListProvider,
    required this.isTrip,
  });

  final TripTransporterListProvider tripTransporterListProvider;
  final ProviderListData providerListData;
  final bool isTrip;

  @override
  State<_AddToCarSheet> createState() => _AddToCarSheetState();
}

class _AddToCarSheetState extends State<_AddToCarSheet> {
  final carList = <CarDetailModel>[];
  final slotList = <String>[];
  final dummyVehicle = <VehicleInfoModel>[];

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      // widget.tripTransporterListProvider.assignCarValue =
      //     widget.tripTransporterListProvider.totalAssignCar;
      for (
        var i = 0;
        i <
            (widget
                    .tripTransporterListProvider
                    .selectedTrip
                    ?.carDetails
                    ?.length ??
                0);
        i++
      ) {
        final e =
            widget.tripTransporterListProvider.selectedTrip?.carDetails?[i];
        final index = widget
            .tripTransporterListProvider
            .transporterDetailList
            .value
            .indexWhere(
              (e) =>
                  e.carDetails.any((v) => v.selectedIndex == i) &&
                  e.trip != widget.providerListData.id,
            );

        e?.assignIndex = null;
        if (index == -1 && e != null) {
          dummyVehicle.add(e);
        }
      }
      if (widget
          .tripTransporterListProvider
          .transporterDetailList
          .value
          .isNotEmpty) {
        final index = widget
            .tripTransporterListProvider
            .transporterDetailList
            .value
            .indexWhere((e) => e.trip == widget.providerListData.id);
        if (index != -1) {
          carList.addAll(
            widget
                .tripTransporterListProvider
                .transporterDetailList
                .value[index]
                .carDetails,
          );
        }
      }
      if (carList.isEmpty) {
        slotList.addAll(
          List.generate(
            widget.tripTransporterListProvider.assignCarValue,
            (index) => '',
          ),
        );
      } else {
        for (final element in carList) {
          slotList.add(element.carName ?? '');
        }
      }
      setState(() {});
      Future.delayed(Durations.short1, () => setState(() {}));
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final list = dummyVehicle.where(
      (e) =>
          (e.car?.size ?? 0) <= (widget.providerListData.availableSlot ?? 0) &&
          (widget.providerListData.isWinch ||
              e.isWinchRequired == widget.providerListData.isWinch),
    );
    return ValueListenableBuilder(
      valueListenable: widget.tripTransporterListProvider.isShowLoader,
      builder: (context, loading, child) {
        final tripTransporterProvider = widget.tripTransporterListProvider;
        return AppLoader(
          isShowLoader: loading,
          // isFullScreen: false,
          child: AddToCarWidget(
            providerData: widget.providerListData,
            slotList: slotList,
            isTrip: widget.isTrip,
            items: (index) => List.generate(list.length, (i) {
              final e = list.toList()[i];
              return GestureDetector(
                onTap: () {
                  if (carList.any((e) => e.selectedIndex == i)) {
                    context.l10n.uAlreadyAssign.showErrorAlert();
                  } else {
                    final indexes = dummyVehicle.indexWhere(
                      (e1) => e1.car?.model == e.car?.model,
                    );
                    final vehicle = dummyVehicle[indexes];
                    if (carList.length == index + 1) {
                      carList.removeAt(index);
                    }

                    for (var i = 0; i < dummyVehicle.length; i++) {
                      if (dummyVehicle[i].assignIndex == index) {
                        dummyVehicle[i].assignIndex = null;
                      }
                    }

                    dummyVehicle[indexes].assignIndex = index;
                    slotList[index] = vehicle.car?.model ?? '';
                    carList.insert(
                      index,
                      CarDetailModel(
                        selectedIndex: i,
                        serialNumber: vehicle.serialNumber?.nullCheck,
                        car: vehicle.car?.id ?? 0,
                        carName: vehicle.car?.model ?? '',
                        brand: vehicle.car?.id?.toString(),
                        year: vehicle.car?.id?.toString(),
                        carSize: vehicle.car?.size ?? 1,
                        // pickUpServiceAndDropOffService:
                        //     widget.controller.costId.value,
                        // fromCarToBePickedUpLocation:
                        //     widget.controller.pickUpAddress.value,
                        totalDistanceFromCustomerLocationToStopLocation: 10,
                        // isWinchRequired: widget.controller.winchRequired.value,
                        carDescription: vehicle.carDescription?.nullCheck,
                      ),
                    );

                        setState(() {});
                        AppNavigationService.pop(context);
                      }
                    },
                    child: Container(
                      margin: EdgeInsets.only(bottom: AppSize.h10),
                      padding: EdgeInsets.all(AppSize.sp10),
                      decoration: BoxDecoration(
                        color: carList.any((e) => e.selectedIndex == i)?AppColors.greyColor.withValues(alpha: 0.2) : AppColors.white,
                        borderRadius: BorderRadius.circular(AppSize.r8),
                        border: Border.all(color: AppColors.ffDEE2E6),
                      ),
                      child: Row(
                        children: [
                          Text(
                            '${e.car?.model ?? ''} (${e.car?.year ?? ''})',
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
                isAddMoreBtn: slotList.length == list.length || list.isEmpty,
                onSlotRemove: (index) {
                  setState(() {
                    if (index < carList.length) {
                      dummyVehicle
                              .firstWhere((e) => e.car?.model == slotList[index])
                              .assignIndex =
                          null;
                      carList.removeAt(index);
                    }
                    if (slotList.length == 1) {
                      slotList[index] = '';
                    } else {
                      slotList.removeAt(index);
                    }
                  });
                  if (slotList.length == 1) {
                    widget.tripTransporterListProvider
                      ..assignCarValue -= 1
                      ..notify();
                  }
                },
                onAddMoreCar: () {
                  if (carList.length == slotList.length) {
                    num totalSize = 0;
                    for (final e in carList) {
                      totalSize += e.carSize ?? 0;
                    }
                    var isSlotExist = true;
                    final dummyList = dummyVehicle.where(
                      (e) => e.assignIndex == null,
                    );

                    isSlotExist = dummyList.any((e) {
                      return totalSize + (e.car?.size ?? 0) <=
                          (widget.providerListData.availableSlot ?? 0);
                    });
                    if (isSlotExist) {
                      widget.tripTransporterListProvider
                        ..assignCarValue += 1
                        ..notify();
                      setState(() {
                        slotList.add('');
                      });
                    } else {
                      context.l10n.noSlotAvailable.showErrorAlert();
                    }
                  } else {
                    '${context.l10n.pleaseAssignCar} ${slotList.length}'
                        .showErrorAlert();
                  }
                },
                onSaveBtn: () {
                  final temptTransporterDetailList = [
                    ...tripTransporterProvider.transporterDetailList.value,
                  ];
                  if (carList.length == slotList.length) {
                    final index = temptTransporterDetailList.indexWhere(
                      (e) => e.trip == widget.providerListData.id,
                    );
                    final data = TransporterDetailModel(
                      trip: widget.providerListData.id ?? 0,
                      carDetails: carList,
                      providerPickUpLocation:
                          widget
                              .providerListData
                              .customerStartStopLocation
                              ?.estimatedArrivalDate ??
                          DateTime.now(),
                    );
                    if (carList.isEmpty) {
                      if (index != -1) {
                        temptTransporterDetailList.removeAt(index);
                      }
                    } else {
                      if (index != -1) {
                        temptTransporterDetailList
                          ..removeAt(index)
                          ..insert(index, data);
                      } else {
                        temptTransporterDetailList.add(data);
                      }
                    }
                    tripTransporterProvider
                        .registerTempSlotSingle(
                          context,
                          data,
                          reqTripController: widget.tripTransporterListProvider,
                        )
                        .then((value) {
                          if (value) {
                            tripTransporterProvider.transporterDetailList.value
                                .clear();
                            tripTransporterProvider.transporterDetailList.value
                                .addAll(temptTransporterDetailList);
                            widget.tripTransporterListProvider
                              ..totalAssignCar = 0
                              ..clearSelectedVehicle();
                            for (final e
                                in tripTransporterProvider
                                    .transporterDetailList
                                    .value) {
                              widget.tripTransporterListProvider
                                ..totalAssignCar += e.carDetails.length
                                ..assignCar(e.carDetails);
                            }
                            widget.tripTransporterListProvider.notify();
                            tripTransporterProvider.notify();
                            // ignore: use_build_context_synchronously
                            if (context.mounted) AppNavigationService.pop(context);
                          }
                        });
                  } else {
                    if (slotList.length == 1) {
                      final index = temptTransporterDetailList.indexWhere(
                        (e) => e.trip == widget.providerListData.id,
                      );
                      if (index != -1) {
                        tripTransporterProvider
                            .registerTempSlotSingle(
                              context,
                              temptTransporterDetailList[index],
                              isRemove: true,
                              reqTripController: widget.tripTransporterListProvider,
                            )
                            .then((value) {
                              if (value) {
                                tripTransporterProvider.transporterDetailList.value
                                    .removeAt(index);
                                widget.tripTransporterListProvider
                                  ..totalAssignCar = 0
                                  ..clearSelectedVehicle();
                                for (final e
                                    in tripTransporterProvider
                                        .transporterDetailList
                                        .value) {
                                  widget.tripTransporterListProvider
                                    ..totalAssignCar += e.carDetails.length
                                    ..assignCar(e.carDetails);
                                }
                                widget.tripTransporterListProvider.notify();
                                tripTransporterProvider.notify();
                                if (context.mounted) {
                                  // ignore: use_build_context_synchronously
                                  AppNavigationService.pop(context);
                                }
                              }
                            });
                      }
                    } else {
                      context.l10n.pleaseChooseCar.showErrorAlert();
                    }
                  }
                },
              ),
            );
      },
    );
  }
}
