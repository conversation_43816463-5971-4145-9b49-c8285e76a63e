// class PaymentSummaryData {
//   PaymentSummaryData({
//     this.transporter,
//     this.totalTransportationCharge,
//     this.totalStartLocationStorageCharge,
//     this.totalEndLocationStorageCharge,
//     this.totalCustomerLocationToStartStopLocationServiceCharge,
//     this.totalInsuranceCharge,
//     this.totalPairingCharge,
//     this.totalAppFee,
//     this.paidAmount,
//     this.remainingBookingAmount,
//     this.totalBookingCost,
//   });

//   factory PaymentSummaryData.fromJson(Map<String, dynamic> json) =>
//       PaymentSummaryData(
//         transporter: json['transporter'] == null
//             ? []
//             : List<TransporterModel>.from(
//                 (json['transporter'] as List?)?.map(
//                       (x) =>
//                           TransporterModel.fromJson(x as Map<String, dynamic>),
//                     ) ??
//                     [],
//               ),
//         totalTransportationCharge: json['total_transportation_charge'] as num?,
//         totalStartLocationStorageCharge:
//             json['total_start_location_storage_charge'] as num?,
//         totalEndLocationStorageCharge:
//             json['total_end_location_storage_charge'] as num?,
//         totalCustomerLocationToStartStopLocationServiceCharge: json[
//                 'total_customer_location_to_start_stop_location_service_charge']
//             as num?,
//         totalInsuranceCharge: json['total_insurance_charge'] as num?,
//         totalPairingCharge: json['total_pairing_charge'] as num?,
//         totalAppFee: json['total_app_fee'] as num?,
//         paidAmount: json['paid_amount'] as num?,
//         remainingBookingAmount: json['remaining_booking_amount'] as num?,
//         totalBookingCost: json['total_booking_cost'] as num?,
//       );
//   List<TransporterModel>? transporter;
//   num? totalTransportationCharge;
//   num? totalStartLocationStorageCharge;
//   num? totalEndLocationStorageCharge;
//   num? totalCustomerLocationToStartStopLocationServiceCharge;
//   num? totalInsuranceCharge;
//   num? totalPairingCharge;
//   num? totalAppFee;
//   num? paidAmount;
//   num? remainingBookingAmount;
//   num? totalBookingCost;

//   Map<String, dynamic> toJson() => {
//         'transporter': transporter == null
//             ? []
//             : List<dynamic>.from(transporter!.map((x) => x.toJson())),
//         'total_transportation_charge': totalTransportationCharge,
//         'total_start_location_storage_charge': totalStartLocationStorageCharge,
//         'total_end_location_storage_charge': totalEndLocationStorageCharge,
//         'total_customer_location_to_start_stop_location_service_charge':
//             totalCustomerLocationToStartStopLocationServiceCharge,
//         'total_insurance_charge': totalInsuranceCharge,
//         'total_pairing_charge': totalPairingCharge,
//         'total_app_fee': totalAppFee,
//         'paid_amount': paidAmount,
//         'remaining_booking_amount': remainingBookingAmount,
//         'total_booking_cost': totalBookingCost,
//       };
// }


class PaymentSummaryData {
  PaymentSummaryData({
    this.bookingType,
    this.transporter,
    this.totalTransportationCharge,
    this.totalStartLocationStorageCharge,
    this.totalEndLocationStorageCharge,
    this.totalCustomerLocationToStartStopLocationServiceCharge,
    this.totalInsuranceCharge,
    this.totalAppFee,
    this.paidAmount,
    this.paidAmountWithoutTax,
    this.remainingBookingAmountWithoutTax,
    this.totalBookingCostWithoutTax,
    this.taxAmount,
    this.taxRate,
    this.netTotalWithTax,
    this.refundAmount,
    this.refundPenalty,
    this.actualRefundAmount,
    this.refundTaxAmount,
    this.actualRefundAmountWithTax,
    this.extraCharges,
    this.extraChargesTaxAmount,
    this.amountDueAfterRefundSettlement,
    this.taxAmountDueAfterRefundSettlement,
    this.actualRefundAgainstSettlement,
    this.actualRefundTaxAgainstSettlement,
  });

  factory PaymentSummaryData.fromJson(Map<String, dynamic> json) =>
      PaymentSummaryData(
        bookingType: json['booking_type'] as String?,
        transporter: json['transporter'] == null
            ? []
            : List<TransporterModel>.from(
                (json['transporter'] as List).map(
                  (x) => TransporterModel.fromJson(
                    x as Map<String, dynamic>,
                  ),
                ),
              ),
        totalTransportationCharge:
            json['total_transportation_charge'] as num?,
        totalStartLocationStorageCharge:
            json['total_start_location_storage_charge'] as num?,
        totalEndLocationStorageCharge:
            json['total_end_location_storage_charge'] as num?,
        totalCustomerLocationToStartStopLocationServiceCharge: json[
                'total_customer_location_to_start_stop_location_service_charge']
            as num?,
        totalInsuranceCharge: json['total_insurance_charge'] as num?,
        totalAppFee: json['total_app_fee'] as num?,
        paidAmount: json['paid_amount'] as num?,
        paidAmountWithoutTax: json['paid_amount_without_tax'] as num?,
        remainingBookingAmountWithoutTax:
            json['remaining_booking_amount_without_tax'] as num?,
        totalBookingCostWithoutTax:
            json['total_booking_cost_without_tax'] as num?,
        taxAmount: json['tax_amount'] as num?,
        taxRate: json['tax_rate'] as num?,
        netTotalWithTax: json['net_total_with_tax'] as num?,
        refundAmount: json['refund_amount'] as num?,
        refundPenalty: json['refund_penalty'] as num?,
        actualRefundAmount: json['actual_refund_amount'] as num?,
        refundTaxAmount: json['refund_tax_amount'] as num?,
        actualRefundAmountWithTax:
            json['actual_refund_amount_with_tax'] as num?,
        extraCharges: json['extra_charges'] as num?,
        extraChargesTaxAmount: json['extra_charges_tax_amount'] as num?,
        amountDueAfterRefundSettlement:
            json['amount_due_after_refund_settlement'] as num?,
        taxAmountDueAfterRefundSettlement:
            json['tax_amount_due_after_refund_settlement'] as num?,
        actualRefundAgainstSettlement:
            json['actual_refund_against_settlement'] as num?,
        actualRefundTaxAgainstSettlement:
            json['actual_refund_tax_against_settlement'] as num?,
      );

  String? bookingType;
  List<TransporterModel>? transporter;
  num? totalTransportationCharge;
  num? totalStartLocationStorageCharge;
  num? totalEndLocationStorageCharge;
  num? totalCustomerLocationToStartStopLocationServiceCharge;
  num? totalInsuranceCharge;
  num? totalAppFee;
  num? paidAmount;
  num? paidAmountWithoutTax;
  num? remainingBookingAmountWithoutTax;
  num? totalBookingCostWithoutTax;
  num? taxAmount;
  num? taxRate;
  num? netTotalWithTax;
  num? refundAmount;
  num? refundPenalty;
  num? actualRefundAmount;
  num? refundTaxAmount;
  num? actualRefundAmountWithTax;
  num? extraCharges;
  num? extraChargesTaxAmount;
  num? amountDueAfterRefundSettlement;
  num? taxAmountDueAfterRefundSettlement;
  num? actualRefundAgainstSettlement;
  num? actualRefundTaxAgainstSettlement;

  Map<String, dynamic> toJson() => {
        'booking_type': bookingType,
        'transporter': transporter == null
            ? []
            : List<dynamic>.from(transporter!.map((x) => x.toJson())),
        'total_transportation_charge': totalTransportationCharge,
        'total_start_location_storage_charge': totalStartLocationStorageCharge,
        'total_end_location_storage_charge': totalEndLocationStorageCharge,
        'total_customer_location_to_start_stop_location_service_charge':
            totalCustomerLocationToStartStopLocationServiceCharge,
        'total_insurance_charge': totalInsuranceCharge,
        'total_app_fee': totalAppFee,
        'paid_amount': paidAmount,
        'paid_amount_without_tax': paidAmountWithoutTax,
        'remaining_booking_amount_without_tax':
            remainingBookingAmountWithoutTax,
        'total_booking_cost_without_tax': totalBookingCostWithoutTax,
        'tax_amount': taxAmount,
        'tax_rate': taxRate,
        'net_total_with_tax': netTotalWithTax,
        'refund_amount': refundAmount,
        'refund_penalty': refundPenalty,
        'actual_refund_amount': actualRefundAmount,
        'refund_tax_amount': refundTaxAmount,
        'actual_refund_amount_with_tax': actualRefundAmountWithTax,
        'extra_charges': extraCharges,
        'extra_charges_tax_amount': extraChargesTaxAmount,
        'amount_due_after_refund_settlement': amountDueAfterRefundSettlement,
        'tax_amount_due_after_refund_settlement':
            taxAmountDueAfterRefundSettlement,
        'actual_refund_against_settlement': actualRefundAgainstSettlement,
        'actual_refund_tax_against_settlement':
            actualRefundTaxAgainstSettlement,
      };
}


// class TransporterModel {
//   TransporterModel({
//     this.id,
//     this.bookingDetailId,
//     this.transportationCharge,
//     this.startStopLocationStorageCharge,
//     this.endStopLocationStorageCharge,
//     this.customerLocationToStartLocationServiceCharge,
//     this.insuranceCharge,
//     this.matchedPairCharge,
//     this.paidAmount,
//     this.remainingBookingDetailAmount,
//     this.totalTripCost,
//     this.totalAppFee,
//   });

//   factory TransporterModel.fromJson(Map<String, dynamic> json) =>
//       TransporterModel(
//         id: json['id'] as int?,
//         bookingDetailId: json['booking_detail_id'] as String?,
//         transportationCharge: json['transportation_charge'] as num?,
//         startStopLocationStorageCharge:
//             json['start_stop_location_storage_charge'] as num?,
//         endStopLocationStorageCharge:
//             json['end_stop_location_storage_charge'] as num?,
//         customerLocationToStartLocationServiceCharge:
//             json['customer_location_to_start_location_service_charge'] as num?,
//         insuranceCharge: json['inurance_charge'] as num?,
//         matchedPairCharge: json['matched_pair_charge'] as num?,
//         paidAmount: json['paid_amount'] as num?,
//         remainingBookingDetailAmount:
//             json['remaining_booking_detail_amount'] as num?,
//         totalTripCost: json['total_trip_cost'] as num?,
//         totalAppFee: json['total_app_fee'] as num?,
//       );
//   int? id;
//   String? bookingDetailId;
//   num? transportationCharge;
//   num? startStopLocationStorageCharge;
//   num? totalAppFee;
//   num? endStopLocationStorageCharge;
//   num? customerLocationToStartLocationServiceCharge;
//   num? insuranceCharge;
//   num? matchedPairCharge;
//   num? paidAmount;
//   num? remainingBookingDetailAmount;
//   num? totalTripCost;

//   Map<String, dynamic> toJson() => {
//         'id': id,
//         'total_app_fee': totalAppFee,
//         'booking_detail_id': bookingDetailId,
//         'transportation_charge': transportationCharge,
//         'start_stop_location_storage_charge': startStopLocationStorageCharge,
//         'end_stop_location_storage_charge': endStopLocationStorageCharge,
//         'customer_location_to_start_location_service_charge':
//             customerLocationToStartLocationServiceCharge,
//         'inurance_charge': insuranceCharge,
//         'matched_pair_charge': matchedPairCharge,
//         'paid_amount': paidAmount,
//         'remaining_booking_detail_amount': remainingBookingDetailAmount,
//         'total_trip_cost': totalTripCost,
//       };
// }


class TransporterModel {
  TransporterModel({
    this.bookingType,
    this.bookingDetailId,
    this.transportationCharge,
    this.startStopLocationStorageCharge,
    this.endStopLocationStorageCharge,
    this.customerLocationToStartLocationServiceCharge,
    this.insuranceCharge,
    this.appFee,
    this.paidAmount,
    this.paidAmountWithoutTax,
    this.totalTripCostWithoutTax,
    this.remainingBookingDetailAmountWithoutTax,
    this.taxAmount,
    this.taxRate,
    this.netTotalWithTax,
    this.refundAmount,
    this.refundPenalty,
    this.actualRefundAmount,
    this.refundTaxAmount,
    this.actualRefundAmountWithTax,
    this.extraCharges,
    this.extraChargesTaxAmount,
    this.amountDueAfterRefundSettlement,
    this.taxAmountDueAfterRefundSettlement,
    this.actualRefundAgainstSettlement,
    this.actualRefundTaxAgainstSettlement,
  });

  factory TransporterModel.fromJson(Map<String, dynamic> json) =>
      TransporterModel(
        bookingType: json['booking_type'] as String?,
        bookingDetailId: json['booking_detail_id'] as String?,
        transportationCharge: json['transportation_charge'] as num?,
        startStopLocationStorageCharge:
            json['start_stop_location_storage_charge'] as num?,
        endStopLocationStorageCharge:
            json['end_stop_location_storage_charge'] as num?,
        customerLocationToStartLocationServiceCharge:
            json['customer_location_to_start_location_service_charge'] as num?,
        insuranceCharge: json['insurance_charge'] as num?,
        appFee: json['app_fee'] as num?,
        paidAmount: json['paid_amount'] as num?,
        paidAmountWithoutTax: json['paid_amount_without_tax'] as num?,
        totalTripCostWithoutTax: json['total_trip_cost_without_tax'] as num?,
        remainingBookingDetailAmountWithoutTax:
            json['remaining_booking_detail_amount_without_tax'] as num?,
        taxAmount: json['tax_amount'] as num?,
        taxRate: json['tax_rate'] as num?,
        netTotalWithTax: json['net_total_with_tax'] as num?,
        refundAmount: json['refund_amount'] as num?,
        refundPenalty: json['refund_penalty'] as num?,
        actualRefundAmount: json['actual_refund_amount'] as num?,
        refundTaxAmount: json['refund_tax_amount'] as num?,
        actualRefundAmountWithTax:
            json['actual_refund_amount_with_tax'] as num?,
        extraCharges: json['extra_charges'] as num?,
        extraChargesTaxAmount: json['extra_charges_tax_amount'] as num?,
        amountDueAfterRefundSettlement:
            json['amount_due_after_refund_settlement'] as num?,
        taxAmountDueAfterRefundSettlement:
            json['tax_amount_due_after_refund_settlement'] as num?,
        actualRefundAgainstSettlement:
            json['actual_refund_against_settlement'] as num?,
        actualRefundTaxAgainstSettlement:
            json['actual_refund_tax_against_settlement'] as num?,
      );

  String? bookingType;
  String? bookingDetailId;
  num? transportationCharge;
  num? startStopLocationStorageCharge;
  num? endStopLocationStorageCharge;
  num? customerLocationToStartLocationServiceCharge;
  num? insuranceCharge;
  num? appFee;
  num? paidAmount;
  num? paidAmountWithoutTax;
  num? totalTripCostWithoutTax;
  num? remainingBookingDetailAmountWithoutTax;
  num? taxAmount;
  num? taxRate;
  num? netTotalWithTax;
  num? refundAmount;
  num? refundPenalty;
  num? actualRefundAmount;
  num? refundTaxAmount;
  num? actualRefundAmountWithTax;
  num? extraCharges;
  num? extraChargesTaxAmount;
  num? amountDueAfterRefundSettlement;
  num? taxAmountDueAfterRefundSettlement;
  num? actualRefundAgainstSettlement;
  num? actualRefundTaxAgainstSettlement;

  Map<String, dynamic> toJson() => {
        'booking_type': bookingType,
        'booking_detail_id': bookingDetailId,
        'transportation_charge': transportationCharge,
        'start_stop_location_storage_charge': startStopLocationStorageCharge,
        'end_stop_location_storage_charge': endStopLocationStorageCharge,
        'customer_location_to_start_location_service_charge':
            customerLocationToStartLocationServiceCharge,
        'insurance_charge': insuranceCharge,
        'app_fee': appFee,
        'paid_amount': paidAmount,
        'paid_amount_without_tax': paidAmountWithoutTax,
        'total_trip_cost_without_tax': totalTripCostWithoutTax,
        'remaining_booking_detail_amount_without_tax':
            remainingBookingDetailAmountWithoutTax,
        'tax_amount': taxAmount,
        'tax_rate': taxRate,
        'net_total_with_tax': netTotalWithTax,
        'refund_amount': refundAmount,
        'refund_penalty': refundPenalty,
        'actual_refund_amount': actualRefundAmount,
        'refund_tax_amount': refundTaxAmount,
        'actual_refund_amount_with_tax': actualRefundAmountWithTax,
        'extra_charges': extraCharges,
        'extra_charges_tax_amount': extraChargesTaxAmount,
        'amount_due_after_refund_settlement': amountDueAfterRefundSettlement,
        'tax_amount_due_after_refund_settlement':
            taxAmountDueAfterRefundSettlement,
        'actual_refund_against_settlement': actualRefundAgainstSettlement,
        'actual_refund_tax_against_settlement':
            actualRefundTaxAgainstSettlement,
      };
}
