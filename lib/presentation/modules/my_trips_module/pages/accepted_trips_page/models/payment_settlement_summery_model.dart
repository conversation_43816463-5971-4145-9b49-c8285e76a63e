class PaymentSettlementSummeryModel {
  PaymentSettlementSummeryModel({
    this.bookingDetailId,
    this.transporter,
    this.isBookingCancelled,
    this.totalPaidAmount,
    this.taxOfTotalPaidAmount,
    this.totalPaidAmountWithTax,
    this.totalRefundAmount,
    this.totalRefundTaxAmount,
    this.totalRefundAmountWithTax,
    this.totalDueAmount,
    this.totalDueTaxAmount,
    this.totalDueAmountWithTax,
    this.bookedCars,
  });

  PaymentSettlementSummeryModel.fromJson(Map<String, dynamic> json) {
    bookingDetailId = json['booking_detail_id'] as String?;
    transporter = json['transporter'] as String?;
    isBookingCancelled = json['is_booking_cancelled'] as bool? ?? false;
    totalPaidAmount = _parseToDouble(json['total_paid_amount']);
    taxOfTotalPaidAmount = _parseToDouble(json['tax_of_total_paid_amount']);
    totalPaidAmountWithTax = _parseToDouble(json['total_paid_amount_with_tax']);
    totalRefundAmount = _parseToDouble(json['total_refund_amount']);
    totalRefundTaxAmount = _parseToDouble(json['total_refund_tax_amount']);
    totalRefundAmountWithTax = _parseToDouble(
      json['total_refund_amount_with_tax'],
    );
    totalDueAmount = _parseToDouble(json['total_due_amount']);
    totalDueTaxAmount = _parseToDouble(json['total_due_tax_amount']);
    totalDueAmountWithTax = _parseToDouble(json['total_due_amount_with_tax']);
    if (json['booked_cars'] != null) {
      bookedCars = <BookedCars>[];
      json['booked_cars'].forEach((v) {
        bookedCars!.add(BookedCars.fromJson(v as Map<String, dynamic>));
      });
    }
  }
  String? bookingDetailId;
  String? transporter;
  bool? isBookingCancelled;
  double? totalPaidAmount;
  double? taxOfTotalPaidAmount;
  double? totalPaidAmountWithTax;
  double? totalRefundAmount;
  double? totalRefundTaxAmount;
  double? totalRefundAmountWithTax;
  double? totalDueAmount;
  double? totalDueTaxAmount;
  double? totalDueAmountWithTax;
  List<BookedCars>? bookedCars;

  /// Helper method to safely parse numeric values to double
  static double? _parseToDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['booking_detail_id'] = bookingDetailId;
    data['transporter'] = transporter;
    data['is_booking_cancelled'] = isBookingCancelled;
    data['total_paid_amount'] = totalPaidAmount;
    data['tax_of_total_paid_amount'] = taxOfTotalPaidAmount;
    data['total_paid_amount_with_tax'] = totalPaidAmountWithTax;
    data['total_refund_amount'] = totalRefundAmount;
    data['total_refund_tax_amount'] = totalRefundTaxAmount;
    data['total_refund_amount_with_tax'] = totalRefundAmountWithTax;
    data['total_due_amount'] = totalDueAmount;
    data['total_due_tax_amount'] = totalDueTaxAmount;
    data['total_due_amount_with_tax'] = totalDueAmountWithTax;
    if (bookedCars != null) {
      data['booked_cars'] = bookedCars!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class BookedCars {
  BookedCars({
    this.id,
    this.serialNumber,
    this.refundCase,
    this.refundCaseDescription,
    this.paidAmount,
    this.taxOfPaidAmount,
    this.totalPaidAmountWithTax,
    this.totalRefundAmount,
    this.bookedCarRefundTaxAmount,
    this.totalRefundAmountWithTax,
    this.dueAmount,
    this.dueTaxAmount,
    this.totalDueAmountWithTax,
    this.sTARTSTOPLOCATION,
    this.eNDSTOPLOCATION,
    this.tRANSPORTATION,
    this.iNSURANCE,
    this.pICKUPSERVICE,
  });

  BookedCars.fromJson(Map<String, dynamic> json) {
    id = json['id'] as String?;
    serialNumber = json['serial_number'] as String?;
    refundCase = json['refund_case'] as String?;
    refundCaseDescription = json['refund_case_description'] as String?;
    paidAmount = PaymentSettlementSummeryModel._parseToDouble(
      json['paid_amount'],
    );
    taxOfPaidAmount = PaymentSettlementSummeryModel._parseToDouble(
      json['tax_of_paid_amount'],
    );
    totalPaidAmountWithTax = PaymentSettlementSummeryModel._parseToDouble(
      json['total_paid_amount_with_tax'],
    );
    totalRefundAmount = PaymentSettlementSummeryModel._parseToDouble(
      json['total_refund_amount'],
    );
    bookedCarRefundTaxAmount = PaymentSettlementSummeryModel._parseToDouble(
      json['booked_car_refund_tax_amount'],
    );
    totalRefundAmountWithTax = PaymentSettlementSummeryModel._parseToDouble(
      json['total_refund_amount_with_tax'],
    );
    dueAmount = PaymentSettlementSummeryModel._parseToDouble(
      json['due_amount'],
    );
    dueTaxAmount = PaymentSettlementSummeryModel._parseToDouble(
      json['due_tax_amount'],
    );
    totalDueAmountWithTax = PaymentSettlementSummeryModel._parseToDouble(
      json['total_due_amount_with_tax'],
    );
    sTARTSTOPLOCATION = json['START_STOP_LOCATION'] != null
        ? STARTSTOPLOCATION.fromJson(
            json['START_STOP_LOCATION'] as Map<String, dynamic>,
          )
        : null;
    eNDSTOPLOCATION = json['END_STOP_LOCATION'] != null
        ? ENDSTOPLOCATION.fromJson(
            json['END_STOP_LOCATION'] as Map<String, dynamic>,
          )
        : null;
    tRANSPORTATION = json['TRANSPORTATION'] != null
        ? TRANSPORTATION.fromJson(
            json['TRANSPORTATION'] as Map<String, dynamic>,
          )
        : null;
    iNSURANCE = json['INSURANCE'] != null
        ? TRANSPORTATION.fromJson(json['INSURANCE'] as Map<String, dynamic>)
        : null;
    pICKUPSERVICE = json['PICKUP_SERVICE'] != null
        ? PICKUPSERVICE.fromJson(json['PICKUP_SERVICE'] as Map<String, dynamic>)
        : null;
  }
  String? id;
  String? serialNumber;
  String? refundCase;
  String? refundCaseDescription;
  double? paidAmount;
  double? taxOfPaidAmount;
  double? totalPaidAmountWithTax;
  double? totalRefundAmount;
  double? bookedCarRefundTaxAmount;
  double? totalRefundAmountWithTax;
  double? dueAmount;
  double? dueTaxAmount;
  double? totalDueAmountWithTax;
  STARTSTOPLOCATION? sTARTSTOPLOCATION;
  ENDSTOPLOCATION? eNDSTOPLOCATION;
  TRANSPORTATION? tRANSPORTATION;
  TRANSPORTATION? iNSURANCE;
  PICKUPSERVICE? pICKUPSERVICE;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['serial_number'] = serialNumber;
    data['refund_case'] = refundCase;
    data['refund_case_description'] = refundCaseDescription;
    data['paid_amount'] = paidAmount;
    data['tax_of_paid_amount'] = taxOfPaidAmount;
    data['total_paid_amount_with_tax'] = totalPaidAmountWithTax;
    data['total_refund_amount'] = totalRefundAmount;
    data['booked_car_refund_tax_amount'] = bookedCarRefundTaxAmount;
    data['total_refund_amount_with_tax'] = totalRefundAmountWithTax;
    data['due_amount'] = dueAmount;
    data['due_tax_amount'] = dueTaxAmount;
    data['total_due_amount_with_tax'] = totalDueAmountWithTax;
    if (sTARTSTOPLOCATION != null) {
      data['START_STOP_LOCATION'] = sTARTSTOPLOCATION!.toJson();
    }
    if (eNDSTOPLOCATION != null) {
      data['END_STOP_LOCATION'] = eNDSTOPLOCATION!.toJson();
    }
    if (tRANSPORTATION != null) {
      data['TRANSPORTATION'] = tRANSPORTATION!.toJson();
    }
    if (iNSURANCE != null) {
      data['INSURANCE'] = iNSURANCE!.toJson();
    }
    if (pICKUPSERVICE != null) {
      data['PICKUP_SERVICE'] = pICKUPSERVICE!.toJson();
    }
    return data;
  }
}

class STARTSTOPLOCATION {
  STARTSTOPLOCATION({
    this.totalServiceChargeInitiallyApplied,
    this.totalDeductedRemainingAmount,
    this.serviceRemainingAmountNeedToCollect,
    this.totalRefundableAmount,
    this.extraCharges,
  });

  STARTSTOPLOCATION.fromJson(Map<String, dynamic> json) {
    totalServiceChargeInitiallyApplied =
        PaymentSettlementSummeryModel._parseToDouble(
          json['total_service_charge_initially_applied'],
        );
    totalDeductedRemainingAmount = PaymentSettlementSummeryModel._parseToDouble(
      json['total_deducted_remaining_amount'],
    );
    serviceRemainingAmountNeedToCollect =
        PaymentSettlementSummeryModel._parseToDouble(
          json['service_remaining_amount_need_to_collect'],
        );
    totalRefundableAmount = PaymentSettlementSummeryModel._parseToDouble(
      json['total_refundable_amount'],
    );
    extraCharges = PaymentSettlementSummeryModel._parseToDouble(
      json['extra_charges'],
    );
  }
  double? totalServiceChargeInitiallyApplied;
  double? totalDeductedRemainingAmount;
  double? serviceRemainingAmountNeedToCollect;
  double? totalRefundableAmount;
  double? extraCharges;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['total_service_charge_initially_applied'] =
        totalServiceChargeInitiallyApplied;
    data['total_deducted_remaining_amount'] = totalDeductedRemainingAmount;
    data['service_remaining_amount_need_to_collect'] =
        serviceRemainingAmountNeedToCollect;
    data['total_refundable_amount'] = totalRefundableAmount;
    data['extra_charges'] = extraCharges;
    return data;
  }
}

class ENDSTOPLOCATION {
  ENDSTOPLOCATION({
    this.totalServiceChargeInitiallyApplied,
    this.totalDeductedRemainingAmount,
    this.serviceRemainingAmountNeedToCollect,
    this.totalRefundableAmount,
    this.extraCharges,
  });

  ENDSTOPLOCATION.fromJson(Map<String, dynamic> json) {
    totalServiceChargeInitiallyApplied =
        PaymentSettlementSummeryModel._parseToDouble(
          json['total_service_charge_initially_applied'],
        );
    totalDeductedRemainingAmount = PaymentSettlementSummeryModel._parseToDouble(
      json['total_deducted_remaining_amount'],
    );
    serviceRemainingAmountNeedToCollect =
        PaymentSettlementSummeryModel._parseToDouble(
          json['service_remaining_amount_need_to_collect'],
        );
    totalRefundableAmount = PaymentSettlementSummeryModel._parseToDouble(
      json['total_refundable_amount'],
    );
    extraCharges = PaymentSettlementSummeryModel._parseToDouble(
      json['extra_charges'],
    );
  }
  double? totalServiceChargeInitiallyApplied;
  double? totalDeductedRemainingAmount;
  double? serviceRemainingAmountNeedToCollect;
  double? totalRefundableAmount;
  double? extraCharges;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['total_service_charge_initially_applied'] =
        totalServiceChargeInitiallyApplied;
    data['total_deducted_remaining_amount'] = totalDeductedRemainingAmount;
    data['service_remaining_amount_need_to_collect'] =
        serviceRemainingAmountNeedToCollect;
    data['total_refundable_amount'] = totalRefundableAmount;
    data['extra_charges'] = extraCharges;
    return data;
  }
}

class TRANSPORTATION {
  TRANSPORTATION({
    this.totalServiceChargeInitiallyApplied,
    this.totalDeductedRemainingAmount,
    this.serviceRemainingAmountNeedToCollect,
    this.totalRefundableAmount,
    this.extraCharges,
  });

  TRANSPORTATION.fromJson(Map<String, dynamic> json) {
    totalServiceChargeInitiallyApplied =
        PaymentSettlementSummeryModel._parseToDouble(
          json['total_service_charge_initially_applied'],
        );
    totalDeductedRemainingAmount = PaymentSettlementSummeryModel._parseToDouble(
      json['total_deducted_remaining_amount'],
    );
    serviceRemainingAmountNeedToCollect =
        PaymentSettlementSummeryModel._parseToDouble(
          json['service_remaining_amount_need_to_collect'],
        );
    totalRefundableAmount = PaymentSettlementSummeryModel._parseToDouble(
      json['total_refundable_amount'],
    );
    extraCharges = PaymentSettlementSummeryModel._parseToDouble(
      json['extra_charges'],
    );
  }
  double? totalServiceChargeInitiallyApplied;
  double? totalDeductedRemainingAmount;
  double? serviceRemainingAmountNeedToCollect;
  double? totalRefundableAmount;
  double? extraCharges;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['total_service_charge_initially_applied'] =
        totalServiceChargeInitiallyApplied;
    data['total_deducted_remaining_amount'] = totalDeductedRemainingAmount;
    data['service_remaining_amount_need_to_collect'] =
        serviceRemainingAmountNeedToCollect;
    data['total_refundable_amount'] = totalRefundableAmount;
    data['extra_charges'] = extraCharges;
    return data;
  }
}

class PICKUPSERVICE {
  PICKUPSERVICE({
    this.totalServiceChargeInitiallyApplied,
    this.totalDeductedRemainingAmount,
    this.serviceRemainingAmountNeedToCollect,
    this.totalRefundableAmount,
    this.extraCharges,
  });

  PICKUPSERVICE.fromJson(Map<String, dynamic> json) {
    totalServiceChargeInitiallyApplied =
        PaymentSettlementSummeryModel._parseToDouble(
          json['total_service_charge_initially_applied'],
        );
    totalDeductedRemainingAmount = PaymentSettlementSummeryModel._parseToDouble(
      json['total_deducted_remaining_amount'],
    );
    serviceRemainingAmountNeedToCollect =
        PaymentSettlementSummeryModel._parseToDouble(
          json['service_remaining_amount_need_to_collect'],
        );
    totalRefundableAmount = PaymentSettlementSummeryModel._parseToDouble(
      json['total_refundable_amount'],
    );
    extraCharges = PaymentSettlementSummeryModel._parseToDouble(
      json['extra_charges'],
    );
  }
  double? totalServiceChargeInitiallyApplied;
  double? totalDeductedRemainingAmount;
  double? serviceRemainingAmountNeedToCollect;
  double? totalRefundableAmount;
  double? extraCharges;

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['total_service_charge_initially_applied'] =
        totalServiceChargeInitiallyApplied;
    data['total_deducted_remaining_amount'] = totalDeductedRemainingAmount;
    data['service_remaining_amount_need_to_collect'] =
        serviceRemainingAmountNeedToCollect;
    data['total_refundable_amount'] = totalRefundableAmount;
    data['extra_charges'] = extraCharges;
    return data;
  }
}
