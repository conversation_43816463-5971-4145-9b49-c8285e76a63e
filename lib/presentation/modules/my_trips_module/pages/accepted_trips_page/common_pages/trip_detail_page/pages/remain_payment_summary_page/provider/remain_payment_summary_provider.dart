import 'package:flutter/material.dart';
import 'package:transport_match/presentation/common_pages/common_webview_page/models/common_web_view_params.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/logger.dart';

class RemainPaymentSummaryProvider extends ChangeNotifier {
  final bool _isClosed = false;

  /// Notify listeners if not closed
  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      'RemainPaymentSummaryProvider notify error: $e'.logE;
    }
  }

  /// get payment information
  /// get payment information
  Future<void> navigateToWebviewForPayment(
    BuildContext context, {
    bool isExclusiveTrip = false,
    required String bookingId,
  }) async {
    '/// bookinggg id : $bookingId'.logD;
    await AppNavigationService.pushNamed(
      context,
      AppRoutes.commonWebViewScreen,
      extra: CommonWebViewParams(
        context: context,
        bodyData: {'booking_detail': bookingId},
        isRemainAmountAPI: true,
        isExclusiveTrip: isExclusiveTrip,
        bookingId: bookingId,
      ),
    );
   // context.goNamed(AppRoutes.homeBase);
  }
}
