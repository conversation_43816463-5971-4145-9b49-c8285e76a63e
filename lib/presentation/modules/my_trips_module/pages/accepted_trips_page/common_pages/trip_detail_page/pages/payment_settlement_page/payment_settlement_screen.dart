import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/pages/payment_settlement_page/models/payment_settlement_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/pages/payment_settlement_page/provider/payment_settlement_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/models/payment_settlement_summery_model.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/widgets/payment_summary_row.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

/// Payment Settlement Screen
class PaymentSettlementScreen extends StatelessWidget {
  /// Constructor
  const PaymentSettlementScreen({
    super.key,
    required this.paymentSettlementParams,
  });

  final PaymentSettlementParams paymentSettlementParams;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => PaymentSettlementProvider()
        ..setPaymentSettlementData(
          paymentSettlementParams.paymentSettlementData,
        ),
      builder: (context, child) {
        return Scaffold(
          appBar: CustomAppBar(title: context.l10n.paymentSettlement),
          backgroundColor: AppColors.ffF8F9FA,
          bottomNavigationBar: ValueListenableBuilder<bool>(
            valueListenable: Provider.of<PaymentSettlementProvider>(
              context,
            ).isShowLoader,
            builder: (context, showLoader, child) {
              return Consumer<PaymentSettlementProvider>(
                builder: (context, provider, child) {
                  if (showLoader) {
                    return const SizedBox.shrink();
                  }

                  // Only show button if there's due amount or refund amount
                  final hasDueAmount = provider.totalDueAmountWithTax > 0;
                  final hasRefundAmount = provider.totalRefundAmountWithTax > 0;

                  if (!hasDueAmount && !hasRefundAmount) {
                    return const SizedBox.shrink();
                  }

                  return AppButton(
                    onPressed: () async {
                      await provider.createPaymentSettlementCheckout(
                        context,
                        bookingId: paymentSettlementParams.bookingId,
                        isExclusiveTrip:
                            paymentSettlementParams.isExclusiveTrip,
                      );
                    },
                    text: context.l10n.proceedToSettlement,
                  );
                },
              );
            },
          ),
          body: Consumer<PaymentSettlementProvider>(
            builder: (context, provider, child) {
              return ValueListenableBuilder(
                valueListenable: provider.isShowLoader,
                builder: (context, isLoading, child) {
                  return AppLoader(
                    isShowLoader: isLoading,

                    child: SingleChildScrollView(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppSize.appPadding,
                      ).add(EdgeInsets.only(bottom: AppSize.h16)),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        spacing: AppSize.h12,
                        children: [
                          // Overall Settlement Summary
                          _buildOverallSummary(context, provider),

                          // Booking Status
                          if (provider.isBookingCancelled)
                            _buildBookingStatusCard(context),

                          // Booked Cars Details
                          if (provider.bookedCars.isNotEmpty)
                            ...provider.bookedCars.asMap().entries.map(
                              (entry) => _buildBookedCarCard(
                                context,
                                entry.value,
                                entry.key,
                              ),
                            ),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          ),
        );
      },
    );
  }

  /// Build overall settlement summary
  Widget _buildOverallSummary(
    BuildContext context,
    PaymentSettlementProvider provider,
  ) {
    return DecoratedBox(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppSize.r8),
        color: Colors.white,
      ),
      child: Padding(
        padding: EdgeInsets.all(AppSize.h16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.l10n.paymentSettlementSummary,
              style: context.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            Gap(AppSize.h12),

            // Transporter Info
            if (provider.transporterName.isNotEmpty)
              PaymentSummaryRow(
                title: context.l10n.transporter,
                amount: provider.transporterName,
              ),

            Gap(AppSize.h8),
            const Divider(),
            Gap(AppSize.h8),

            // Total Amounts
            TotalRowWithDetails(
              firstTitle: context.l10n.paidAmount,
              lastTitle:
                  provider.paymentSettlementData?.totalPaidAmount
                      ?.toString()
                      .smartFormat() ??
                  '0',
              tax:
                  provider.paymentSettlementData?.taxOfTotalPaidAmount
                      ?.toString()
                      .smartFormat() ??
                  '0',
            ),

            Gap(AppSize.h8),

            if ((provider.totalRefundAmountWithTax) > 0)
              TotalRowWithDetails(
                firstTitle: context.l10n.refundAmount,
                lastTitle:
                    provider.paymentSettlementData?.totalRefundAmount
                        ?.toString()
                        .smartFormat() ??
                    '0',
                tax:
                    provider.paymentSettlementData?.totalRefundTaxAmount
                        ?.toString()
                        .smartFormat() ??
                    '0',
              ),

            Gap(AppSize.h8),

            if ((provider.totalDueAmountWithTax) > 0)
              TotalRowWithDetails(
                firstTitle: context.l10n.dueAmount,
                lastTitle:
                    provider.paymentSettlementData?.totalDueAmount
                        ?.toString()
                        .smartFormat() ??
                    '0',
                tax:
                    provider.paymentSettlementData?.totalDueTaxAmount
                        ?.toString()
                        .smartFormat() ??
                    '0',
              ),
          ],
        ),
      ),
    );
  }

  /// Build booking status card
  Widget _buildBookingStatusCard(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppSize.r8),
        color: AppColors.warningColor.withValues(alpha: 0.1),
        border: Border.all(color: AppColors.ffFFC107),
      ),
      child: Padding(
        padding: EdgeInsets.all(AppSize.h16),
        child: Row(
          children: [
            Icon(
              Icons.warning_amber_rounded,
              color: AppColors.ffFFC107,
              size: AppSize.sp24,
            ),
            Gap(AppSize.w12),
            Expanded(
              child: Text(
                context.l10n.bookingCancelled,
                style: context.textTheme.bodyMedium?.copyWith(
                  color: AppColors.warningColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build booked car card
  Widget _buildBookedCarCard(BuildContext context, BookedCars car, int index) {
    return DecoratedBox(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppSize.r8),
        color: Colors.white,
      ),
      child: Padding(
        padding: EdgeInsets.all(AppSize.h16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Car Header
            Row(
              children: [
                Icon(
                  Icons.directions_car,
                  color: AppColors.primaryColor,
                  size: AppSize.sp20,
                ),
                Gap(AppSize.w8),
                Expanded(
                  child: Text(
                    '${context.l10n.vehicle} ${index + 1}${(car.serialNumber?.isNotEmpty ?? false) ? ' - ${car.serialNumber}' : ''}',
                    style: context.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),

            Gap(AppSize.h12),

            // Car Payment Details
            if ((car.paidAmount ?? 0) > 0)
              PaymentSummaryRow(
                title: context.l10n.paidAmount,
                amount: car.paidAmount?.toString().smartFormat() ?? '0',
              ),

            if ((car.totalRefundAmount ?? 0) > 0)
              PaymentSummaryRow(
                title: context.l10n.refundAmount,
                amount: car.totalRefundAmount?.toString().smartFormat() ?? '0',
              ),

            if ((car.dueAmount ?? 0) > 0)
              PaymentSummaryRow(
                title: context.l10n.dueAmount,
                amount: car.dueAmount?.toString().smartFormat() ?? '0',
              ),

            // Service Details
            _buildServiceDetails(context, car),

            // Refund Case Information
            if (car.refundCase?.isNotEmpty ?? false)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Gap(AppSize.h8),
                  const Divider(),

                  if (car.refundCaseDescription?.isNotEmpty ?? false)
                    Padding(
                      padding: EdgeInsets.only(top: AppSize.h8),
                      child: Text(
                        car.refundCaseDescription ?? '',
                        style: context.textTheme.bodySmall?.copyWith(
                          color: AppColors.ff6C757D,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  /// Build service details breakdown
  Widget _buildServiceDetails(BuildContext context, BookedCars car) {
    final hasServiceDetails =
        car.tRANSPORTATION != null ||
        car.iNSURANCE != null ||
        car.sTARTSTOPLOCATION != null ||
        car.eNDSTOPLOCATION != null ||
        car.pICKUPSERVICE != null;

    if (!hasServiceDetails) return const SizedBox.shrink();

    return Theme(
      data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
      child: ExpansionTile(
        tilePadding: EdgeInsets.zero,
        childrenPadding: EdgeInsets.zero,
        title: Text(
          context.l10n.serviceBreakdown,
          style: context.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w700,
            color: AppColors.primaryColor,
          ),
        ),
        children: [
          // Transportation Service
          if (car.tRANSPORTATION != null &&
              ((car.tRANSPORTATION!.totalServiceChargeInitiallyApplied ?? 0) >
                  0))
            _buildServiceDetailCard(
              context,
              context.l10n.transportation,
              car.tRANSPORTATION!,
              Icons.local_shipping,
            ),

          // Insurance Service
          if (car.iNSURANCE != null &&
              ((car.iNSURANCE!.totalServiceChargeInitiallyApplied ?? 0) > 0))
            _buildServiceDetailCard(
              context,
              context.l10n.insurance,
              car.iNSURANCE!,
              Icons.security,
            ),

          // Start Stop Location Service
          if (car.sTARTSTOPLOCATION != null &&
              ((car.sTARTSTOPLOCATION!.totalServiceChargeInitiallyApplied ??
                      0) >
                  0))
            _buildStartStopLocationCard(
              context,
              context.l10n.pickUpStorage,
              car.sTARTSTOPLOCATION!,
              Icons.warehouse,
            ),

          // End Stop Location Service
          if (car.eNDSTOPLOCATION != null &&
              ((car.eNDSTOPLOCATION!.totalServiceChargeInitiallyApplied ?? 0) >
                  0))
            _buildEndStopLocationCard(
              context,
              context.l10n.dropOffStorage,
              car.eNDSTOPLOCATION!,
              Icons.inventory,
            ),

          // Pickup Service
          if (car.pICKUPSERVICE != null &&
              ((car.pICKUPSERVICE!.totalServiceChargeInitiallyApplied ?? 0) >
                  0))
            _buildPickupServiceCard(
              context,
              context.l10n.pickupService,
              car.pICKUPSERVICE!,
              Icons.local_taxi,
            ),
        ],
      ),
    );
  }

  /// Build service detail card for Transportation and Insurance
  Widget _buildServiceDetailCard(
    BuildContext context,
    String title,
    TRANSPORTATION service,
    IconData icon,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: AppSize.h8),
      padding: EdgeInsets.all(AppSize.h12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppSize.r6),
        color: AppColors.primaryColor.withValues(alpha: 0.05),
        border: Border.all(
          color: AppColors.primaryColor.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: AppSize.sp16, color: AppColors.primaryColor),
              Gap(AppSize.w8),
              Text(
                title,
                style: context.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primaryColor,
                ),
              ),
            ],
          ),
          Gap(AppSize.h8),
          // if ((service.totalServiceChargeInitiallyApplied ?? 0) > 0)
          _buildServiceRow(
            context.l10n.initialCharge,
            service.totalServiceChargeInitiallyApplied
                    ?.toString()
                    .smartFormat() ??
                '0',
          ),
          // if ((service.totalDeductedRemainingAmount ?? 0) > 0)
          _buildServiceRow(
            context.l10n.deductedAmount,
            service.totalDeductedRemainingAmount?.toString().smartFormat() ??
                '0',
          ),
          // if ((service.serviceRemainingAmountNeedToCollect ?? 0) > 0)
          _buildServiceRow(
            context.l10n.remainingToCollect,
            service.serviceRemainingAmountNeedToCollect
                    ?.toString()
                    .smartFormat() ??
                '0',
          ),
          // if ((service.totalRefundableAmount ?? 0) > 0)
          _buildServiceRow(
            context.l10n.refundableAmount,
            service.totalRefundableAmount?.toString().smartFormat() ?? '0',
          ),
          // if ((service.extraCharges ?? 0) > 0)
          _buildServiceRow(
            context.l10n.extraCharges,
            service.extraCharges?.toString().smartFormat() ?? '0',
          ),
        ],
      ),
    );
  }

  /// Build start stop location service card
  Widget _buildStartStopLocationCard(
    BuildContext context,
    String title,
    STARTSTOPLOCATION service,
    IconData icon,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: AppSize.h8),
      padding: EdgeInsets.all(AppSize.h12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppSize.r6),
        color: AppColors.primaryColor.withValues(alpha: 0.05),
        border: Border.all(
          color: AppColors.primaryColor.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: AppSize.sp16, color: AppColors.primaryColor),
              Gap(AppSize.w8),
              Text(
                title,
                style: context.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primaryColor,
                ),
              ),
            ],
          ),
          Gap(AppSize.h8),
          // if ((service.totalServiceChargeInitiallyApplied ?? 0) > 0)
          _buildServiceRow(
            context.l10n.initialCharge,
            service.totalServiceChargeInitiallyApplied
                    ?.toString()
                    .smartFormat() ??
                '0',
          ),
          // if ((service.totalDeductedRemainingAmount ?? 0) > 0)
          _buildServiceRow(
            context.l10n.deductedAmount,
            service.totalDeductedRemainingAmount?.toString().smartFormat() ??
                '0',
          ),
          // if ((service.serviceRemainingAmountNeedToCollect ?? 0) > 0)
          _buildServiceRow(
            context.l10n.remainingToCollect,
            service.serviceRemainingAmountNeedToCollect
                    ?.toString()
                    .smartFormat() ??
                '0',
          ),
          // if ((service.totalRefundableAmount ?? 0) > 0)
          _buildServiceRow(
            context.l10n.refundableAmount,
            service.totalRefundableAmount?.toString().smartFormat() ?? '0',
          ),
          // if ((service.extraCharges ?? 0) > 0)
          _buildServiceRow(
            context.l10n.extraCharges,
            service.extraCharges?.toString().smartFormat() ?? '0',
          ),
        ],
      ),
    );
  }

  /// Build end stop location service card
  Widget _buildEndStopLocationCard(
    BuildContext context,
    String title,
    ENDSTOPLOCATION service,
    IconData icon,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: AppSize.h8),
      padding: EdgeInsets.all(AppSize.h12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppSize.r6),
        color: AppColors.primaryColor.withValues(alpha: 0.05),
        border: Border.all(
          color: AppColors.primaryColor.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: AppSize.sp16, color: AppColors.primaryColor),
              Gap(AppSize.w8),
              Text(
                title,
                style: context.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primaryColor,
                ),
              ),
            ],
          ),
          Gap(AppSize.h8),
          // if ((service.totalServiceChargeInitiallyApplied ?? 0) > 0)
          _buildServiceRow(
            context.l10n.initialCharge,
            service.totalServiceChargeInitiallyApplied
                    ?.toString()
                    .smartFormat() ??
                '0',
          ),
          // if ((service.totalDeductedRemainingAmount ?? 0) > 0)
          _buildServiceRow(
            context.l10n.deductedAmount,
            service.totalDeductedRemainingAmount?.toString().smartFormat() ??
                '0',
          ),
          // if ((service.serviceRemainingAmountNeedToCollect ?? 0) > 0)
          _buildServiceRow(
            context.l10n.remainingToCollect,
            service.serviceRemainingAmountNeedToCollect
                    ?.toString()
                    .smartFormat() ??
                '0',
          ),
          // if ((service.totalRefundableAmount ?? 0) > 0)
          _buildServiceRow(
            context.l10n.refundableAmount,
            service.totalRefundableAmount?.toString().smartFormat() ?? '0',
          ),
          // if ((service.extraCharges ?? 0) > 0)
          _buildServiceRow(
            context.l10n.extraCharges,
            service.extraCharges?.toString().smartFormat() ?? '0',
          ),
        ],
      ),
    );
  }

  /// Build pickup service card
  Widget _buildPickupServiceCard(
    BuildContext context,
    String title,
    PICKUPSERVICE service,
    IconData icon,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: AppSize.h8),
      padding: EdgeInsets.all(AppSize.h12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppSize.r6),
        color: AppColors.primaryColor.withValues(alpha: 0.05),
        border: Border.all(
          color: AppColors.primaryColor.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: AppSize.sp16, color: AppColors.primaryColor),
              Gap(AppSize.w8),
              Text(
                title,
                style: context.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primaryColor,
                ),
              ),
            ],
          ),
          Gap(AppSize.h8),
          // if ((service.totalServiceChargeInitiallyApplied ?? 0) > 0)
          _buildServiceRow(
            context.l10n.initialCharge,
            service.totalServiceChargeInitiallyApplied
                    ?.toString()
                    .smartFormat() ??
                '0',
          ),
          // if ((service.totalDeductedRemainingAmount ?? 0) > 0)
          _buildServiceRow(
            context.l10n.deductedAmount,
            service.totalDeductedRemainingAmount?.toString().smartFormat() ??
                '0',
          ),
          // if ((service.serviceRemainingAmountNeedToCollect ?? 0) > 0)
          _buildServiceRow(
            context.l10n.remainingToCollect,
            service.serviceRemainingAmountNeedToCollect
                    ?.toString()
                    .smartFormat() ??
                '0',
          ),
          // if ((service.totalRefundableAmount ?? 0) > 0)
          _buildServiceRow(
            context.l10n.refundableAmount,
            service.totalRefundableAmount?.toString().smartFormat() ?? '0',
          ),
          // if ((service.extraCharges ?? 0) > 0)
          _buildServiceRow(
            context.l10n.extraCharges,
            service.extraCharges?.toString().smartFormat() ?? '0',
          ),
        ],
      ),
    );
  }

  /// Build service row
  Widget _buildServiceRow(String title, String amount) {
    return Padding(
      padding: EdgeInsets.only(bottom: AppSize.h4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: AppSize.sp12,
              fontWeight: FontWeight.w500,
              color: AppColors.ff6C757D,
            ),
          ),
          Text(
            amount,
            style: TextStyle(
              fontSize: AppSize.sp12,
              fontWeight: FontWeight.w600,
              color: AppColors.ff495057,
            ),
          ),
        ],
      ),
    );
  }
}
