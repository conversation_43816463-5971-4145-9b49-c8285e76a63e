import 'package:flutter/material.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/presentation/common_pages/common_webview_page/models/common_web_view_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/models/payment_settlement_summery_model.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/shared/repositories/trip_repo.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/app_common_functions.dart';

/// Provider for payment settlement screen
class PaymentSettlementProvider extends ChangeNotifier {
  final bool _isClosed = false;

  PaymentSettlementSummeryModel? _paymentSettlementData;

  /// Get payment settlement data
  PaymentSettlementSummeryModel? get paymentSettlementData =>
      _paymentSettlementData;

  /// loader
  final isShowLoader = ValueNotifier<bool>(false);

  /// Set payment settlement data
  void setPaymentSettlementData(PaymentSettlementSummeryModel data) {
    _paymentSettlementData = data;
    notify();
  }

  /// Notify listeners if not closed
  void notify() {
    if (_isClosed) return;
    try {
      notifyListeners();
    } catch (e) {
      // Handle notification error silently
      debugPrint('PaymentSettlementProvider notify error: $e');
    }
  }

  /// Get total paid amount with tax
  double get totalPaidAmountWithTax =>
      _paymentSettlementData?.totalPaidAmountWithTax ?? 0.0;

  /// Get total refund amount with tax
  double get totalRefundAmountWithTax =>
      _paymentSettlementData?.totalRefundAmountWithTax ?? 0.0;

  /// Get total due amount with tax
  double get totalDueAmountWithTax =>
      _paymentSettlementData?.totalDueAmountWithTax ?? 0.0;

  /// Check if booking is cancelled
  bool get isBookingCancelled =>
      _paymentSettlementData?.isBookingCancelled ?? false;

  /// Get booked cars list
  List<BookedCars> get bookedCars => _paymentSettlementData?.bookedCars ?? [];

  /// Get transporter name
  String get transporterName => _paymentSettlementData?.transporter ?? '';

  /// Create payment settlement checkout
  Future<void> createPaymentSettlementCheckout(
    BuildContext context, {
    required String bookingId,
    required bool isExclusiveTrip,
  }) async {
    try {
      isShowLoader.value = true;
      final endpoint = isExclusiveTrip
          ? EndPoints.exclusiveTripPaymentSettlementCheckout
          : EndPoints.sharedTripPaymentSettlementCheckout;

      final bodyData = {'booking_detail': bookingId};

      final response = await Injector.instance<TripRepository>()
          .createPaymentSettlementCheckout(
            ApiRequest(path: endpoint, data: bodyData),
          );

      await response.when(
        success: (data) async {
          // Handle successful response - typically navigate to payment URL
          final paymentUrl = data['session_url'] as String?;
          isShowLoader.value = false;
          if (paymentUrl != null && paymentUrl.isNotEmpty) {
            // Redirect user to common web view page
            await AppNavigationService.pushNamed(
              context,
              AppRoutes.commonWebViewScreen,
              extra: CommonWebViewParams(
                context: context,
                url: paymentUrl,
                isSettlementAmount: true,
              ),
            );
          } else {
            'Invalid or empty session URL received from the server.'
                .showErrorAlert();
          }
        },
        error: (error) {
          isShowLoader.value = false;
          error.message.showErrorAlert();
        },
      );
    } catch (e) {
      isShowLoader.value = false;
      'Failed to create payment settlement checkout: $e'.showErrorAlert();
    }
  }
}
