import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/common_pages/car_info_page/car_info_screen.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/checklist_page/models/checklist_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/trip_detail_page/pages/car_payment_detail_page/models/car_payment_detail_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/widgets/payment_summary_row.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_padding.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

class CarPaymentDetailScreen extends StatelessWidget {
  const CarPaymentDetailScreen({
    super.key,
    required this.carPaymentDetailParams,
  });
  final CarPaymentDetailParams carPaymentDetailParams;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.ffF8F9FA,
      appBar: CustomAppBar(
        title:
            carPaymentDetailParams.bookingDetail?.tripData?.companyName ?? '',
      ),
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: AppSize.appPadding,
        ).add(EdgeInsets.only(bottom: AppSize.h16)),
        children: [
          Text(
            context.l10n.vehicleInfo,
            style: context.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w700,
            ),
          ),
          if (carPaymentDetailParams.bookingDetail?.carDetails?.isNotEmpty ??
              false)
            ListView.builder(
              shrinkWrap: true,
              padding: EdgeInsets.only(bottom: AppSize.h16),
              physics: const NeverScrollableScrollPhysics(),
              itemCount:
                  carPaymentDetailParams.bookingDetail?.carDetails?.length ?? 0,
              itemBuilder: (context, index) {
                final carData =
                    carPaymentDetailParams.bookingDetail?.carDetails?[index];
                return Container(
                  padding: EdgeInsets.all(AppSize.h16),
                  margin: EdgeInsets.symmetric(vertical: AppSize.h8),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.ffADB5BD),
                    borderRadius: BorderRadius.circular(AppSize.r4),
                    color: AppColors.white,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    spacing: AppSize.h8,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          if (carData?.car?.brand != null)
                            VehiclesInfoField(
                              title: context.l10n.carBrand,
                              value: carData?.car?.brand ?? '',
                            ),
                          if (carData?.car?.model != null)
                            VehiclesInfoField(
                              title: context.l10n.carModel,
                              value: carData?.car?.model ?? '',
                            ),
                        ],
                      ),
                      if (carData?.car?.year != null &&
                          carData?.car?.model != null) ...[
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            if (carData?.serialNumber != null)
                              VehiclesInfoField(
                                title: '${context.l10n.carSerial} #',
                                value: carData?.serialNumber ?? '',
                              ),
                            if (carData?.car?.year != null)
                              VehiclesInfoField(
                                title: context.l10n.carYear,
                                value: carData?.car?.year?.toString() ?? '',
                              ),
                          ],
                        ),
                        Padding(
                          padding: EdgeInsets.only(top: AppSize.h8),
                          child: GestureDetector(
                            onTap: () => AppNavigationService.pushNamed(
                              context,
                              AppRoutes.tripsChecklistScreen,
                              extra: ChecklistParams(
                                clientName:
                                    carPaymentDetailParams
                                        .bookingDetail
                                        ?.tripData
                                        ?.companyName ??
                                    '',
                                carId: carData?.id?.toString() ?? '',
                              ),
                              // extra:
                              // ChecklistScreen(
                              //   clientName:
                              //       bookingDetail?.tripData?.companyName ??
                              //           '',
                              //   carId: carData?.id?.toString() ?? '',
                              // ),
                            ),
                            child: Row(
                              spacing: 5,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  context.l10n.viewCheckList,
                                  style: TextStyle(
                                    color: AppColors.primaryColor,
                                    fontSize: AppSize.sp14,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                Icon(
                                  Icons.arrow_forward_ios_rounded,
                                  size: AppSize.sp14,
                                  color: AppColors.primaryColor,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                );
              },
            )
          else
            AppPadding.symmetric(
              vertical: AppSize.h10,
              child: Text(context.l10n.noVehicleAvailable),
            ),
          Column(
            spacing: AppSize.h16,
            children: [
              DecoratedBox(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(AppSize.r8),
                  color: AppColors.white,
                ),
                child: Padding(
                  padding: EdgeInsets.all(AppSize.r16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        context.l10n.paymentSummary,
                        style: context.textTheme.titleLarge,
                      ),
                      SizedBox(height: AppSize.h8),
                      if (carPaymentDetailParams
                              .paymentData
                              ?.transportationCharge !=
                          null)
                        PaymentSummaryRow(
                          title: context.l10n.transportationCost,
                          amount:
                              '\$${carPaymentDetailParams.paymentData?.transportationCharge}',
                        ),
                      if (carPaymentDetailParams
                              .paymentData
                              ?.endStopLocationStorageCharge !=
                          null)
                        PaymentSummaryRow(
                          title: context.l10n.dropOffStorageFee,
                          amount:
                              '\$${carPaymentDetailParams.paymentData?.endStopLocationStorageCharge}',
                        ),
                      if (carPaymentDetailParams
                              .paymentData
                              ?.startStopLocationStorageCharge !=
                          null)
                        PaymentSummaryRow(
                          title: context.l10n.pickupStorageFee,
                          amount:
                              '\$${carPaymentDetailParams.paymentData?.startStopLocationStorageCharge}',
                        ),
                      if (carPaymentDetailParams.paymentData?.appFee != null)
                        PaymentSummaryRow(
                          title: context.l10n.serviceFee,
                          amount:
                              '\$${carPaymentDetailParams.paymentData?.appFee}',
                        ),
                    ],
                  ),
                ),
              ),
              TotalRowWithDetails(
                firstTitle: context.l10n.paidAmount,
                lastTitle:
                    '\$${carPaymentDetailParams.paymentData?.paidAmountWithoutTax}'
                        .smartFormat(),
                tax:
                    '\$${(carPaymentDetailParams.paymentData?.paidAmount ?? 0.0) - (carPaymentDetailParams.paymentData?.paidAmountWithoutTax ?? 0.0)}'
                        .smartFormat(),
              ),
              TotalRowWithDetails(
                firstTitle: context.l10n.remainAmount,
                lastTitle:
                    '\$${carPaymentDetailParams.paymentData?.remainingBookingDetailAmountWithoutTax}'
                        .smartFormat(),
                tax: '\$${carPaymentDetailParams.paymentData?.taxAmount}'
                    .smartFormat(),
              ),
              TotalRow(
                firstTitle: context.l10n.total,
                lastTitle:
                    '\$${carPaymentDetailParams.paymentData?.totalTripCostWithoutTax}'
                        .smartFormat(),
                padding: AppSize.h16,
                isLast: true,
              ),
            ],
          ),
        ],
      ),
      bottomNavigationBar: AppButton(
        text: context.l10n.cancelTrip,
        isFillButton: false,
        borderColor: AppColors.errorColor,
        textStyle: context.textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w600,
          color: AppColors.errorColor,
          fontSize: AppSize.sp16,
        ),
        onPressed: carPaymentDetailParams.onCancelTrip.call,
      ),
    );
  }
}
