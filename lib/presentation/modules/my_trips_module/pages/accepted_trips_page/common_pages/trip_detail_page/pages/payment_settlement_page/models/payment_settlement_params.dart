import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/models/payment_settlement_summery_model.dart';

/// Parameter model for payment settlement screen.
class PaymentSettlementParams {
  /// Constructor
  const PaymentSettlementParams({
    required this.paymentSettlementData,
    required this.bookingId,
    this.isExclusiveTrip = false,
  });

  /// Payment settlement data
  final PaymentSettlementSummeryModel paymentSettlementData;
  
  /// Booking ID
  final String bookingId;
  
  /// Is exclusive trip flag
  final bool isExclusiveTrip;
}
