import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_datetime.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/checklist_page/models/show_checklist_params.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/checklist_page/provider/checklist_provider.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/checklist_page/widgets/checklist_image_widget.dart';
import 'package:transport_match/presentation/modules/my_trips_module/pages/accepted_trips_page/common_pages/checklist_page/widgets/checklist_widget.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/app_string.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/app_textfield.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

///Check list Screen Ui
class ShowChecklistScreen extends StatelessWidget {
  /// Constructor
  const ShowChecklistScreen({super.key, required this.showChecklistParams});
  final ShowChecklistParams showChecklistParams;

  @override
  Widget build(BuildContext context) {
    final titleStyle = context.textTheme.bodyLarge?.copyWith(
      fontWeight: FontWeight.w600,
    );
    final isNeedVerification =
        (showChecklistParams.checklistModel?.performedDuring ==
            ChecklistType.COLLECTING_FROM_CUSTOMER.name) ||
        (showChecklistParams.checklistModel?.performedDuring ==
            ChecklistType.HANDED_OVER_TO_CUSTOMER.name);
    final isApproved =
        showChecklistParams
            .checklistModel
            ?.customerChecklistVerificationStatus ==
        ChecklistRequestDataType.ACCEPTED.name;
    return ChangeNotifierProvider.value(
      value: showChecklistParams.checkListProvider,
      child: Scaffold(
        backgroundColor: AppColors.ffF8F9FA,
        appBar: CustomAppBar(title: context.l10n.checklist),
        bottomNavigationBar: isNeedVerification && !isApproved
            ? Selector<ChecklistProvider, bool>(
                selector: (context, provider) => provider.isApproveShowLoader,
                builder: (context, isShowLoader, child) {
                  return AppButton(
                    text: context.l10n.confirmChecklist,
                    onPressed: !isShowLoader
                        ? () => showChecklistParams.checkListProvider
                              .approvedCheckList(
                                showChecklistParams.checklistModel?.id ?? 0,
                                context,
                              )
                        : null,
                  );
                },
              )
            : null,
        body: Selector<ChecklistProvider, bool>(
          selector: (context, provider) => provider.isApproveShowLoader,
          builder: (context, isShowLoader, child) {
            return AppLoader(isShowLoader: isShowLoader, child: child!);
          },
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: AppSize.appPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.l10n.pleaseReview,
                  style: context.textTheme.bodySmall?.copyWith(
                    color: AppColors.errorColor,
                  ),
                ),
                // Container(
                //   width: double.maxFinite,
                //   decoration: BoxDecoration(
                //     color: AppColors.white,
                //     borderRadius: BorderRadius.circular(AppSize.r5),
                //     border: Border.all(color: AppColors.ffADB5BD),
                //   ),
                //   padding: EdgeInsets.all(AppSize.sp10),
                //   margin: EdgeInsets.only(top: AppSize.h10),
                //   child: Column(
                //     crossAxisAlignment: CrossAxisAlignment.start,
                //     children: [
                //       Text(
                //         context.l10n.clientName,
                //         style: context.textTheme.titleSmall
                //             ?.copyWith(color: AppColors.ffADB5BD),
                //       ),
                //       Text(
                //         clientName,
                //         style: context.textTheme.titleMedium,
                //       ),
                //     ],
                //   ),
                // ),
                if (showChecklistParams
                        .checklistModel
                        ?.performedDuring
                        ?.isNotEmpty ??
                    false)
                  Padding(
                    padding: EdgeInsets.only(top: AppSize.h20),
                    child: Text(context.l10n.checklistType, style: titleStyle),
                  ),
                if (showChecklistParams
                        .checklistModel
                        ?.performedDuring
                        ?.isNotEmpty ??
                    false)
                  Text(
                    "${AppStrings.bullet}${showChecklistParams.checklistModel?.performedDuring?.upToLower ?? ''}",
                    style: context.textTheme.titleSmall,
                  ),
                Builder(
                  builder: (context) {
                    final isPickup =
                        showChecklistParams.checklistModel?.performedDuring ==
                            ChecklistType.COLLECTING_FROM_CUSTOMER.name ||
                        showChecklistParams.checklistModel?.performedDuring ==
                            ChecklistType
                                .COLLECTING_FROM_ORIGIN_STOP_ADMIN
                                .name;
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (showChecklistParams.checklistModel?.mileage != null)
                          Padding(
                            padding: EdgeInsets.only(top: AppSize.h15),
                            child: Text(
                              context.l10n.mileageAt(
                                isPickup
                                    ? context.l10n.pickup
                                    : context.l10n.delivery,
                              ),
                              style: titleStyle,
                            ),
                          ),
                        if (showChecklistParams.checklistModel?.mileage != null)
                          Text(
                            "${AppStrings.bullet}${showChecklistParams.checklistModel?.mileage ?? ''}",
                            style: context.textTheme.titleSmall,
                          ),
                        if (showChecklistParams.checklistModel?.createdAt !=
                            null)
                          Padding(
                            padding: EdgeInsets.only(top: AppSize.h15),
                            child: Text(
                              context.l10n.dateLabel(
                                isPickup
                                    ? context.l10n.pickup
                                    : context.l10n.delivery,
                              ),
                              style: titleStyle,
                            ),
                          ),
                        if (showChecklistParams.checklistModel?.createdAt !=
                            null)
                          Text(
                            "${AppStrings.bullet}${showChecklistParams.checklistModel?.createdAt?.fullDateTime ?? ''}",
                            style: context.textTheme.titleSmall,
                          ),
                      ],
                    );
                  },
                ),
                Gap(AppSize.h20),
                ChecklistWidget(
                  checklistModel: showChecklistParams.checklistModel,
                ),
                if (showChecklistParams.checklistModel?.fuelLevel?.isNotEmpty ??
                    false)
                  Text(context.l10n.fuelLevel, style: titleStyle),
                if (showChecklistParams.checklistModel?.fuelLevel?.isNotEmpty ??
                    false)
                  Text(
                    "${AppStrings.bullet}${showChecklistParams.checklistModel?.fuelLevel?.upToLower ?? ''}",
                    style: context.textTheme.titleSmall,
                  ),
                if (showChecklistParams.checklistModel?.fuelLevel?.isNotEmpty ??
                    false)
                  Gap(AppSize.h20),
                if (showChecklistParams.checklistModel?.other?.isNotEmpty ??
                    false)
                  Text(context.l10n.notes, style: titleStyle),
                if (showChecklistParams.checklistModel?.other?.isNotEmpty ??
                    false)
                  Text(
                    "${AppStrings.bullet}${showChecklistParams.checklistModel?.other?.toString() ?? ''}",
                    style: context.textTheme.titleSmall,
                  ),
                if (showChecklistParams.checklistModel?.images?.isNotEmpty ??
                    false)
                  ChecklistImageWidget(
                    checklistModel: showChecklistParams.checklistModel,
                  ),
                Gap(AppSize.h16),
                if (isNeedVerification)
                  const Divider(color: AppColors.ffDEE2E6),
                if (isNeedVerification && !isApproved)
                  Padding(
                    padding: EdgeInsets.only(bottom: AppSize.h20),
                    child: AppTextFormField(
                      maxLine: 4,
                      controller: showChecklistParams
                          .checkListProvider
                          .commentController,
                      title: context.l10n.addComments,
                      hintText: context.l10n.addAnyDiscrepancies,
                    ),
                  ),
                if (isApproved &&
                    (showChecklistParams
                            .checklistModel
                            ?.commentList
                            ?.isNotEmpty ??
                        false))
                  Text(context.l10n.comments, style: titleStyle),
                if (isApproved)
                  ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount:
                        showChecklistParams
                            .checklistModel
                            ?.commentList
                            ?.length ??
                        0,
                    padding: EdgeInsets.only(
                      bottom: AppSize.h30,
                      top: AppSize.h5,
                    ),
                    separatorBuilder: (context, index) => Gap(AppSize.h5),
                    itemBuilder: (context, index) {
                      final description = showChecklistParams
                          .checklistModel
                          ?.commentList?[index]
                          .description;
                      return description.isNotEmptyAndNotNull
                          ? Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(AppStrings.arrowWithoutSpace),
                                Flexible(
                                  child: Text(
                                    showChecklistParams
                                            .checklistModel
                                            ?.commentList?[index]
                                            .description ??
                                        '',
                                  ),
                                ),
                              ],
                            )
                          : const SizedBox.shrink();
                    },
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
