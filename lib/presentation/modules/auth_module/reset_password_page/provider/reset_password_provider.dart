import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/db/app_db.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/shared/repositories/account_repository.dart';
import 'package:transport_match/shared/rest_api/api_keys.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/utils/logger.dart';

class ResetPasswordProvider extends ChangeNotifier {
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  final formKeyResetPassword = GlobalKey<FormState>();

  final isPasswordShow = ValueNotifier(false);
  final isConfirmPasswordShow = ValueNotifier(false);
  final isShowLoader = ValueNotifier(false);
  CancelToken? resetPasswordCancelToken;
  bool _isClosed = false;

  Future<void> resetPasswordAPIcall({required BuildContext context}) async {
    '/// here 1'.logD;
    if (_isClosed) return;
    try {
      '/// here 2'.logD;
      isShowLoader.value = true;
      resetPasswordCancelToken?.cancel();
      resetPasswordCancelToken = CancelToken();

      Map<String, dynamic> data;
      data = {
        ApiKeys.resetType: PasswordType.FORGOT_PASSWORD.name,
        ApiKeys.password: passwordController.text,
      };
      '/// data = ${data.entries.map((e) => '${e.key} : ${e.value}').join(', ')}'.logD;
      '/// data cancelToken = ${resetPasswordCancelToken.hashCode}'.logD;
      '/// here 3'.logD;
      final request = ApiRequest(
        path: EndPoints.resetPassword,
        data: data,
        cancelToken: resetPasswordCancelToken,
      );
      '/// here 4'.logD;
      '/// here _isClosed = ${_isClosed}'.logD;
      if (_isClosed) return;
      final res =
          await Injector.instance<AccountRepository>().restPassword(request);
          '/// here 5'.logD;
      await res.when(
        success: (data) async {
          '/// here 6'.logD;
          if (_isClosed || (resetPasswordCancelToken?.isCancelled ?? true)) {
            '/// here 7'.logD;
            return;
          }
          '/// here 8'.logD;
          isShowLoader.value = false;
          context.l10n.passChanged.showSuccessAlert();
          Injector.instance<AppDB>().refreshToken = '';
          Injector.instance<AppDB>().token = '';
          '/// here 9'.logD;
          await AppNavigationService.pushAndRemoveAllPreviousRoute(
            context,
            AppRoutes.authBase,
            isBaseRoute: true,
          );
        },
        error: (exception) {
          '/// here 10'.logD;
          if (_isClosed || (resetPasswordCancelToken?.isCancelled ?? true)) {
            '/// here 11'.logD;
            return;
          }
          '/// here 12'.logD;
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      '/// here 13'.logD;
      if (_isClosed || (resetPasswordCancelToken?.isCancelled ?? true)) return;
      '/// here 14'.logD;
      isShowLoader.value = false;
      'resetPasswordAPIcall error: $e'.logE;
    }
  }

  @override
  void dispose() {
    _isClosed = true;
    resetPasswordCancelToken?.cancel();
    passwordController.dispose();
    confirmPasswordController.dispose();
    isPasswordShow.dispose();
    isConfirmPasswordShow.dispose();
    isShowLoader.dispose();
    super.dispose();
  }
}
