import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/db/app_db.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/auth_module/login_screen_page/provider/login_provider.dart';
import 'package:transport_match/presentation/modules/auth_module/login_screen_page/widgets/language_popup_widget.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/utils/logger.dart';
import 'package:transport_match/utils/validators/global_text_validator.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_loader.dart';
import 'package:transport_match/widgets/app_padding.dart';
import 'package:transport_match/widgets/app_textfield.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';

/// Login Screen
class LoginScreen extends StatelessWidget {
  /// Login Screen
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    '/// languagecode = ${Injector.instance<AppDB>().languageCode}'.logD;
    '/// languagecodeeee = ${Localizations.localeOf(context).languageCode}'.logD;
    return ChangeNotifierProvider<LoginProvider>(
      create: (context) => LoginProvider(),
      child: Builder(
        builder: (context) {
          final loginProvider = context.read<LoginProvider>();
          return Scaffold(
            appBar: const CustomAppBar(
              title: '',
              canPop: false,
              backgroundColor: AppColors.white,
              actions: [LanguagePopupWidget()],
            ),
            body: ValueListenableBuilder(
              valueListenable: loginProvider.isShowLoader,
              builder: (context, isLoading, child) {
                return AppLoader(isShowLoader: isLoading, child: child!);
              },
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: AppSize.appPadding),
                child: Column(
                  children: [
                    SizedBox(
                      height: context.height * 0.4,
                      width: double.infinity,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          AppAssets.imagesLogo.image(height: AppSize.h64),
                          Gap(AppSize.h32),
                          Text(
                            context.l10n.ultimateStressFreeCarTransport,
                            textAlign: TextAlign.center,
                            style: context.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                              fontSize: AppSize.sp14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Form(
                      key: loginProvider.formKeyLogin,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AppPadding(
                            bottom: AppSize.h24,
                            child: AppTextFormField(
                              hintText: context.l10n.enterYourEmail,
                              title: context.l10n.email,
                              keyboardType: TextInputType.emailAddress,
                              textAction: TextInputAction.next,
                              fillColor: AppColors.ffF8F9FA,
                              controller: loginProvider.emailController,
                              validator: (p0) => emailValidator().call(p0),
                            ),
                          ),
                          AppPadding(
                            bottom: AppSize.h8,
                            child: ValueListenableBuilder(
                              valueListenable: loginProvider.isPasswordShow,
                              builder: (context, isPasswordShow, _) {
                                return AppTextFormField(
                                  hintText: context.l10n.enterYourPassword,
                                  title: context.l10n.password,
                                  fillColor: AppColors.ffF8F9FA,
                                  obscureText:
                                      !loginProvider.isPasswordShow.value,
                                  controller: loginProvider.passwordController,
                                  validator: (p0) =>
                                      passwordValidator().call(p0),
                                  suffixIcon: GestureDetector(
                                    onTap: () {
                                      loginProvider.isPasswordShow.value =
                                          !loginProvider.isPasswordShow.value;
                                    },
                                    child: Icon(
                                      isPasswordShow
                                          ? Icons.visibility_outlined
                                          : Icons.visibility_off_outlined,
                                      color: AppColors.ff6C757D,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                          Align(
                            alignment: Alignment.centerRight,
                            child: InkWell(
                              onTap: () => AppNavigationService.pushNamed(
                                context,
                                AppRoutes.authForgotPasswordScreen,
                              ),
                              child: Text(
                                context.l10n.forgotPasswordQuestion,
                                style: context.textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.w500,
                                  color: AppColors.ff0087C7,
                                ),
                              ),
                            ),
                          ),
                          AppPadding(
                            top: AppSize.h46,
                            bottom: AppSize.h16,
                            child: AppButton(
                              text: context.l10n.logIn,
                              isBottomBtn: false,
                              onPressed: () {
                                if (loginProvider.formKeyLogin.currentState!
                                    .validate()) {
                                  loginProvider.loginAPICall(context: context);
                                }
                              },
                            ),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                context.l10n.doNtHaveAccount,
                                style: context.textTheme.titleSmall,
                              ),
                              InkWell(
                                onTap: () => AppNavigationService.pushNamed(
                                  context,
                                  AppRoutes.authSignupScreen,
                                ),
                                child: Text(
                                  ' ${context.l10n.signUp}',
                                  style: context.textTheme.titleSmall?.copyWith(
                                    fontWeight: FontWeight.w700,
                                    color: AppColors.primaryColor,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Gap(AppSize.h10),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
