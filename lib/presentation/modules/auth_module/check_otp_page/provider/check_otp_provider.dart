import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/db/app_db.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/models/signup_and_signin_model.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/shared/repositories/account_repository.dart';
import 'package:transport_match/shared/rest_api/rest_api.dart';
import 'package:transport_match/utils/app_common_functions.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/utils/logger.dart';

class CheckOtpProvider extends ChangeNotifier {
  CheckOtpProvider() {
    otpTimer = Timer.periodic(
      const Duration(seconds: 1),
      otpTimerCallback,
    );
  }

  final otpTextController = TextEditingController();
  final isShowLoader = ValueNotifier<bool>(false);
  CancelToken? verifyOtpCancelToken;
  CancelToken? resendCancelToken;
  bool _isClosed = false;

  final int otpTimeout = kDebugMode ? 10 : 59;
  Timer? otpTimer;
  final otpTimerValue = ValueNotifier<int>(0);

  void otpTimerCallback(Timer timer) {
    if (_isClosed) return;
    if (timer.tick >= otpTimeout) {
      otpTimer?.cancel();
      otpTimerValue.value = 0;
    } else {
      otpTimerValue.value = timer.tick;
    }
  }

  Future<void> verifyOTPApiCall({
    required String email,
    required BuildContext context,
    required bool isFromForgotPassword,
  }) async {
    if (otpTextController.text.trim().isEmpty) {
      context.l10n.pleaseEnterOtp.showErrorAlert();
    } else if (otpTextController.text.trim().length != 4) {
      context.l10n.pleaseEnterValidOtp.showErrorAlert();
    } else {
      if (_isClosed) return;
      try {
        isShowLoader.value = true;
        verifyOtpCancelToken?.cancel();
        verifyOtpCancelToken = CancelToken();
        final deviceId = await AppCommonFunctions.getDeviceId();
        final registrationId = await AppCommonFunctions.getFcmToken();
        if (_isClosed) return;
        Map<String, dynamic> data;
        data = {
          ApiKeys.email: email.trim().toLowerCase(),
          ApiKeys.verificationCode: otpTextController.text.trim(),
          ApiKeys.deviceId: deviceId,
          ApiKeys.deviceType: Platform.isAndroid
              ? DeviceType.ANDROID.name.toUpperCase()
              : Platform.isIOS
                  ? DeviceType.IOS.name.toUpperCase()
                  : 'UNKNOWN',
          ApiKeys.registrationId: registrationId,
        };
        final request = ApiRequest(
          path: EndPoints.verifyOtp,
          data: data,
          cancelToken: verifyOtpCancelToken,
        );
        final res =
            await Injector.instance<AccountRepository>().verifyOtp(request);
          
        await res.when(
          success: (data) async {
            '///// access before =  ${data.access}'.logD;
            Injector.instance<AppDB>().token = data.access ?? '';
              '///// access after =  ${Injector.instance<AppDB>().token}'.logD;
            if (_isClosed || (verifyOtpCancelToken?.isCancelled ?? true)) {
              return;
            }
            isShowLoader.value = false;
            Injector.instance<AppDB>().refreshToken = data.refresh ?? '';
            resetTimer();
            if (isFromForgotPassword) {
              await AppNavigationService.pushNamed(
                context,
                AppRoutes.authResetPasswordScreen,
                // extra: ResetPassParam(
                //   email: email,
                //   refreshToken: data.refresh ?? '',
                // ),
              );
            } else {
              Injector.instance<AppDB>().userModel =
                  SignUpAndSignInModel.fromJson(data.toJson());
              Injector.instance<AppDB>().token = data.access ?? '';
              '///// access after =  ${Injector.instance<AppDB>().token}'.logD;
              await AppNavigationService.pushAndRemoveAllPreviousRoute(
                context,
                AppRoutes.homeBase,
                isBaseRoute: true,
              );
            }
          },
          error: (exception) {
            '///// response of verifyOtpApiCall: ${exception}'.logD;
            if (_isClosed || (verifyOtpCancelToken?.isCancelled ?? true)) {
              return;
            }
            isShowLoader.value = false;
            exception.message.showErrorAlert();
          },
        );
      } catch (e) {
        if (_isClosed || (verifyOtpCancelToken?.isCancelled ?? true)) return;
        isShowLoader.value = false;
        'verifyOTPApiCall error: $e'.logE;
      }
    }
  }

  Future<void> resendOtpApiCall({
    required String email,
    required BuildContext context,
    required bool isFromForgotPassword,
  }) async {
    if (_isClosed) return;
    try {
      isShowLoader.value = true;
      resendCancelToken?.cancel();
      resendCancelToken = CancelToken();
      Map<String, dynamic> data;
      data = {
        ApiKeys.email: email.trim().toLowerCase(),
      };
      final request = ApiRequest(
        path: isFromForgotPassword
            ? EndPoints.sendForgotPasswordOtp
            : EndPoints.resendOtp,
        data: data,
        cancelToken: resendCancelToken,
      );
      final res =
          await Injector.instance<AccountRepository>().resendOtp(request);
      resetTimer();
      await res.when(
        success: (data) async {
          if (_isClosed || (resendCancelToken?.isCancelled ?? true)) return;
          isShowLoader.value = false;
          otpTimer = Timer.periodic(
            const Duration(seconds: 1),
            otpTimerCallback,
          );
          context.l10n.otpSentSuccess.showSuccessAlert();
        },
        error: (exception) {
          if (_isClosed || (resendCancelToken?.isCancelled ?? true)) return;
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (resendCancelToken?.isCancelled ?? true)) return;
      resetTimer();
      isShowLoader.value = false;
      'resendOtpApiCall error: $e'.logE;
    }
  }

  void resetTimer() {
    if (_isClosed) return;
    try {
      otpTimer?.cancel();
      otpTimerValue.value = 0;
      otpTextController.clear();
    } catch (e) {
      'resetTimer error: $e'.logE;
    }
  }

  @override
  void dispose() {
    _isClosed = true;
    verifyOtpCancelToken?.cancel();
    resendCancelToken?.cancel();
    otpTimer?.cancel();
    otpTextController.dispose();
    isShowLoader.dispose();
    otpTimerValue.dispose();
    super.dispose();
  }
}
