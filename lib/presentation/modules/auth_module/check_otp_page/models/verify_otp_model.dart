class VerifyOtpModel {
  const VerifyOtpModel({
    this.user,
    this.refresh,
    this.access,
  });

  factory VerifyOtpModel.fromJson(Map<String, dynamic> json) {
    return VerifyOtpModel(
      user: json['user'] == null
          ? null
          : UserModel.fromJson(json['user'] as Map<String, dynamic>? ?? {}),
      refresh: json['refresh'] == null ? '' : json['refresh'].toString(),
      access: json['access'] == null ? '' : json['access'].toString(),
    );
  }

  final UserModel? user;
  final String? refresh;
  final String? access;

  Map<String, dynamic> toJson() => {
        'user': user?.toJson(),
        'refresh': refresh,
        'access': access,
      };
}

class UserModel {
  UserModel({
    this.id,
    // this.userDetailData,
    this.profileUrl,
    this.role,
    this.dateJoined,
    this.firstName,
    this.lastName,
    this.email,
    this.country,
    this.lastLogin,
    this.isOnline,
    this.isEmailVerified,
    this.socketSessionId,
    this.isFirstTimeLogin,
    this.userDetailData,
    this.preferredLanguage,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] == null ? -1 : int.tryParse(json['id'].toString()),
      profileUrl:
          json['profile_url'] == null ? '' : json['profile_url'].toString(),
      role: json['role'] == null ? '' : json['role'].toString(),
      dateJoined: json['date_joined'] == null
          ? null
          : DateTime.tryParse(json['date_joined'].toString()),
      firstName:
          json['first_name'] == null ? '' : json['first_name'].toString(),
      lastName: json['last_name'] == null ? '' : json['last_name'].toString(),
      email: json['email'] == null ? '' : json['email'].toString(),
      country: json['country'] == null ? '' : json['country'].toString(),
      lastLogin:
          json['last_login'] == null ? '' : json['last_login'].toString(),
      isOnline: json['is_online'] == null
          ? false
          : bool.tryParse(json['is_online'].toString()),
      isEmailVerified: json['is_email_verified'] == null
          ? false
          : bool.tryParse(json['is_email_verified'].toString()),
      socketSessionId: json['socket_session_id'] == null
          ? ''
          : json['socket_session_id'].toString(),
      isFirstTimeLogin: json['is_first_time_login'] == null
          ? false
          : bool.tryParse(json['is_first_time_login'].toString()),
      userDetailData: json['user_detail_data'] == null
          ? null
          : UserDetailData.fromJson(
              json['user_detail_data'] as Map,
            ),
      preferredLanguage: json['preferred_language'] == null
          ? 'en'
          : json['preferred_language'].toString(),
    );
  }

  final int? id;
  final String? profileUrl;
  final String? role;
  final DateTime? dateJoined;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? country;
  final String? lastLogin;
  final bool? isOnline;
  final bool? isEmailVerified;
  final String? socketSessionId;
  final bool? isFirstTimeLogin;
  UserDetailData? userDetailData;
  final String? preferredLanguage;

  Map<String, dynamic> toJson() => {
        'id': id,
        'profile_url': profileUrl,
        'role': role,
        'date_joined': dateJoined?.toUtc().toIso8601String(),
        'first_name': firstName,
        'last_name': lastName,
        'email': email,
        'country': country,
        'last_login': lastLogin,
        'is_online': isOnline,
        'is_email_verified': isEmailVerified,
        'socket_session_id': socketSessionId,
        'is_first_time_login': isFirstTimeLogin,
        'user_detail_data': userDetailData?.toJson(),
        'preferred_language': preferredLanguage,
      };
}

// THIS IS FOR PROVIDER ONLY, NOT FOR USER
class UserDetailData {
  const UserDetailData({
    this.id,
    this.stripeCustomerId,
    this.bookingNumber,
    this.bookingCountryCode,
  });

  // ignore: strict_raw_type
  factory UserDetailData.fromJson(Map json) {
    return UserDetailData(
      id: json['id'] as int?,
      stripeCustomerId: json['stripe_account_id'] as String?,
      bookingNumber: json['booking_contact_number'] as String?,
      bookingCountryCode:
          json['booking_contact_number_country_code'] as String?,
    );
  }

  final int? id;
  final String? stripeCustomerId;
  final String? bookingNumber;
  final String? bookingCountryCode;

  Map<String, dynamic> toJson() => {
        'id': id,
        'stripe_account_id': stripeCustomerId,
        'booking_contact_number': bookingNumber,
        'booking_contact_number_country_code': bookingCountryCode,
      };
}
