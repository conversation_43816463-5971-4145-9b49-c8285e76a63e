import 'package:flutter/material.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/presentation/modules/auth_module/check_otp_page/models/check_otp_params.dart';
import 'package:transport_match/router/app_navigation_service.dart';
import 'package:transport_match/router/app_routes.dart';
import 'package:transport_match/shared/repositories/account_repository.dart';
import 'package:transport_match/shared/rest_api/api_keys.dart';
import 'package:transport_match/shared/rest_api/api_request.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/utils/logger.dart';

class ForgotPasswordProvider extends ChangeNotifier {
  final emailController = TextEditingController();
  final formKeyForgotPassword = GlobalKey<FormState>();
  final isShowLoader = ValueNotifier<bool>(false);
  CancelToken? forgotPasswordCancelToken;
  bool _isClosed = false;

  Future<void> forgotPasswordAPIcall({required BuildContext context}) async {
    if (_isClosed) return;
    try {
      isShowLoader.value = true;
      forgotPasswordCancelToken?.cancel();
      forgotPasswordCancelToken = CancelToken();
      Map<String, dynamic> data;
      data = {
        ApiKeys.email: emailController.text.trim().toLowerCase(),
      };
      final request = ApiRequest(
        path: EndPoints.sendForgotPasswordOtp,
        data: data,
        cancelToken: forgotPasswordCancelToken,
      );

      // Time consuming operation check
      // if (_isClosed) return;
      // await Future.delayed(const Duration(seconds: 2));

      if (_isClosed) return;
      final res = await Injector.instance<AccountRepository>()
          .forgotPasswordSendOtp(request);
          '///// response = ${res}'.logD;
      await res.when(
        success: (data) async {
          if (_isClosed || (forgotPasswordCancelToken?.isCancelled ?? false)) {
            return;
          }
          isShowLoader.value = false;
          await AppNavigationService.pushNamed(
            context,
            AppRoutes.authCheckOtpScreen,
            extra: CheckOtpParams(
              email: emailController.text.trim().toLowerCase(),
              isFromForgotPassword: true,
            ),
          );
        },
        error: (exception) {
          if (_isClosed || (forgotPasswordCancelToken?.isCancelled ?? false)) {
            return;
          }
          isShowLoader.value = false;
          exception.message.showErrorAlert();
        },
      );
    } catch (e) {
      if (_isClosed || (forgotPasswordCancelToken?.isCancelled ?? false)) {
        return;
      }
      isShowLoader.value = false;
      'forgotPasswordAPIcall error: $e'.logE;
    }
  }

  @override
  void dispose() {
    _isClosed = true;
    forgotPasswordCancelToken?.cancel();
    emailController.dispose();
    isShowLoader.dispose();
    super.dispose();
  }
}
