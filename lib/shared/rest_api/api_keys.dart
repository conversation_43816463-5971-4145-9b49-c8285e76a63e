import 'package:flutter/foundation.dart' show immutable;

/// This class contains all keys name used to store values locally
@immutable
final class ApiKeys {
  const ApiKeys._();

  /// user parameters
  static const email = 'email';
  static const password = 'password';
  static const registrationId = 'registration_id';

  /// preffered language
  static const preferredLanguage = 'preferred_language';

  ///FCM token
  static const firstName = 'first_name';
  static const deviceId = 'device_id';
  static const refreshToken = 'refresh_token';
  static const deviceType = 'device_type';

  /// ENUM
  static const role = 'role';
  static const verificationCode = 'verification_code';
  static const resetType = 'reset_type';

  /// ENUM
  static const oldPassword = 'old_password';
  static const refresh = 'refresh';
  static const latitude = 'latitude';
  static const longitude = 'longitude';

  ///
  static const slot = 'slot';
  static const trip = 'trip';
  static const startStopLocation = 'start_stop_location';
  static const endStopLocation = 'end_stop_location';
  static const sessionId = 'session_id';
  static const bookingSessionId = 'booking_session_id';
  static const name = 'name';
  static const idProofType = 'id_proof_type';
  static const awsImageKey = 'aws_image_key';
  static const description = 'description';
  static const assigneeType = 'assignee_type';
  static const booking = 'booking';
  static const assignees = 'assignees';
  static const contentType = 'Content-type';
  static const fileExtension = 'file_extension';
  static const folderName = 'folder_name';
  static const numberOfUrl = 'number_of_url';
  static const putUrl = 'put_url';
  static const keyName = 'key_name';
  static const brandId = 'brand_id';
  static const provider = 'provider';
  static const bookingDetail = 'booking_detail';
  static const rating = 'rating';
  static const suggestion = 'suggestion';
  static const time = 'time';
  static const bookingNumber = 'booking_contact_number';
  static const bookingNumberCode = 'booking_contact_number_country_code';
}
