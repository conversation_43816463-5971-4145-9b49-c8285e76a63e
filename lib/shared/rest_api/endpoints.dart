// ignore_for_file: constant_identifier_names, public_member_api_docs

/// API endpoints
final class EndPoints {
  /// Api base url
  // static const String baseUrl = 'http://192.168.0.19:8000/api/';
  // static const String baseUrl = 'http://192.168.0.72:8001/api/';
  // static const String baseUrl =
  //     'https://f145-43-251-72-102.ngrok-free.app/api/';

  // ^ Header value
  static const Header_Content_Key = 'Content-Type';
  static const Header_Content_value = 'application/json';
  static const Header_Content_valueFormData = 'application/form-data';
  static const Header_Content_valueUrlEncoded =
      'application/x-www-form-urlencoded';
  static const Header_Authorization_KEY = 'Authorization';
  static const Header_Accept_Key = 'Accept';
  static const BEARER_TOKEN = 'Bearer ';

  /// Endpoint for fetching users
  static const String signup = 'auth/public/sign-up/';
  static const String signin = 'auth/public/sign-in/';
  static const String logout = 'auth/logout/';
  static const String getUserInfo = 'auth/user-profile-detail/';
  static const String getCustomerDetail = 'auth/customer-detail/';
  static const String stripeCustomerPortalUrl =
      'auth/stripe-customer-portal-url/';
  static const String deleteAccount = 'auth/account/';
  static const String verifyOtp = 'auth/public/verify-otp/';
  static const String resendOtp = 'auth/public/resend-otp/';
  static const String sendForgotPasswordOtp =
      'auth/public/send-forgot-password-otp/';
  static const String resetPassword = 'auth/public/reset-password/';
  static const String generateUrl = 'auth/generate-presigned-url/';
  static const String refreshToken = 'token/refresh/';
  static const String listNearByLocation =
      'customer/list-near-by-stop-locations/';

  /// Update language
  static const String updateLanguage = 'customer/preferred-language/update/';

  /// Endpoint for fetching vehicle information
  static const String carBrandsAll = 'customer/car-brands/all/';
  static const String listCarForCreation = 'customer/list-car-for-creation/';
  static const String createExclusiveBook =
      'customer/create-exclusive-booking/';
  static const String createCarVerification =
      'customer/create-booking-for-car-verification/';
  static const String getSharedTripPaymentSummary =
      'customer/shared-trip-payment-summary/';
  static const String getExclusiveTripPaymentSummary =
      'customer/exclusive-trip-payment-summary/';
  static const String getInsurance = 'customer/insurances/';
  static const String getBookingUrl =
      'customer/create-shared-trip-initial-payment-checkout/';
  static const String getExclusiveBookingUrl =
      'customer/create-exclusive-trip-initial-payment-checkout/';
  static const String getRemainBookingUrl =
      'customer/create-shared-trip-remaining-payment-checkout/';
  static const String getRemainExclusiveBookingUrl =
      'customer/create-exclusive-trip-remaining-payment-checkout/';
  static const String registerTempSlot = 'customer/register-temporary-slot/';
  static const String createBookingSession = 'customer/create-booking-session/';
  static const String createBookingAssign = 'customer/create-booking-assignee/';
  static const String unreadNotificationCount =
      'customer/notifications/unread/';
  static const String rateProvider = 'customer/rate-transporter/';
  static const String customerSupport = 'customer/complaint/create/';
  static const String complaintDetails = 'customer/complaints/';
  static const String checklistComments = 'customer/checklist-comments/create/';
  static String findTransporter({
    required String startDate,
    required String endDate,
    required String startLocation,
    required String endLocation,
    required String requiredSlot,
    required String minSize,
    required String maxSize,
    String? allVehicleIn,
    String? winch,
    String? rating,
    String? price,
    String? time,
    String? lowestPrice,
    String? isExceptionsSupported,
  }) => _finalLinkWithout(
    'customer/list-shared-trip-with-slot-availability/?${_string('customer_start_date', startDate)}'
    '${_string('customer_end_date', endDate)}${_string('start_stop_location', startLocation)}'
    '${_string('minimum_required_slot_size', minSize)}${_string('is_exceptions_supported', isExceptionsSupported)}'
    '${_string('end_stop_location', endLocation)}${_string('all_vehicle_in_one_trip', allVehicleIn)}'
    '${_string('required_slot', requiredSlot)}${_string('winch', winch)}${_string('rating', rating)}'
    '${_string('price', price)}${_string('time', time)}${_string('lowest_per_km', lowestPrice)}',
  );
  static String findTransporterForExclusive({
    required String bookingId,
    String? rating,
    String? price,
    String? time,
    String? lowestPrice,
    String? team,
    String? winch,
    String? isExceptionsSupported,
  }) => _finalLinkWithout(
    'customer/list-exclusive-trip-with-slot-availability/?${_string('booking_id', bookingId)}${_string('rating', rating)}'
    '${_string('price', price)}${_string('time', time)}${_string('lowest_per_km', lowestPrice)}${_string('winch', winch)}'
    '${_string('required_slot', team)}${_string('is_exceptions_supported', isExceptionsSupported)}',
  );
  static const String getRequestedTrip = 'customer/list-requested-booking/';
  static const String getAcceptedTrip = 'customer/list-accepted-bookings/';
  static const String getCancelledBookings =
      'customer/list-cancelled-bookings/';
  static const String getBookingDetail = 'customer/booking/';
  static const String requestedTripDetail = 'customer/requested-booking/';
  static const String sharedTripPaymentSettlementCheckout =
      'customer/create-shared-trip-refund-settlement-checkout/';
  static const String exclusiveTripPaymentSettlementCheckout =
      'customer/create-exclusive-trip-refund-settlement-checkout/';
  static String getSharedTripPaymentSettlementSummary(String bookingId) =>
      'customer/booking/booking-detail/$bookingId/shared-trip-refund-settlement-payment-summary/';
  static String getExclusiveTripPaymentSettlementSummary(String bookingId) =>
      'customer/booking/booking-detail/$bookingId/exclusive-trip-refund-settlement-payment-summary/';
  static const String sharedTripRemainingPaymentSummary =
      'customer/shared-trip-remaining-payment-summary/booking/';
  static const String exclusiveTripRemainingPaymentSummary =
      'customer/exclusive-trip-remaining-payment-summary/booking/';
  static String getPaymentSummary(String bookingId) =>
      'customer/booking/booking_detail/booked_car/$bookingId/payment-summary/';
  static String getCheckList(String bookingId) =>
      'customer/booking/booked-car/$bookingId/checklists/';
  static const String notificationMarkRead =
      'customer/notifications/mark-readed/';
  static String cancelReqTrip(String bookingId) =>
      'customer/cancel-requested-booking/$bookingId/delete/';
  static const String notifications = 'customer/notifications/';
  static const String createNotes = 'customer/booking-notes/create/';
  static const String getRefundRequests = 'customer/refund-requests/';
  static String deleteNotes(String id) => 'customer/booking-notes/$id/delete/';
  static const String getCostOfPickUp =
      'customer/pick-up-and-drop-off-service-fee/';
  static String updateRestedTrip(String bookingId) =>
      'customer/rested-booking/$bookingId/update/';

  /// Chat
  static String getOldChatMessages(String id) =>
      'customer/chat/$id/messages/?limit=20';
  static String markAllChatMessagesReaded(String id) =>
      'customer/chat/$id/messages/mark-as-readed/';
  static String customerBookingCancel = 'customer/booking/cancel/';
  static String customerBookingDetailCancel =
      'customer/booking/booking-detail/cancel/';
  static String customerBookedCarCancel =
      'customer/booking/booking-detail/booked-car/cancel/';
}

String? _string(String path, String? value) =>
    value != null && value.isNotEmpty ? '$path=$value&' : '';

String _finalLinkWithout(String link) =>
    link.lastIndexOf('&') == link.length - 1
    ? link.replaceRange(link.length - 1, null, '')
    : '$link/';
