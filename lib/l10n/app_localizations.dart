import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_es.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('es'),
  ];

  /// No description provided for @helloWorld.
  ///
  /// In en, this message translates to:
  /// **'hello world'**
  String get helloWorld;

  /// No description provided for @byeWorld.
  ///
  /// In en, this message translates to:
  /// **'bye world'**
  String get byeWorld;

  /// No description provided for @just_now.
  ///
  /// In en, this message translates to:
  /// **'Just now'**
  String get just_now;

  /// No description provided for @min_ago.
  ///
  /// In en, this message translates to:
  /// **'Minute ago'**
  String get min_ago;

  /// No description provided for @hour_ago.
  ///
  /// In en, this message translates to:
  /// **'Hour ago'**
  String get hour_ago;

  /// No description provided for @day_ago.
  ///
  /// In en, this message translates to:
  /// **'Day ago'**
  String get day_ago;

  /// No description provided for @week_ago.
  ///
  /// In en, this message translates to:
  /// **'Week ago'**
  String get week_ago;

  /// No description provided for @year_ago.
  ///
  /// In en, this message translates to:
  /// **'Year ago'**
  String get year_ago;

  /// No description provided for @today.
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get today;

  /// No description provided for @yesterday.
  ///
  /// In en, this message translates to:
  /// **'Yesterday'**
  String get yesterday;

  /// No description provided for @copied.
  ///
  /// In en, this message translates to:
  /// **'Copied'**
  String get copied;

  /// No description provided for @pleaseEnterEmail.
  ///
  /// In en, this message translates to:
  /// **'Please enter email'**
  String get pleaseEnterEmail;

  /// No description provided for @pleaseEnterValidEmail.
  ///
  /// In en, this message translates to:
  /// **'Please enter valid email'**
  String get pleaseEnterValidEmail;

  /// No description provided for @pleaseEnterCurrentPassword.
  ///
  /// In en, this message translates to:
  /// **'Please enter current password'**
  String get pleaseEnterCurrentPassword;

  /// No description provided for @pleaseEnterPassword.
  ///
  /// In en, this message translates to:
  /// **'Please enter password'**
  String get pleaseEnterPassword;

  /// No description provided for @passwordMinLength.
  ///
  /// In en, this message translates to:
  /// **'Password should be at least 8 characters.'**
  String get passwordMinLength;

  /// No description provided for @passwordShouldBeAtLeast8Characters.
  ///
  /// In en, this message translates to:
  /// **'Password should be at least 8 characters.'**
  String get passwordShouldBeAtLeast8Characters;

  /// No description provided for @newPasswordAndOldPasswordCannotBeSame.
  ///
  /// In en, this message translates to:
  /// **'New password and old password cannot be same'**
  String get newPasswordAndOldPasswordCannotBeSame;

  /// No description provided for @otpMinLength.
  ///
  /// In en, this message translates to:
  /// **'Please enter valid OTP'**
  String get otpMinLength;

  /// No description provided for @pleaseEnterValidUsername.
  ///
  /// In en, this message translates to:
  /// **'Please enter valid username'**
  String get pleaseEnterValidUsername;

  /// No description provided for @usernameMinLength.
  ///
  /// In en, this message translates to:
  /// **'Please enter valid username'**
  String get usernameMinLength;

  /// No description provided for @pleaseEnterName.
  ///
  /// In en, this message translates to:
  /// **'Please enter name'**
  String get pleaseEnterName;

  /// No description provided for @nameMinLength.
  ///
  /// In en, this message translates to:
  /// **'Please enter name'**
  String get nameMinLength;

  /// No description provided for @pleaseEnterFirstName.
  ///
  /// In en, this message translates to:
  /// **'Please enter first name'**
  String get pleaseEnterFirstName;

  /// No description provided for @firstNameMinLength.
  ///
  /// In en, this message translates to:
  /// **'Please enter first name'**
  String get firstNameMinLength;

  /// No description provided for @pleaseEnterLastName.
  ///
  /// In en, this message translates to:
  /// **'Please enter last name'**
  String get pleaseEnterLastName;

  /// No description provided for @lastNameMinLength.
  ///
  /// In en, this message translates to:
  /// **'Please enter last name'**
  String get lastNameMinLength;

  /// No description provided for @passwordDoesNotMatch.
  ///
  /// In en, this message translates to:
  /// **'Password does not match'**
  String get passwordDoesNotMatch;

  /// No description provided for @accountRegisterSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Account registered successfully, Please verify your email.'**
  String get accountRegisterSuccessfully;

  /// No description provided for @passwordIncludeLetterNumberSymbol.
  ///
  /// In en, this message translates to:
  /// **'Password must include at least one uppercase letter, one lowercase letter, one number, and one special character.'**
  String get passwordIncludeLetterNumberSymbol;

  /// No description provided for @checkYourEmail.
  ///
  /// In en, this message translates to:
  /// **'Check your email'**
  String get checkYourEmail;

  /// No description provided for @sentVerificationCode.
  ///
  /// In en, this message translates to:
  /// **'We\'ve sent a verification code on '**
  String get sentVerificationCode;

  /// No description provided for @verifyOtp.
  ///
  /// In en, this message translates to:
  /// **'Verify OTP'**
  String get verifyOtp;

  /// No description provided for @pleaseEnterOtp.
  ///
  /// In en, this message translates to:
  /// **'Please enter OTP!'**
  String get pleaseEnterOtp;

  /// No description provided for @pleaseEnterValidOtp.
  ///
  /// In en, this message translates to:
  /// **'Please enter valid OTP!'**
  String get pleaseEnterValidOtp;

  /// No description provided for @didNtReceiveEmail.
  ///
  /// In en, this message translates to:
  /// **'Didn\'t received the email? '**
  String get didNtReceiveEmail;

  /// No description provided for @clickToResend.
  ///
  /// In en, this message translates to:
  /// **'Click to resend'**
  String get clickToResend;

  /// No description provided for @resetPasswordInstructions.
  ///
  /// In en, this message translates to:
  /// **'We\'ll send you OTP in your email'**
  String get resetPasswordInstructions;

  /// No description provided for @sendAnEmail.
  ///
  /// In en, this message translates to:
  /// **'Send an email'**
  String get sendAnEmail;

  /// No description provided for @ultimateStressFreeCarTransport.
  ///
  /// In en, this message translates to:
  /// **'The ultimate stress-free car\ntransport experience.'**
  String get ultimateStressFreeCarTransport;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @enterYourEmail.
  ///
  /// In en, this message translates to:
  /// **'Enter your email'**
  String get enterYourEmail;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// No description provided for @enterYourPassword.
  ///
  /// In en, this message translates to:
  /// **'Enter your password'**
  String get enterYourPassword;

  /// No description provided for @enterYourConfirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Enter your confirm password'**
  String get enterYourConfirmPassword;

  /// No description provided for @forgotPassword.
  ///
  /// In en, this message translates to:
  /// **'Forgot Password'**
  String get forgotPassword;

  /// No description provided for @forgotPasswordQuestion.
  ///
  /// In en, this message translates to:
  /// **'Forgot Password?'**
  String get forgotPasswordQuestion;

  /// No description provided for @logIn.
  ///
  /// In en, this message translates to:
  /// **'Log In'**
  String get logIn;

  /// No description provided for @doNtHaveAccount.
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account?'**
  String get doNtHaveAccount;

  /// No description provided for @signUp.
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get signUp;

  /// No description provided for @setPassword.
  ///
  /// In en, this message translates to:
  /// **'Set password'**
  String get setPassword;

  /// No description provided for @setNewPassword.
  ///
  /// In en, this message translates to:
  /// **'Go ahead and set a new password'**
  String get setNewPassword;

  /// No description provided for @confirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPassword;

  /// No description provided for @pleaseEnterConfirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Please enter confirm password'**
  String get pleaseEnterConfirmPassword;

  /// No description provided for @pleaseEnterDetails.
  ///
  /// In en, this message translates to:
  /// **'Please enter your details to create an account'**
  String get pleaseEnterDetails;

  /// No description provided for @name.
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// No description provided for @enterYourName.
  ///
  /// In en, this message translates to:
  /// **'Enter your name'**
  String get enterYourName;

  /// No description provided for @createAccount.
  ///
  /// In en, this message translates to:
  /// **'Create an account'**
  String get createAccount;

  /// No description provided for @alreadyHaveAccount.
  ///
  /// In en, this message translates to:
  /// **'Already have an account?'**
  String get alreadyHaveAccount;

  /// No description provided for @customer_support.
  ///
  /// In en, this message translates to:
  /// **'Customer Support'**
  String get customer_support;

  /// No description provided for @payments.
  ///
  /// In en, this message translates to:
  /// **'Payments'**
  String get payments;

  /// No description provided for @past_purchase.
  ///
  /// In en, this message translates to:
  /// **'Past Purchase'**
  String get past_purchase;

  /// No description provided for @sign_out.
  ///
  /// In en, this message translates to:
  /// **'Sign Out'**
  String get sign_out;

  /// No description provided for @delete_account.
  ///
  /// In en, this message translates to:
  /// **'Delete Account'**
  String get delete_account;

  /// No description provided for @edit_details.
  ///
  /// In en, this message translates to:
  /// **'Edit Details'**
  String get edit_details;

  /// No description provided for @change_password.
  ///
  /// In en, this message translates to:
  /// **'Change Password'**
  String get change_password;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @enter_your_name.
  ///
  /// In en, this message translates to:
  /// **'Enter your name'**
  String get enter_your_name;

  /// No description provided for @enter_your_email.
  ///
  /// In en, this message translates to:
  /// **'Enter your email'**
  String get enter_your_email;

  /// No description provided for @enter_your_password.
  ///
  /// In en, this message translates to:
  /// **'Enter your password'**
  String get enter_your_password;

  /// No description provided for @set_new_password.
  ///
  /// In en, this message translates to:
  /// **'Set new password'**
  String get set_new_password;

  /// No description provided for @go_ahead_and_set_a_new_password.
  ///
  /// In en, this message translates to:
  /// **'Go ahead and set a new password'**
  String get go_ahead_and_set_a_new_password;

  /// No description provided for @old_password.
  ///
  /// In en, this message translates to:
  /// **'Old Password'**
  String get old_password;

  /// No description provided for @uploadCarImg.
  ///
  /// In en, this message translates to:
  /// **'Upload car images'**
  String get uploadCarImg;

  /// No description provided for @new_password.
  ///
  /// In en, this message translates to:
  /// **'New Password'**
  String get new_password;

  /// No description provided for @confirm_password.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirm_password;

  /// No description provided for @please_enter_confirm_password.
  ///
  /// In en, this message translates to:
  /// **'Please enter confirm password'**
  String get please_enter_confirm_password;

  /// No description provided for @transporter.
  ///
  /// In en, this message translates to:
  /// **'Transporter'**
  String get transporter;

  /// No description provided for @total_trip_cost.
  ///
  /// In en, this message translates to:
  /// **'Total Trip Cost'**
  String get total_trip_cost;

  /// No description provided for @no_of_vehicles.
  ///
  /// In en, this message translates to:
  /// **'No. Of Vehicles'**
  String get no_of_vehicles;

  /// No description provided for @equipment_type.
  ///
  /// In en, this message translates to:
  /// **'Equipment type'**
  String get equipment_type;

  /// No description provided for @vehicles_info.
  ///
  /// In en, this message translates to:
  /// **'Vehicles Info'**
  String get vehicles_info;

  /// No description provided for @details.
  ///
  /// In en, this message translates to:
  /// **'Details'**
  String get details;

  /// No description provided for @car_brand.
  ///
  /// In en, this message translates to:
  /// **'Car brand'**
  String get car_brand;

  /// No description provided for @car_model.
  ///
  /// In en, this message translates to:
  /// **'Car model'**
  String get car_model;

  /// No description provided for @car_serial.
  ///
  /// In en, this message translates to:
  /// **'Car serial #'**
  String get car_serial;

  /// No description provided for @car_year.
  ///
  /// In en, this message translates to:
  /// **'Car year'**
  String get car_year;

  /// No description provided for @view_details.
  ///
  /// In en, this message translates to:
  /// **'View Details'**
  String get view_details;

  /// No description provided for @stockLocations.
  ///
  /// In en, this message translates to:
  /// **'Stock Locations'**
  String get stockLocations;

  /// No description provided for @stockLocation.
  ///
  /// In en, this message translates to:
  /// **'Stock Location'**
  String get stockLocation;

  /// No description provided for @originStockLocation.
  ///
  /// In en, this message translates to:
  /// **'Origin Stock Location'**
  String get originStockLocation;

  /// No description provided for @chooseOriginStockLocation.
  ///
  /// In en, this message translates to:
  /// **'Choose origin stock location'**
  String get chooseOriginStockLocation;

  /// No description provided for @pleaseSelectPickupDate.
  ///
  /// In en, this message translates to:
  /// **'Please select pickup date'**
  String get pleaseSelectPickupDate;

  /// No description provided for @pleaseSelectDeliveryDate.
  ///
  /// In en, this message translates to:
  /// **'Please select delivery date'**
  String get pleaseSelectDeliveryDate;

  /// No description provided for @pleaseSelectDelivery.
  ///
  /// In en, this message translates to:
  /// **'Please select delivery date'**
  String get pleaseSelectDelivery;

  /// No description provided for @dropStockLocation.
  ///
  /// In en, this message translates to:
  /// **'Drop Stock Location'**
  String get dropStockLocation;

  /// No description provided for @chooseDropStockLocation.
  ///
  /// In en, this message translates to:
  /// **'Choose drop stock location'**
  String get chooseDropStockLocation;

  /// No description provided for @vehicleInfo.
  ///
  /// In en, this message translates to:
  /// **'Vehicles Info'**
  String get vehicleInfo;

  /// No description provided for @vehicleBrand.
  ///
  /// In en, this message translates to:
  /// **'Vehicle brand'**
  String get vehicleBrand;

  /// No description provided for @chooseVehicleBrand.
  ///
  /// In en, this message translates to:
  /// **'Choose vehicle brand'**
  String get chooseVehicleBrand;

  /// No description provided for @vehicleModel.
  ///
  /// In en, this message translates to:
  /// **'Vehicle model'**
  String get vehicleModel;

  /// No description provided for @chooseVehicleModel.
  ///
  /// In en, this message translates to:
  /// **'Choose vehicle model'**
  String get chooseVehicleModel;

  /// No description provided for @vehicleYear.
  ///
  /// In en, this message translates to:
  /// **'Vehicle year'**
  String get vehicleYear;

  /// No description provided for @chooseVehicleYear.
  ///
  /// In en, this message translates to:
  /// **'Choose vehicle year'**
  String get chooseVehicleYear;

  /// No description provided for @vehicleCondition.
  ///
  /// In en, this message translates to:
  /// **'Vehicle condition'**
  String get vehicleCondition;

  /// No description provided for @chooseVehicleCondition.
  ///
  /// In en, this message translates to:
  /// **'Write vehicle condition'**
  String get chooseVehicleCondition;

  /// No description provided for @pleaseDescribeTheIssue.
  ///
  /// In en, this message translates to:
  /// **'Please describe the issue.'**
  String get pleaseDescribeTheIssue;

  /// No description provided for @writeIssueDetailsHere.
  ///
  /// In en, this message translates to:
  /// **'Write issue details here'**
  String get writeIssueDetailsHere;

  /// No description provided for @vehicleSerialNo.
  ///
  /// In en, this message translates to:
  /// **'Vehicle serial no.'**
  String get vehicleSerialNo;

  /// No description provided for @vehicleSerialNumber.
  ///
  /// In en, this message translates to:
  /// **'Vehicle serial number'**
  String get vehicleSerialNumber;

  /// No description provided for @iNeedMyCarToBePickedUpAndTakenToTheStockLocation.
  ///
  /// In en, this message translates to:
  /// **'I need my car to be picked up and taken to the stock location.'**
  String get iNeedMyCarToBePickedUpAndTakenToTheStockLocation;

  /// No description provided for @enterPickupAddress.
  ///
  /// In en, this message translates to:
  /// **'Enter pickup address'**
  String get enterPickupAddress;

  /// No description provided for @towingCost.
  ///
  /// In en, this message translates to:
  /// **'Towing cost'**
  String get towingCost;

  /// No description provided for @drivingCostWithOperator.
  ///
  /// In en, this message translates to:
  /// **'Driving cost with operator'**
  String get drivingCostWithOperator;

  /// No description provided for @addAnotherVehicle.
  ///
  /// In en, this message translates to:
  /// **'Add Another Vehicle'**
  String get addAnotherVehicle;

  /// No description provided for @pickupAndDeliveryDates.
  ///
  /// In en, this message translates to:
  /// **'Pickup & Delivery Dates'**
  String get pickupAndDeliveryDates;

  /// No description provided for @pickupDate.
  ///
  /// In en, this message translates to:
  /// **'Pickup date'**
  String get pickupDate;

  /// No description provided for @deliveryDate.
  ///
  /// In en, this message translates to:
  /// **'Delivery date'**
  String get deliveryDate;

  /// No description provided for @transportAllVehicleInOneTruck.
  ///
  /// In en, this message translates to:
  /// **'Transport all vehicle in one truck'**
  String get transportAllVehicleInOneTruck;

  /// No description provided for @vehicleVersion.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Version'**
  String get vehicleVersion;

  /// No description provided for @passChanged.
  ///
  /// In en, this message translates to:
  /// **'Password changed successfully'**
  String get passChanged;

  /// No description provided for @findTransporter.
  ///
  /// In en, this message translates to:
  /// **'Find Transporter'**
  String get findTransporter;

  /// No description provided for @enterVBrandName.
  ///
  /// In en, this message translates to:
  /// **'Enter brand of your vehicle'**
  String get enterVBrandName;

  /// No description provided for @enterBrandName.
  ///
  /// In en, this message translates to:
  /// **'Enter brand of your {enterBrandName} vehicle'**
  String enterBrandName(String enterBrandName);

  /// No description provided for @chooseVBrandName.
  ///
  /// In en, this message translates to:
  /// **'Choose brand of your vehicle'**
  String get chooseVBrandName;

  /// No description provided for @chooseBrandName.
  ///
  /// In en, this message translates to:
  /// **'Choose brand of your {brandName} vehicle'**
  String chooseBrandName(String brandName);

  /// No description provided for @enterVYear.
  ///
  /// In en, this message translates to:
  /// **'Enter year of your vehicle'**
  String get enterVYear;

  /// No description provided for @enterVehicleYear.
  ///
  /// In en, this message translates to:
  /// **'Enter year of your {enterVehicleYear} vehicle'**
  String enterVehicleYear(String enterVehicleYear);

  /// No description provided for @chooseVYear.
  ///
  /// In en, this message translates to:
  /// **'Choose year of your vehicle'**
  String get chooseVYear;

  /// No description provided for @pleaseChooseVehicleYear.
  ///
  /// In en, this message translates to:
  /// **'Choose year of your {vehicleYear} vehicle'**
  String pleaseChooseVehicleYear(String vehicleYear);

  /// No description provided for @enterVModel.
  ///
  /// In en, this message translates to:
  /// **'Enter model of your vehicle'**
  String get enterVModel;

  /// No description provided for @enterVehicleModel.
  ///
  /// In en, this message translates to:
  /// **'Enter model of your {enterVehicleModel} vehicle'**
  String enterVehicleModel(String enterVehicleModel);

  /// No description provided for @chooseVModel.
  ///
  /// In en, this message translates to:
  /// **'Choose model of your vehicle'**
  String get chooseVModel;

  /// No description provided for @pleaseChooseVehicleModel.
  ///
  /// In en, this message translates to:
  /// **'Choose model of your {vehicleModel} vehicle'**
  String pleaseChooseVehicleModel(String vehicleModel);

  /// No description provided for @pleaseEnterVSerialNumber.
  ///
  /// In en, this message translates to:
  /// **'Please enter serial number of your vehicle'**
  String get pleaseEnterVSerialNumber;

  /// No description provided for @pleaseEnterSerialNumber.
  ///
  /// In en, this message translates to:
  /// **'Please enter serial number of your {serialNumber} vehicle'**
  String pleaseEnterSerialNumber(String serialNumber);

  /// No description provided for @pleasePickUpAddress.
  ///
  /// In en, this message translates to:
  /// **'Please enter your pickup address'**
  String get pleasePickUpAddress;

  /// No description provided for @bookingDataVerification.
  ///
  /// In en, this message translates to:
  /// **'Booking data has been successfully submitted for car verification.'**
  String get bookingDataVerification;

  /// No description provided for @exclusiveCreated.
  ///
  /// In en, this message translates to:
  /// **'Exclusive booking created successfully.'**
  String get exclusiveCreated;

  /// No description provided for @pleasePickUpZip.
  ///
  /// In en, this message translates to:
  /// **'Please enter zip code of your pickup address'**
  String get pleasePickUpZip;

  /// No description provided for @pleaseEnterDropUserName.
  ///
  /// In en, this message translates to:
  /// **'Please enter drop user name'**
  String get pleaseEnterDropUserName;

  /// No description provided for @invalidDropUserName.
  ///
  /// In en, this message translates to:
  /// **'Please enter valid drop user name'**
  String get invalidDropUserName;

  /// No description provided for @pleaseEnterDropUserDocType.
  ///
  /// In en, this message translates to:
  /// **'Please enter drop user document type'**
  String get pleaseEnterDropUserDocType;

  /// No description provided for @pleaseEnterDropUserDoc.
  ///
  /// In en, this message translates to:
  /// **'Please enter drop user document'**
  String get pleaseEnterDropUserDoc;

  /// No description provided for @pleaseEnterPickUserName.
  ///
  /// In en, this message translates to:
  /// **'Please enter pick user name'**
  String get pleaseEnterPickUserName;

  /// No description provided for @invalidPickUserName.
  ///
  /// In en, this message translates to:
  /// **'Please enter valid pick user name'**
  String get invalidPickUserName;

  /// No description provided for @pleaseEnterPickUserDocType.
  ///
  /// In en, this message translates to:
  /// **'Please enter pick user document type'**
  String get pleaseEnterPickUserDocType;

  /// No description provided for @signOut.
  ///
  /// In en, this message translates to:
  /// **'Sign Out'**
  String get signOut;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @newUpdateAvailable.
  ///
  /// In en, this message translates to:
  /// **'New Update Available'**
  String get newUpdateAvailable;

  /// No description provided for @updateMessage.
  ///
  /// In en, this message translates to:
  /// **'We\'ve made improvements and fixed bugs to enhance your experience.Please update the app to continue using it smoothly.'**
  String get updateMessage;

  /// No description provided for @versionInfo.
  ///
  /// In en, this message translates to:
  /// **'📱 Current version: {currentAppVersion}\n✅ Required version: {minimumAppVersion}'**
  String versionInfo(String currentAppVersion, String minimumAppVersion);

  /// No description provided for @updateNow.
  ///
  /// In en, this message translates to:
  /// **'Update Now'**
  String get updateNow;

  /// No description provided for @troubleUpdating.
  ///
  /// In en, this message translates to:
  /// **'Having trouble updating?\n Visit the app store manually or contact support'**
  String get troubleUpdating;

  /// No description provided for @signOutContent.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to Sign Out?'**
  String get signOutContent;

  /// No description provided for @deleteAccount.
  ///
  /// In en, this message translates to:
  /// **'Delete Account'**
  String get deleteAccount;

  /// No description provided for @deleteAccountContent.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this account?'**
  String get deleteAccountContent;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @searchOriginLocation.
  ///
  /// In en, this message translates to:
  /// **'Search origin location'**
  String get searchOriginLocation;

  /// No description provided for @searchDropLocation.
  ///
  /// In en, this message translates to:
  /// **'Search drop location'**
  String get searchDropLocation;

  /// No description provided for @enterZipCode.
  ///
  /// In en, this message translates to:
  /// **'Enter ZIP code'**
  String get enterZipCode;

  /// No description provided for @userPickUpLocation.
  ///
  /// In en, this message translates to:
  /// **'User\'s Pick-Up location'**
  String get userPickUpLocation;

  /// No description provided for @userLocation.
  ///
  /// In en, this message translates to:
  /// **'User\'s Location'**
  String get userLocation;

  /// No description provided for @userDeliveryLocation.
  ///
  /// In en, this message translates to:
  /// **'User\'s Delivery location'**
  String get userDeliveryLocation;

  /// No description provided for @toContinueWithTrip.
  ///
  /// In en, this message translates to:
  /// **'To continue with exclusive trip you have to fill the Pick-Up and Delivery Location'**
  String get toContinueWithTrip;

  /// No description provided for @otpSentSuccess.
  ///
  /// In en, this message translates to:
  /// **'OTP sent successfully!'**
  String get otpSentSuccess;

  /// No description provided for @assigned.
  ///
  /// In en, this message translates to:
  /// **'Assigned'**
  String get assigned;

  /// No description provided for @spotAvailableReservation.
  ///
  /// In en, this message translates to:
  /// **'Spot available for reservation'**
  String get spotAvailableReservation;

  /// No description provided for @noOfVehicle.
  ///
  /// In en, this message translates to:
  /// **'No. Of Vehicles'**
  String get noOfVehicle;

  /// No description provided for @vehicleType.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Type'**
  String get vehicleType;

  /// No description provided for @enterVehicleBrand.
  ///
  /// In en, this message translates to:
  /// **'Enter vehicle brand'**
  String get enterVehicleBrand;

  /// No description provided for @enterVehicleYearLabel.
  ///
  /// In en, this message translates to:
  /// **'Enter vehicle year'**
  String get enterVehicleYearLabel;

  /// No description provided for @enterVehicleModelLabel.
  ///
  /// In en, this message translates to:
  /// **'Enter vehicle model'**
  String get enterVehicleModelLabel;

  /// No description provided for @exclusiveTrip.
  ///
  /// In en, this message translates to:
  /// **'Exclusive Trip'**
  String get exclusiveTrip;

  /// No description provided for @didNtFoundCar.
  ///
  /// In en, this message translates to:
  /// **'Didn\'t found car? Send your car info to Verification'**
  String get didNtFoundCar;

  /// No description provided for @aPersonalized.
  ///
  /// In en, this message translates to:
  /// **'A personalized service where your vehicle is picked up from your premises and delivered to your chosen location (if accessible). With no sharing and flexible scheduling, It offers exclusivity at a higher cost.'**
  String get aPersonalized;

  /// No description provided for @pleaseEnterPickUserDoc.
  ///
  /// In en, this message translates to:
  /// **'Please enter pick user document'**
  String get pleaseEnterPickUserDoc;

  /// No description provided for @sendRequest.
  ///
  /// In en, this message translates to:
  /// **'Send Request'**
  String get sendRequest;

  /// No description provided for @sharedTrip.
  ///
  /// In en, this message translates to:
  /// **'Shared Trip'**
  String get sharedTrip;

  /// No description provided for @transportUrVehicle.
  ///
  /// In en, this message translates to:
  /// **'Transport your vehicle with others by dropping it at a designated pick-up point for delivery to a set location. This cost-effective option excludes home pick-up and delivery.'**
  String get transportUrVehicle;

  /// No description provided for @winchRequired.
  ///
  /// In en, this message translates to:
  /// **'Winch required'**
  String get winchRequired;

  /// No description provided for @noTransportersFound.
  ///
  /// In en, this message translates to:
  /// **'No Any Transporters Available'**
  String get noTransportersFound;

  /// No description provided for @noProvider.
  ///
  /// In en, this message translates to:
  /// **'No provider'**
  String get noProvider;

  /// No description provided for @thereIsNoProviderWaitList.
  ///
  /// In en, this message translates to:
  /// **'There is no provider with the requested characteristics, You can join waitlist or continue with exclusive trip'**
  String get thereIsNoProviderWaitList;

  /// No description provided for @thereIsNoProvider.
  ///
  /// In en, this message translates to:
  /// **'There is no provider with the requested characteristics, You can continue with exclusive trip'**
  String get thereIsNoProvider;

  /// No description provided for @enterPersonName.
  ///
  /// In en, this message translates to:
  /// **'Enter person\'s name'**
  String get enterPersonName;

  /// No description provided for @chooseIdType.
  ///
  /// In en, this message translates to:
  /// **'Choose the ID proof type'**
  String get chooseIdType;

  /// No description provided for @idType.
  ///
  /// In en, this message translates to:
  /// **'ID type'**
  String get idType;

  /// No description provided for @idProofPhoto.
  ///
  /// In en, this message translates to:
  /// **'ID Proof photo'**
  String get idProofPhoto;

  /// No description provided for @uploadPicture.
  ///
  /// In en, this message translates to:
  /// **'Upload Pictures of your ID proof'**
  String get uploadPicture;

  /// No description provided for @tapThisAnd.
  ///
  /// In en, this message translates to:
  /// **'Tap this and choose the pictures you want to upload from  your device'**
  String get tapThisAnd;

  /// No description provided for @enterUrAddress.
  ///
  /// In en, this message translates to:
  /// **'Enter your address'**
  String get enterUrAddress;

  /// No description provided for @loading.
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// No description provided for @yourAddress.
  ///
  /// In en, this message translates to:
  /// **'Your address not found'**
  String get yourAddress;

  /// No description provided for @summary.
  ///
  /// In en, this message translates to:
  /// **'Summary'**
  String get summary;

  /// No description provided for @transportationCost.
  ///
  /// In en, this message translates to:
  /// **'Transportation Cost'**
  String get transportationCost;

  /// No description provided for @dropOffStorageFee.
  ///
  /// In en, this message translates to:
  /// **'Drop-off Storage Fee'**
  String get dropOffStorageFee;

  /// No description provided for @pickupStorageFee.
  ///
  /// In en, this message translates to:
  /// **'Pickup Storage Fee'**
  String get pickupStorageFee;

  /// No description provided for @serviceFee.
  ///
  /// In en, this message translates to:
  /// **'Service Fee'**
  String get serviceFee;

  /// No description provided for @dropTransportation.
  ///
  /// In en, this message translates to:
  /// **'Drop-off Transportation Cost'**
  String get dropTransportation;

  /// No description provided for @includeInsurance.
  ///
  /// In en, this message translates to:
  /// **'Include Insurance'**
  String get includeInsurance;

  /// No description provided for @noteEnsure.
  ///
  /// In en, this message translates to:
  /// **'Note: Ensure your vehicle against any damage during transport.'**
  String get noteEnsure;

  /// No description provided for @total.
  ///
  /// In en, this message translates to:
  /// **'Total'**
  String get total;

  /// No description provided for @noteUHaveToPay.
  ///
  /// In en, this message translates to:
  /// **'Note: You have to pay {pay}% amount of total amount right now at a time of booking and rest of the {remain}% amount on the time of delivery.'**
  String noteUHaveToPay(String pay, String remain);

  /// No description provided for @actualTransportationCost.
  ///
  /// In en, this message translates to:
  /// **'Actual Transportation Cost'**
  String get actualTransportationCost;

  /// No description provided for @extraTransportationCost.
  ///
  /// In en, this message translates to:
  /// **'Extra Transportation Cost'**
  String get extraTransportationCost;

  /// No description provided for @insuranceCost.
  ///
  /// In en, this message translates to:
  /// **'Insurance Cost'**
  String get insuranceCost;

  /// No description provided for @totalInsuranceCost.
  ///
  /// In en, this message translates to:
  /// **'Total Insurance Cost'**
  String get totalInsuranceCost;

  /// No description provided for @exclusiveTripExtraCostNote.
  ///
  /// In en, this message translates to:
  /// **'Due to it is exclusive trip you\'ve to pay extra transportation cost'**
  String get exclusiveTripExtraCostNote;

  /// No description provided for @grossAmount.
  ///
  /// In en, this message translates to:
  /// **'Gross Amount'**
  String get grossAmount;

  /// No description provided for @netAmount.
  ///
  /// In en, this message translates to:
  /// **'Net Amount'**
  String get netAmount;

  /// No description provided for @startLocationStorageFee.
  ///
  /// In en, this message translates to:
  /// **'Start Location Storage Fee'**
  String get startLocationStorageFee;

  /// No description provided for @endLocationStorageFee.
  ///
  /// In en, this message translates to:
  /// **'End Location Storage Fee'**
  String get endLocationStorageFee;

  /// No description provided for @customerLocationToStartLocationServiceFee.
  ///
  /// In en, this message translates to:
  /// **'Customer to Start Location Service Fee'**
  String get customerLocationToStartLocationServiceFee;

  /// No description provided for @netTransportationCost.
  ///
  /// In en, this message translates to:
  /// **'Net Transportation Cost'**
  String get netTransportationCost;

  /// No description provided for @netStartLocationStorageFee.
  ///
  /// In en, this message translates to:
  /// **'Net Start Location Storage Fee'**
  String get netStartLocationStorageFee;

  /// No description provided for @netEndLocationStorageFee.
  ///
  /// In en, this message translates to:
  /// **'Net End Location Storage Fee'**
  String get netEndLocationStorageFee;

  /// No description provided for @netCustomerLocationToStartLocationServiceFee.
  ///
  /// In en, this message translates to:
  /// **'Net Customer to Start Location Service Fee'**
  String get netCustomerLocationToStartLocationServiceFee;

  /// No description provided for @netInsuranceCost.
  ///
  /// In en, this message translates to:
  /// **'Net Insurance Cost'**
  String get netInsuranceCost;

  /// No description provided for @netTripCharge.
  ///
  /// In en, this message translates to:
  /// **'Net Trip Charge'**
  String get netTripCharge;

  /// No description provided for @totalTripCost.
  ///
  /// In en, this message translates to:
  /// **'Total Trip Cost'**
  String get totalTripCost;

  /// No description provided for @netTotalAppFee.
  ///
  /// In en, this message translates to:
  /// **'Net Total App Fee'**
  String get netTotalAppFee;

  /// No description provided for @totalNetTransportationCharge.
  ///
  /// In en, this message translates to:
  /// **'Total Net Transportation Charge'**
  String get totalNetTransportationCharge;

  /// No description provided for @totalNetInsuranceCharge.
  ///
  /// In en, this message translates to:
  /// **'Total Net Insurance Charge'**
  String get totalNetInsuranceCharge;

  /// No description provided for @totalNetBookingPay.
  ///
  /// In en, this message translates to:
  /// **'Total Net Booking Pay'**
  String get totalNetBookingPay;

  /// No description provided for @transportationCostExplanation.
  ///
  /// In en, this message translates to:
  /// **'Cost for transporting your vehicle from pickup to delivery location'**
  String get transportationCostExplanation;

  /// No description provided for @storageCostExplanation.
  ///
  /// In en, this message translates to:
  /// **'Storage fee for keeping your vehicle at our facility'**
  String get storageCostExplanation;

  /// No description provided for @insuranceCostExplanation.
  ///
  /// In en, this message translates to:
  /// **'Optional insurance coverage to protect your vehicle during transport'**
  String get insuranceCostExplanation;

  /// No description provided for @platformFeeExplanation.
  ///
  /// In en, this message translates to:
  /// **'Service fee for using our platform and booking management'**
  String get platformFeeExplanation;

  /// No description provided for @dropOffServiceExplanation.
  ///
  /// In en, this message translates to:
  /// **'Additional service for picking up your vehicle from your location'**
  String get dropOffServiceExplanation;

  /// No description provided for @daysSelected.
  ///
  /// In en, this message translates to:
  /// **'Days selected'**
  String get daysSelected;

  /// No description provided for @perDayRate.
  ///
  /// In en, this message translates to:
  /// **'Per day rate'**
  String get perDayRate;

  /// No description provided for @totalDays.
  ///
  /// In en, this message translates to:
  /// **'Total days'**
  String get totalDays;

  /// No description provided for @baseRate.
  ///
  /// In en, this message translates to:
  /// **'Base rate'**
  String get baseRate;

  /// No description provided for @discountApplied.
  ///
  /// In en, this message translates to:
  /// **'Discount applied'**
  String get discountApplied;

  /// No description provided for @taxesAndFees.
  ///
  /// In en, this message translates to:
  /// **'Taxes and fees'**
  String get taxesAndFees;

  /// No description provided for @youPayNow.
  ///
  /// In en, this message translates to:
  /// **'You pay now to confirm booking'**
  String get youPayNow;

  /// No description provided for @remainingOnDelivery.
  ///
  /// In en, this message translates to:
  /// **'Remaining amount due on delivery'**
  String get remainingOnDelivery;

  /// No description provided for @costBreakdownFor.
  ///
  /// In en, this message translates to:
  /// **'Cost breakdown for {companyName}'**
  String costBreakdownFor(String companyName);

  /// No description provided for @finalAmountToPay.
  ///
  /// In en, this message translates to:
  /// **'Final amount to pay now'**
  String get finalAmountToPay;

  /// No description provided for @viewCarCharges.
  ///
  /// In en, this message translates to:
  /// **'View Car Charges'**
  String get viewCarCharges;

  /// No description provided for @carChargesFor.
  ///
  /// In en, this message translates to:
  /// **'Car Charges for {serialNumber}'**
  String carChargesFor(String serialNumber);

  /// No description provided for @totalCost.
  ///
  /// In en, this message translates to:
  /// **'Total Cost'**
  String get totalCost;

  /// No description provided for @taxAmount.
  ///
  /// In en, this message translates to:
  /// **'Tax Amount'**
  String get taxAmount;

  /// No description provided for @taxRate.
  ///
  /// In en, this message translates to:
  /// **'Tax Rate'**
  String get taxRate;

  /// No description provided for @totalWithTax.
  ///
  /// In en, this message translates to:
  /// **'Total with Tax'**
  String get totalWithTax;

  /// No description provided for @netTotalWithTax.
  ///
  /// In en, this message translates to:
  /// **'Net Total with Tax'**
  String get netTotalWithTax;

  /// No description provided for @providerBreakdown.
  ///
  /// In en, this message translates to:
  /// **'Provider Breakdown'**
  String get providerBreakdown;

  /// No description provided for @overallSummary.
  ///
  /// In en, this message translates to:
  /// **'Overall Summary'**
  String get overallSummary;

  /// No description provided for @costBreakdown.
  ///
  /// In en, this message translates to:
  /// **'Cost Breakdown'**
  String get costBreakdown;

  /// No description provided for @transportationCostDescription.
  ///
  /// In en, this message translates to:
  /// **'Cost for transporting vehicles from pickup to delivery'**
  String get transportationCostDescription;

  /// No description provided for @startLocationStorage.
  ///
  /// In en, this message translates to:
  /// **'Start Location Storage'**
  String get startLocationStorage;

  /// No description provided for @startLocationStorageDescription.
  ///
  /// In en, this message translates to:
  /// **'Storage fee at pickup location'**
  String get startLocationStorageDescription;

  /// No description provided for @endLocationStorage.
  ///
  /// In en, this message translates to:
  /// **'End Location Storage'**
  String get endLocationStorage;

  /// No description provided for @endLocationStorageDescription.
  ///
  /// In en, this message translates to:
  /// **'Storage fee at delivery location'**
  String get endLocationStorageDescription;

  /// No description provided for @customerLocationService.
  ///
  /// In en, this message translates to:
  /// **'Customer Location Service'**
  String get customerLocationService;

  /// No description provided for @customerLocationServiceDescription.
  ///
  /// In en, this message translates to:
  /// **'Service for picking up from your location'**
  String get customerLocationServiceDescription;

  /// No description provided for @insuranceCoverage.
  ///
  /// In en, this message translates to:
  /// **'Insurance Coverage'**
  String get insuranceCoverage;

  /// No description provided for @insuranceCoverageDescription.
  ///
  /// In en, this message translates to:
  /// **'Protection for your vehicles during transport'**
  String get insuranceCoverageDescription;

  /// No description provided for @vehicleWiseCharges.
  ///
  /// In en, this message translates to:
  /// **'Vehicle-wise Charges ({count} vehicles)'**
  String vehicleWiseCharges(int count);

  /// No description provided for @vehicle.
  ///
  /// In en, this message translates to:
  /// **'Vehicle'**
  String get vehicle;

  /// No description provided for @transportation.
  ///
  /// In en, this message translates to:
  /// **'Transportation'**
  String get transportation;

  /// No description provided for @startStorage.
  ///
  /// In en, this message translates to:
  /// **'Start Storage'**
  String get startStorage;

  /// No description provided for @endStorage.
  ///
  /// In en, this message translates to:
  /// **'End Storage'**
  String get endStorage;

  /// No description provided for @customerService.
  ///
  /// In en, this message translates to:
  /// **'Customer Service'**
  String get customerService;

  /// No description provided for @insurance.
  ///
  /// In en, this message translates to:
  /// **'Insurance'**
  String get insurance;

  /// No description provided for @netYouPay.
  ///
  /// In en, this message translates to:
  /// **'Net (You Pay)'**
  String get netYouPay;

  /// No description provided for @finalAmountToPayTitle.
  ///
  /// In en, this message translates to:
  /// **'Final Amount to Pay'**
  String get finalAmountToPayTitle;

  /// No description provided for @payNowToConfirm.
  ///
  /// In en, this message translates to:
  /// **'Pay now to confirm your booking'**
  String get payNowToConfirm;

  /// No description provided for @tax.
  ///
  /// In en, this message translates to:
  /// **'Tax'**
  String get tax;

  /// No description provided for @totalAmountToPay.
  ///
  /// In en, this message translates to:
  /// **'Total Amount to Pay'**
  String get totalAmountToPay;

  /// No description provided for @providerBreakdownTitle.
  ///
  /// In en, this message translates to:
  /// **'Provider Breakdown'**
  String get providerBreakdownTitle;

  /// No description provided for @tripId.
  ///
  /// In en, this message translates to:
  /// **'Trip ID'**
  String get tripId;

  /// No description provided for @failedToFetchSuggestion.
  ///
  /// In en, this message translates to:
  /// **'Failed to fetch suggestion'**
  String get failedToFetchSuggestion;

  /// No description provided for @shipmentConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Shipment Confirmation'**
  String get shipmentConfirmation;

  /// No description provided for @yourShipment.
  ///
  /// In en, this message translates to:
  /// **'Your Shipment'**
  String get yourShipment;

  /// No description provided for @noOfTotalVehicle.
  ///
  /// In en, this message translates to:
  /// **'No. Of Total Vehicles'**
  String get noOfTotalVehicle;

  /// No description provided for @proceedToPayment.
  ///
  /// In en, this message translates to:
  /// **'Proceed to Payments'**
  String get proceedToPayment;

  /// No description provided for @pickupFrom.
  ///
  /// In en, this message translates to:
  /// **'Pickup from'**
  String get pickupFrom;

  /// No description provided for @dropAt.
  ///
  /// In en, this message translates to:
  /// **'Drop at'**
  String get dropAt;

  /// No description provided for @viewDetails.
  ///
  /// In en, this message translates to:
  /// **'View Details'**
  String get viewDetails;

  /// No description provided for @pickupLocation.
  ///
  /// In en, this message translates to:
  /// **'Pickup Location'**
  String get pickupLocation;

  /// No description provided for @deliveryLocation.
  ///
  /// In en, this message translates to:
  /// **'Delivery Location'**
  String get deliveryLocation;

  /// No description provided for @notePleaseDrop.
  ///
  /// In en, this message translates to:
  /// **'Note: Please drop off your vehicle one day before the scheduled date and pick it up one day after delivery to allow time for a condition checklist.'**
  String get notePleaseDrop;

  /// No description provided for @pleaseSelectDropDate.
  ///
  /// In en, this message translates to:
  /// **'Please select drop date for your vehicle at stock location'**
  String get pleaseSelectDropDate;

  /// No description provided for @pleaseSelectDropDateForAllVehicles.
  ///
  /// In en, this message translates to:
  /// **'Please select drop date for your vehicle at stock location for all vehicles'**
  String get pleaseSelectDropDateForAllVehicles;

  /// No description provided for @carBrand.
  ///
  /// In en, this message translates to:
  /// **'Car brand'**
  String get carBrand;

  /// No description provided for @carModel.
  ///
  /// In en, this message translates to:
  /// **'Car model'**
  String get carModel;

  /// No description provided for @carSerial.
  ///
  /// In en, this message translates to:
  /// **'Car serial #'**
  String get carSerial;

  /// No description provided for @carYear.
  ///
  /// In en, this message translates to:
  /// **'Car year'**
  String get carYear;

  /// No description provided for @carSize.
  ///
  /// In en, this message translates to:
  /// **'Car size'**
  String get carSize;

  /// No description provided for @dropOffDate.
  ///
  /// In en, this message translates to:
  /// **'Drop-off Date'**
  String get dropOffDate;

  /// No description provided for @selectVehicleDrop.
  ///
  /// In en, this message translates to:
  /// **'Select vehicle drop off date at stock location'**
  String get selectVehicleDrop;

  /// No description provided for @selectDateForPickupCar.
  ///
  /// In en, this message translates to:
  /// **'Choose the date your vehicle is ready for pickup'**
  String get selectDateForPickupCar;

  /// No description provided for @storageFee.
  ///
  /// In en, this message translates to:
  /// **'Storage Fee'**
  String get storageFee;

  /// No description provided for @perDay.
  ///
  /// In en, this message translates to:
  /// **'per day'**
  String get perDay;

  /// No description provided for @close.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// No description provided for @continues.
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continues;

  /// No description provided for @transportList.
  ///
  /// In en, this message translates to:
  /// **'Transporters List'**
  String get transportList;

  /// No description provided for @edit.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// No description provided for @chooseTransporter.
  ///
  /// In en, this message translates to:
  /// **'Choose Transporter'**
  String get chooseTransporter;

  /// No description provided for @filterBy.
  ///
  /// In en, this message translates to:
  /// **'Filter By'**
  String get filterBy;

  /// No description provided for @noProviderFound.
  ///
  /// In en, this message translates to:
  /// **'No any provider available'**
  String get noProviderFound;

  /// No description provided for @userInfo.
  ///
  /// In en, this message translates to:
  /// **'User Info'**
  String get userInfo;

  /// No description provided for @carDropPerson.
  ///
  /// In en, this message translates to:
  /// **'Car Drop person info'**
  String get carDropPerson;

  /// No description provided for @thisIsDropInfo.
  ///
  /// In en, this message translates to:
  /// **'This is the info of the person who is going to drop the car'**
  String get thisIsDropInfo;

  /// No description provided for @carPickupPerson.
  ///
  /// In en, this message translates to:
  /// **'Car Pickup person info'**
  String get carPickupPerson;

  /// No description provided for @thisIsDeliveryInfo.
  ///
  /// In en, this message translates to:
  /// **'This is the info of the person who is going to take delivery'**
  String get thisIsDeliveryInfo;

  /// No description provided for @uAlreadyAssign.
  ///
  /// In en, this message translates to:
  /// **'You already assign this vehicle, Please select another one'**
  String get uAlreadyAssign;

  /// No description provided for @noSlotAvailable.
  ///
  /// In en, this message translates to:
  /// **'No slot available for your next car'**
  String get noSlotAvailable;

  /// No description provided for @pleaseAssignCar.
  ///
  /// In en, this message translates to:
  /// **'Please assign car for slot'**
  String get pleaseAssignCar;

  /// No description provided for @pleaseChooseCar.
  ///
  /// In en, this message translates to:
  /// **'Please choose a car for a slot'**
  String get pleaseChooseCar;

  /// No description provided for @clearAll.
  ///
  /// In en, this message translates to:
  /// **'Clear All'**
  String get clearAll;

  /// No description provided for @teamCapacity.
  ///
  /// In en, this message translates to:
  /// **'Team Capacity'**
  String get teamCapacity;

  /// No description provided for @rating.
  ///
  /// In en, this message translates to:
  /// **'Rating'**
  String get rating;

  /// No description provided for @rated.
  ///
  /// In en, this message translates to:
  /// **'Rated'**
  String get rated;

  /// No description provided for @pricing.
  ///
  /// In en, this message translates to:
  /// **'Pricing'**
  String get pricing;

  /// No description provided for @lowestPerKM.
  ///
  /// In en, this message translates to:
  /// **'Lowest Per K.M'**
  String get lowestPerKM;

  /// No description provided for @in2Days.
  ///
  /// In en, this message translates to:
  /// **'In 2 Days'**
  String get in2Days;

  /// No description provided for @rateProvider.
  ///
  /// In en, this message translates to:
  /// **'Rate Provider'**
  String get rateProvider;

  /// No description provided for @payRemainAmount.
  ///
  /// In en, this message translates to:
  /// **'Pay Remain Amount'**
  String get payRemainAmount;

  /// No description provided for @paySettlementAmount.
  ///
  /// In en, this message translates to:
  /// **'Pay Settlement Amount'**
  String get paySettlementAmount;

  /// No description provided for @paymentSettlement.
  ///
  /// In en, this message translates to:
  /// **'Payment Settlement'**
  String get paymentSettlement;

  /// No description provided for @remainPayments.
  ///
  /// In en, this message translates to:
  /// **'Remaining Payments'**
  String get remainPayments;

  /// No description provided for @cancelTrip.
  ///
  /// In en, this message translates to:
  /// **'Cancel Trip'**
  String get cancelTrip;

  /// No description provided for @inTrackTransport.
  ///
  /// In en, this message translates to:
  /// **'InTrack Transport'**
  String get inTrackTransport;

  /// No description provided for @addNotes.
  ///
  /// In en, this message translates to:
  /// **'Add Notes'**
  String get addNotes;

  /// No description provided for @notes.
  ///
  /// In en, this message translates to:
  /// **'Notes'**
  String get notes;

  /// No description provided for @pleaseAddNotes.
  ///
  /// In en, this message translates to:
  /// **'Please add notes'**
  String get pleaseAddNotes;

  /// No description provided for @reportsFromTransporter.
  ///
  /// In en, this message translates to:
  /// **'Reports from transporter'**
  String get reportsFromTransporter;

  /// No description provided for @writeNotes.
  ///
  /// In en, this message translates to:
  /// **'Write Notes'**
  String get writeNotes;

  /// No description provided for @writeUrMessage.
  ///
  /// In en, this message translates to:
  /// **'Write your message here'**
  String get writeUrMessage;

  /// No description provided for @upcoming.
  ///
  /// In en, this message translates to:
  /// **'Upcoming'**
  String get upcoming;

  /// No description provided for @ongoing.
  ///
  /// In en, this message translates to:
  /// **'Ongoing'**
  String get ongoing;

  /// No description provided for @completed.
  ///
  /// In en, this message translates to:
  /// **'Completed'**
  String get completed;

  /// No description provided for @chatWithProvider.
  ///
  /// In en, this message translates to:
  /// **'Chat with provider'**
  String get chatWithProvider;

  /// No description provided for @checklist.
  ///
  /// In en, this message translates to:
  /// **'Checklist'**
  String get checklist;

  /// No description provided for @checklists.
  ///
  /// In en, this message translates to:
  /// **'Checklists'**
  String get checklists;

  /// No description provided for @mileageAtPickup.
  ///
  /// In en, this message translates to:
  /// **'Mileage at Pickup'**
  String get mileageAtPickup;

  /// No description provided for @mileageAtDelivery.
  ///
  /// In en, this message translates to:
  /// **'Mileage at Delivery'**
  String get mileageAtDelivery;

  /// No description provided for @pickupDateNPlace.
  ///
  /// In en, this message translates to:
  /// **'Pickup Date & Place'**
  String get pickupDateNPlace;

  /// No description provided for @deliveryDateNPlace.
  ///
  /// In en, this message translates to:
  /// **'Delivery Date & Place'**
  String get deliveryDateNPlace;

  /// No description provided for @pickupOfficer.
  ///
  /// In en, this message translates to:
  /// **'Pickup Officer'**
  String get pickupOfficer;

  /// No description provided for @deliveryOfficer.
  ///
  /// In en, this message translates to:
  /// **'Delivery Officer'**
  String get deliveryOfficer;

  /// No description provided for @yes.
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No description provided for @no.
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// No description provided for @uploadPictureOfId.
  ///
  /// In en, this message translates to:
  /// **'Upload Pictures of your ID proof'**
  String get uploadPictureOfId;

  /// No description provided for @tapThisNChoose.
  ///
  /// In en, this message translates to:
  /// **'Tap this and choose the pictures you want to upload from  your device'**
  String get tapThisNChoose;

  /// No description provided for @editDetails.
  ///
  /// In en, this message translates to:
  /// **'Edit Details'**
  String get editDetails;

  /// No description provided for @originLocation.
  ///
  /// In en, this message translates to:
  /// **'Origin Location'**
  String get originLocation;

  /// No description provided for @dropLocation.
  ///
  /// In en, this message translates to:
  /// **'Drop Location'**
  String get dropLocation;

  /// No description provided for @addMoreCars.
  ///
  /// In en, this message translates to:
  /// **'Add more cars'**
  String get addMoreCars;

  /// No description provided for @paymentSummary.
  ///
  /// In en, this message translates to:
  /// **'Payment Summary'**
  String get paymentSummary;

  /// No description provided for @paidAmount.
  ///
  /// In en, this message translates to:
  /// **'Paid Amount'**
  String get paidAmount;

  /// No description provided for @remainAmount.
  ///
  /// In en, this message translates to:
  /// **'Remain Amount'**
  String get remainAmount;

  /// No description provided for @remainAmountTax.
  ///
  /// In en, this message translates to:
  /// **'Remain Amount Tax'**
  String get remainAmountTax;

  /// No description provided for @cancelBooking.
  ///
  /// In en, this message translates to:
  /// **'Cancel Booking'**
  String get cancelBooking;

  /// No description provided for @chooseWhatUFeel.
  ///
  /// In en, this message translates to:
  /// **'Choose what you feel'**
  String get chooseWhatUFeel;

  /// No description provided for @writeSuggestion.
  ///
  /// In en, this message translates to:
  /// **'Write Suggestion'**
  String get writeSuggestion;

  /// No description provided for @writeUrSuggestion.
  ///
  /// In en, this message translates to:
  /// **'Write your suggestion if you have'**
  String get writeUrSuggestion;

  /// No description provided for @submitReview.
  ///
  /// In en, this message translates to:
  /// **'Submit Review'**
  String get submitReview;

  /// No description provided for @viewCheckList.
  ///
  /// In en, this message translates to:
  /// **'View Check List'**
  String get viewCheckList;

  /// No description provided for @removeCar.
  ///
  /// In en, this message translates to:
  /// **'Remove Car'**
  String get removeCar;

  /// No description provided for @areUSureRemoveCar.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to remove this car?'**
  String get areUSureRemoveCar;

  /// No description provided for @noVehicleAvailable.
  ///
  /// In en, this message translates to:
  /// **'No vehicle available'**
  String get noVehicleAvailable;

  /// No description provided for @pleaseEnterPickLocation.
  ///
  /// In en, this message translates to:
  /// **'Please enter your pickup location'**
  String get pleaseEnterPickLocation;

  /// No description provided for @pleaseEnterDeliveryLocation.
  ///
  /// In en, this message translates to:
  /// **'Please enter your delivery location'**
  String get pleaseEnterDeliveryLocation;

  /// No description provided for @submitForApproval.
  ///
  /// In en, this message translates to:
  /// **'Submit Detail for Approval'**
  String get submitForApproval;

  /// No description provided for @assignedCars.
  ///
  /// In en, this message translates to:
  /// **'Assigned Cars'**
  String get assignedCars;

  /// No description provided for @howManyCar.
  ///
  /// In en, this message translates to:
  /// **'How many cars you want to ship with this provider'**
  String get howManyCar;

  /// No description provided for @chooseVehicle.
  ///
  /// In en, this message translates to:
  /// **'Choose vehicle'**
  String get chooseVehicle;

  /// No description provided for @assignMoreCar.
  ///
  /// In en, this message translates to:
  /// **'Assign More Cars'**
  String get assignMoreCar;

  /// No description provided for @user.
  ///
  /// In en, this message translates to:
  /// **'User'**
  String get user;

  /// No description provided for @userEmail.
  ///
  /// In en, this message translates to:
  /// **'User Email'**
  String get userEmail;

  /// No description provided for @creditDebitCards.
  ///
  /// In en, this message translates to:
  /// **'Credit & Debit Cards'**
  String get creditDebitCards;

  /// No description provided for @bank.
  ///
  /// In en, this message translates to:
  /// **'Bank'**
  String get bank;

  /// No description provided for @addAnotherMethod.
  ///
  /// In en, this message translates to:
  /// **'Add Another Another Method'**
  String get addAnotherMethod;

  /// No description provided for @congratulation.
  ///
  /// In en, this message translates to:
  /// **'Congratulations'**
  String get congratulation;

  /// No description provided for @youHaveGotTransportation.
  ///
  /// In en, this message translates to:
  /// **'You have got the transportation spot. you can process further by making Payments'**
  String get youHaveGotTransportation;

  /// No description provided for @changeBid.
  ///
  /// In en, this message translates to:
  /// **'Change Bid'**
  String get changeBid;

  /// No description provided for @yourCurrentBid.
  ///
  /// In en, this message translates to:
  /// **'Your Current Bid'**
  String get yourCurrentBid;

  /// No description provided for @minimumBidRequired.
  ///
  /// In en, this message translates to:
  /// **'Minimum bid required for your car to be transported'**
  String get minimumBidRequired;

  /// No description provided for @minimumRequirementFor.
  ///
  /// In en, this message translates to:
  /// **'Minimum requirement for your car to be transported.'**
  String get minimumRequirementFor;

  /// No description provided for @chooseYourBidIncrement.
  ///
  /// In en, this message translates to:
  /// **'Choose your bid increment'**
  String get chooseYourBidIncrement;

  /// No description provided for @yourNeWBidWillBe.
  ///
  /// In en, this message translates to:
  /// **'Your New Bid will be'**
  String get yourNeWBidWillBe;

  /// No description provided for @note.
  ///
  /// In en, this message translates to:
  /// **'Note'**
  String get note;

  /// No description provided for @requestedTrips.
  ///
  /// In en, this message translates to:
  /// **'Requested Trips'**
  String get requestedTrips;

  /// No description provided for @acceptedTrips.
  ///
  /// In en, this message translates to:
  /// **'Accepted Trips'**
  String get acceptedTrips;

  /// No description provided for @yourPositionInList.
  ///
  /// In en, this message translates to:
  /// **'Your position in List'**
  String get yourPositionInList;

  /// No description provided for @enterAuction.
  ///
  /// In en, this message translates to:
  /// **'Enter Auction'**
  String get enterAuction;

  /// No description provided for @reject.
  ///
  /// In en, this message translates to:
  /// **'Reject'**
  String get reject;

  /// No description provided for @dueToMoreUser.
  ///
  /// In en, this message translates to:
  /// **'Due to more users than available spots, Priority for reserving spots will be given to those who offer a higher Payments for the spots'**
  String get dueToMoreUser;

  /// No description provided for @wait.
  ///
  /// In en, this message translates to:
  /// **'Wait'**
  String get wait;

  /// No description provided for @joinWaitlist.
  ///
  /// In en, this message translates to:
  /// **'Join Waitlist'**
  String get joinWaitlist;

  /// No description provided for @areUSure.
  ///
  /// In en, this message translates to:
  /// **'Are you sure?'**
  String get areUSure;

  /// No description provided for @uAreRejecting.
  ///
  /// In en, this message translates to:
  /// **'You are rejecting this offer do you want to wait for another offer or want to go to waitlist'**
  String get uAreRejecting;

  /// No description provided for @resumeTrip.
  ///
  /// In en, this message translates to:
  /// **'Resume Trip'**
  String get resumeTrip;

  /// No description provided for @editTrip.
  ///
  /// In en, this message translates to:
  /// **'Edit Trip'**
  String get editTrip;

  /// No description provided for @noTripFound.
  ///
  /// In en, this message translates to:
  /// **'No any trip available'**
  String get noTripFound;

  /// No description provided for @auction.
  ///
  /// In en, this message translates to:
  /// **'Auction'**
  String get auction;

  /// No description provided for @closingIn.
  ///
  /// In en, this message translates to:
  /// **'Closing In'**
  String get closingIn;

  /// No description provided for @opnTrackTransportation.
  ///
  /// In en, this message translates to:
  /// **'OnTrack Transportation'**
  String get opnTrackTransportation;

  /// No description provided for @availableSlot.
  ///
  /// In en, this message translates to:
  /// **'Available Slots'**
  String get availableSlot;

  /// No description provided for @noOfAvailableSlot.
  ///
  /// In en, this message translates to:
  /// **'No.Of Available Slots'**
  String get noOfAvailableSlot;

  /// No description provided for @carsWanted.
  ///
  /// In en, this message translates to:
  /// **'Cars wanted to Move'**
  String get carsWanted;

  /// No description provided for @yourCurrentSpot.
  ///
  /// In en, this message translates to:
  /// **'Your Current Spot'**
  String get yourCurrentSpot;

  /// No description provided for @listOfCurrentBid.
  ///
  /// In en, this message translates to:
  /// **'List of Current bids'**
  String get listOfCurrentBid;

  /// No description provided for @theUsersListed.
  ///
  /// In en, this message translates to:
  /// **'The users listed inside the box are the ones that will be transported.'**
  String get theUsersListed;

  /// No description provided for @exitAuction.
  ///
  /// In en, this message translates to:
  /// **'Exit Auction'**
  String get exitAuction;

  /// No description provided for @startBiding.
  ///
  /// In en, this message translates to:
  /// **'Start Biding'**
  String get startBiding;

  /// No description provided for @uHaveNotBid.
  ///
  /// In en, this message translates to:
  /// **'You haven\'t bid yet'**
  String get uHaveNotBid;

  /// No description provided for @accept.
  ///
  /// In en, this message translates to:
  /// **'Accept'**
  String get accept;

  /// No description provided for @origin.
  ///
  /// In en, this message translates to:
  /// **'Origin'**
  String get origin;

  /// No description provided for @destination.
  ///
  /// In en, this message translates to:
  /// **'Destination'**
  String get destination;

  /// No description provided for @small.
  ///
  /// In en, this message translates to:
  /// **'Small'**
  String get small;

  /// No description provided for @medium.
  ///
  /// In en, this message translates to:
  /// **'Medium'**
  String get medium;

  /// No description provided for @large.
  ///
  /// In en, this message translates to:
  /// **'Large'**
  String get large;

  /// No description provided for @enterVehicleVersion.
  ///
  /// In en, this message translates to:
  /// **'Enter Vehicle Version'**
  String get enterVehicleVersion;

  /// No description provided for @enterVehicleCondition.
  ///
  /// In en, this message translates to:
  /// **'Enter Vehicle Condition'**
  String get enterVehicleCondition;

  /// No description provided for @pauseTrip.
  ///
  /// In en, this message translates to:
  /// **'Pause Trip'**
  String get pauseTrip;

  /// No description provided for @yesCancel.
  ///
  /// In en, this message translates to:
  /// **'Yes, Cancel'**
  String get yesCancel;

  /// No description provided for @restTrip.
  ///
  /// In en, this message translates to:
  /// **'Rest Trip'**
  String get restTrip;

  /// No description provided for @areUSureCancel.
  ///
  /// In en, this message translates to:
  /// **'Are you sure want to cancel this trip?'**
  String get areUSureCancel;

  /// No description provided for @areUSureCancelBooking.
  ///
  /// In en, this message translates to:
  /// **'Are you sure want to cancel this entire booking?'**
  String get areUSureCancelBooking;

  /// No description provided for @carCancelledSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Car cancelled successfully'**
  String get carCancelledSuccessfully;

  /// No description provided for @addedVehiclesInfo.
  ///
  /// In en, this message translates to:
  /// **'Approved Vehicles Info'**
  String get addedVehiclesInfo;

  /// No description provided for @rejectedVehiclesInfo.
  ///
  /// In en, this message translates to:
  /// **'Rejected Vehicles Info'**
  String get rejectedVehiclesInfo;

  /// No description provided for @pendingVehiclesInfo.
  ///
  /// In en, this message translates to:
  /// **'Pending Vehicles Info'**
  String get pendingVehiclesInfo;

  /// No description provided for @allNotifications.
  ///
  /// In en, this message translates to:
  /// **'All Notifications'**
  String get allNotifications;

  /// No description provided for @noNotificationsFound.
  ///
  /// In en, this message translates to:
  /// **'No notification found'**
  String get noNotificationsFound;

  /// No description provided for @sooner.
  ///
  /// In en, this message translates to:
  /// **'Sooner'**
  String get sooner;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @selectLanguage.
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectLanguage;

  /// No description provided for @locationServicesDisabled.
  ///
  /// In en, this message translates to:
  /// **'Location services are disabled. Please enable them to use this feature.'**
  String get locationServicesDisabled;

  /// No description provided for @locationPermissionsDenied.
  ///
  /// In en, this message translates to:
  /// **'Location permissions are denied. Please enable them to use this feature.'**
  String get locationPermissionsDenied;

  /// No description provided for @locationPermissionsDeniedForever.
  ///
  /// In en, this message translates to:
  /// **'Location permissions are permanently denied. Please enable them in settings.'**
  String get locationPermissionsDeniedForever;

  /// No description provided for @failedToGetLocation.
  ///
  /// In en, this message translates to:
  /// **'Failed to get current location. Please try again.'**
  String get failedToGetLocation;

  /// No description provided for @searchAddress.
  ///
  /// In en, this message translates to:
  /// **'Search Address'**
  String get searchAddress;

  /// No description provided for @search.
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// No description provided for @selectAnotherAddress.
  ///
  /// In en, this message translates to:
  /// **'Something went wrong, Please select another address'**
  String get selectAnotherAddress;

  /// No description provided for @somethingWentWrong.
  ///
  /// In en, this message translates to:
  /// **'Something went wrong, Please try again'**
  String get somethingWentWrong;

  /// No description provided for @selectLocation.
  ///
  /// In en, this message translates to:
  /// **'Select Location'**
  String get selectLocation;

  /// No description provided for @selectLocationOnMap.
  ///
  /// In en, this message translates to:
  /// **'Please select a location on the map'**
  String get selectLocationOnMap;

  /// No description provided for @failedToGetLocationDetails.
  ///
  /// In en, this message translates to:
  /// **'Failed to get location details'**
  String get failedToGetLocationDetails;

  /// No description provided for @tripDetails.
  ///
  /// In en, this message translates to:
  /// **'Trip Details'**
  String get tripDetails;

  /// No description provided for @dropAndPickupPerson.
  ///
  /// In en, this message translates to:
  /// **'Drop and Pickup person'**
  String get dropAndPickupPerson;

  /// No description provided for @addDropAndPickupPerson.
  ///
  /// In en, this message translates to:
  /// **'+ Add drop and pickup person'**
  String get addDropAndPickupPerson;

  /// No description provided for @day.
  ///
  /// In en, this message translates to:
  /// **'day'**
  String get day;

  /// No description provided for @clientName.
  ///
  /// In en, this message translates to:
  /// **'Client Name'**
  String get clientName;

  /// No description provided for @checklistType.
  ///
  /// In en, this message translates to:
  /// **'Checklist Type'**
  String get checklistType;

  /// No description provided for @pickup.
  ///
  /// In en, this message translates to:
  /// **'Pickup'**
  String get pickup;

  /// No description provided for @delivery.
  ///
  /// In en, this message translates to:
  /// **'Delivery'**
  String get delivery;

  /// No description provided for @fuelLevel.
  ///
  /// In en, this message translates to:
  /// **'Fuel level'**
  String get fuelLevel;

  /// No description provided for @mileageAt.
  ///
  /// In en, this message translates to:
  /// **'Mileage at {type} (In miles/hr)'**
  String mileageAt(String type);

  /// No description provided for @dateLabel.
  ///
  /// In en, this message translates to:
  /// **'{type} Date'**
  String dateLabel(String type);

  /// No description provided for @carSizeLabel.
  ///
  /// In en, this message translates to:
  /// **'Car size:'**
  String get carSizeLabel;

  /// No description provided for @carSizeSmall.
  ///
  /// In en, this message translates to:
  /// **'Small'**
  String get carSizeSmall;

  /// No description provided for @carSizeMedium.
  ///
  /// In en, this message translates to:
  /// **'Medium'**
  String get carSizeMedium;

  /// No description provided for @carSizeLarge.
  ///
  /// In en, this message translates to:
  /// **'Large'**
  String get carSizeLarge;

  /// No description provided for @insuranceProvider.
  ///
  /// In en, this message translates to:
  /// **'Insurance provider'**
  String get insuranceProvider;

  /// No description provided for @asWeHaveTwoDifferentPricingWeWillTakeTheLargestSumAmount.
  ///
  /// In en, this message translates to:
  /// **'As we have two different pricing we will take the largest sum amount as towing cost.'**
  String get asWeHaveTwoDifferentPricingWeWillTakeTheLargestSumAmount;

  /// No description provided for @youHaveOptForWinchServiceSoYouCanNotDisableThisOption.
  ///
  /// In en, this message translates to:
  /// **'You have opt for winch service so you can not disable this option.'**
  String get youHaveOptForWinchServiceSoYouCanNotDisableThisOption;

  /// No description provided for @type.
  ///
  /// In en, this message translates to:
  /// **'Type'**
  String get type;

  /// No description provided for @performedDuring.
  ///
  /// In en, this message translates to:
  /// **'Performed During'**
  String get performedDuring;

  /// No description provided for @report.
  ///
  /// In en, this message translates to:
  /// **'Report'**
  String get report;

  /// No description provided for @viewLess.
  ///
  /// In en, this message translates to:
  /// **'View less'**
  String get viewLess;

  /// No description provided for @viewMore.
  ///
  /// In en, this message translates to:
  /// **'View more'**
  String get viewMore;

  /// No description provided for @waitingList.
  ///
  /// In en, this message translates to:
  /// **'Waiting List'**
  String get waitingList;

  /// No description provided for @restedTrip.
  ///
  /// In en, this message translates to:
  /// **'Rested Trip'**
  String get restedTrip;

  /// No description provided for @resetPassword.
  ///
  /// In en, this message translates to:
  /// **'Reset password'**
  String get resetPassword;

  /// No description provided for @providerInfo.
  ///
  /// In en, this message translates to:
  /// **'Provider Info'**
  String get providerInfo;

  /// No description provided for @noTransporterFoundYet.
  ///
  /// In en, this message translates to:
  /// **'No transporter found yet'**
  String get noTransporterFoundYet;

  /// No description provided for @pleaseEnterValidPickupAddress.
  ///
  /// In en, this message translates to:
  /// **'Please enter valid pickup address'**
  String get pleaseEnterValidPickupAddress;

  /// No description provided for @stopLocation.
  ///
  /// In en, this message translates to:
  /// **'Stop Location'**
  String get stopLocation;

  /// No description provided for @pleaseAssignCarWithin.
  ///
  /// In en, this message translates to:
  /// **'This sloat assigned to you for {type}. If you have any remaining cars, Please assign all cars to a your preferred transporter within {type}.'**
  String pleaseAssignCarWithin(String type);

  /// No description provided for @noStockLocationFound.
  ///
  /// In en, this message translates to:
  /// **'No stock location found, Please choose another location'**
  String get noStockLocationFound;

  /// No description provided for @pleaseSelectInsurance.
  ///
  /// In en, this message translates to:
  /// **'Please select insurance for your vehicle'**
  String get pleaseSelectInsurance;

  /// No description provided for @thanksUForFeedback.
  ///
  /// In en, this message translates to:
  /// **'Thank you for your feedback'**
  String get thanksUForFeedback;

  /// No description provided for @noCheckListFound.
  ///
  /// In en, this message translates to:
  /// **'Your vehicle isn’t performed yet. After it gets performed, You’ll have to approve a checklist.'**
  String get noCheckListFound;

  /// No description provided for @bookingNoteCreatedSuccess.
  ///
  /// In en, this message translates to:
  /// **'Booking note created successfully'**
  String get bookingNoteCreatedSuccess;

  /// No description provided for @bookingNoteDeletedSuccess.
  ///
  /// In en, this message translates to:
  /// **'Booking note deleted successfully'**
  String get bookingNoteDeletedSuccess;

  /// No description provided for @transporterDoesNotHave.
  ///
  /// In en, this message translates to:
  /// **'Transporter does not have winch option.'**
  String get transporterDoesNotHave;

  /// No description provided for @transporterDoesNotMeet.
  ///
  /// In en, this message translates to:
  /// **'Transporter does not meet vehicle requirements.'**
  String get transporterDoesNotMeet;

  /// No description provided for @noAvailableSlot.
  ///
  /// In en, this message translates to:
  /// **'No available slots for remaining vehicles.'**
  String get noAvailableSlot;

  /// No description provided for @allVehicleAreAlready.
  ///
  /// In en, this message translates to:
  /// **'All vehicles are already assigned.'**
  String get allVehicleAreAlready;

  /// No description provided for @enableToUploadImages.
  ///
  /// In en, this message translates to:
  /// **'Enable to upload images, Please try again latter'**
  String get enableToUploadImages;

  /// No description provided for @pleaseAddBothPickupNDropInfo.
  ///
  /// In en, this message translates to:
  /// **'Please add both pickup and drop info'**
  String get pleaseAddBothPickupNDropInfo;

  /// No description provided for @description.
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// No description provided for @pleaseDescribeIssue.
  ///
  /// In en, this message translates to:
  /// **'Please describe your issue'**
  String get pleaseDescribeIssue;

  /// No description provided for @photosOptional.
  ///
  /// In en, this message translates to:
  /// **'Photos (Optional)'**
  String get photosOptional;

  /// No description provided for @submit.
  ///
  /// In en, this message translates to:
  /// **'Submit'**
  String get submit;

  /// No description provided for @yourComplainSubmittedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Your Complain Submitted Successfully'**
  String get yourComplainSubmittedSuccessfully;

  /// No description provided for @chooseAnAction.
  ///
  /// In en, this message translates to:
  /// **'Choose an Action'**
  String get chooseAnAction;

  /// No description provided for @camera.
  ///
  /// In en, this message translates to:
  /// **'Camera'**
  String get camera;

  /// No description provided for @gallery.
  ///
  /// In en, this message translates to:
  /// **'Gallery'**
  String get gallery;

  /// No description provided for @maxImagesError.
  ///
  /// In en, this message translates to:
  /// **'Maximum 7 images can be selected.'**
  String get maxImagesError;

  /// No description provided for @imagesNotUploaded.
  ///
  /// In en, this message translates to:
  /// **'Images are not uploaded'**
  String get imagesNotUploaded;

  /// No description provided for @pleaseEnterDescription.
  ///
  /// In en, this message translates to:
  /// **'Please enter your description'**
  String get pleaseEnterDescription;

  /// No description provided for @vehicleSize.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Size'**
  String get vehicleSize;

  /// No description provided for @vehicleDescription.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Description'**
  String get vehicleDescription;

  /// No description provided for @isWinchRequired.
  ///
  /// In en, this message translates to:
  /// **'Is winch required?'**
  String get isWinchRequired;

  /// No description provided for @pickupService.
  ///
  /// In en, this message translates to:
  /// **'Pickup Service'**
  String get pickupService;

  /// No description provided for @minimumCost.
  ///
  /// In en, this message translates to:
  /// **'Minimum Cost'**
  String get minimumCost;

  /// No description provided for @pickupAddress.
  ///
  /// In en, this message translates to:
  /// **'Pickup Address'**
  String get pickupAddress;

  /// No description provided for @charge.
  ///
  /// In en, this message translates to:
  /// **'Charge'**
  String get charge;

  /// No description provided for @vehicleCharges.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Charges'**
  String get vehicleCharges;

  /// No description provided for @openMap.
  ///
  /// In en, this message translates to:
  /// **'Open Map'**
  String get openMap;

  /// No description provided for @select.
  ///
  /// In en, this message translates to:
  /// **'Select'**
  String get select;

  /// No description provided for @winchSupport.
  ///
  /// In en, this message translates to:
  /// **'Winch Support'**
  String get winchSupport;

  /// No description provided for @exception.
  ///
  /// In en, this message translates to:
  /// **'Exception'**
  String get exception;

  /// No description provided for @exceptionsSupport.
  ///
  /// In en, this message translates to:
  /// **'Exceptions Support'**
  String get exceptionsSupport;

  /// No description provided for @writeUrMessageHere.
  ///
  /// In en, this message translates to:
  /// **'Write your message here'**
  String get writeUrMessageHere;

  /// No description provided for @originStockAdmin.
  ///
  /// In en, this message translates to:
  /// **'Origin stock admin'**
  String get originStockAdmin;

  /// No description provided for @dropStockAdmin.
  ///
  /// In en, this message translates to:
  /// **'Drop stock admin'**
  String get dropStockAdmin;

  /// No description provided for @readMore.
  ///
  /// In en, this message translates to:
  /// **'read more'**
  String get readMore;

  /// No description provided for @readLess.
  ///
  /// In en, this message translates to:
  /// **'read less'**
  String get readLess;

  /// No description provided for @customerDetail.
  ///
  /// In en, this message translates to:
  /// **'Customer Detail'**
  String get customerDetail;

  /// No description provided for @cancelledBookings.
  ///
  /// In en, this message translates to:
  /// **'Cancelled Bookings'**
  String get cancelledBookings;

  /// No description provided for @noCancelledBookingsFound.
  ///
  /// In en, this message translates to:
  /// **'No cancelled bookings found'**
  String get noCancelledBookingsFound;

  /// No description provided for @addCustomerDetail.
  ///
  /// In en, this message translates to:
  /// **'Add Customer Detail'**
  String get addCustomerDetail;

  /// No description provided for @mobileNumber.
  ///
  /// In en, this message translates to:
  /// **'Mobile Number'**
  String get mobileNumber;

  /// No description provided for @enterUrMobileNumber.
  ///
  /// In en, this message translates to:
  /// **'Enter your mobile number'**
  String get enterUrMobileNumber;

  /// No description provided for @pleaseEnterUrMobileNumber.
  ///
  /// In en, this message translates to:
  /// **'Please enter your mobile number'**
  String get pleaseEnterUrMobileNumber;

  /// No description provided for @pleaseEnterValidMobileNumber.
  ///
  /// In en, this message translates to:
  /// **'Please enter valid mobile number'**
  String get pleaseEnterValidMobileNumber;

  /// No description provided for @chatInactive.
  ///
  /// In en, this message translates to:
  /// **'Chat is inactive, You can not chat here anymore!'**
  String get chatInactive;

  /// No description provided for @noTripDatFound.
  ///
  /// In en, this message translates to:
  /// **'No any trip data available'**
  String get noTripDatFound;

  /// No description provided for @noMessageYet.
  ///
  /// In en, this message translates to:
  /// **'No messages yet'**
  String get noMessageYet;

  /// No description provided for @noteForMobile.
  ///
  /// In en, this message translates to:
  /// **'Note: The mobile number is only visible to those transporters and stock administrators who are associated with the trip in which you select the pickup vehicle from your location or winch option, or transporters associated with exclusive booking.'**
  String get noteForMobile;

  /// No description provided for @pleaseReview.
  ///
  /// In en, this message translates to:
  /// **'* Please review the checklist carefully. Once you\'ve verified all the items, Proceed by confirming the checklist. We won\'t be able to continue until the checklist is approved.'**
  String get pleaseReview;

  /// No description provided for @affectedTime.
  ///
  /// In en, this message translates to:
  /// **'Affected time'**
  String get affectedTime;

  /// No description provided for @confirmChecklist.
  ///
  /// In en, this message translates to:
  /// **'Confirm Checklist'**
  String get confirmChecklist;

  /// No description provided for @hour.
  ///
  /// In en, this message translates to:
  /// **'hour'**
  String get hour;

  /// No description provided for @addComments.
  ///
  /// In en, this message translates to:
  /// **'Add Comments'**
  String get addComments;

  /// No description provided for @comments.
  ///
  /// In en, this message translates to:
  /// **'Comments'**
  String get comments;

  /// No description provided for @pleaseWaitWeAreRedirectingStripe.
  ///
  /// In en, this message translates to:
  /// **'Please wait we are redirecting you to Stripe customer portal'**
  String get pleaseWaitWeAreRedirectingStripe;

  /// No description provided for @pleaseWaitWeAreRedirectingPayment.
  ///
  /// In en, this message translates to:
  /// **'Please wait we are redirecting you to Payment page'**
  String get pleaseWaitWeAreRedirectingPayment;

  /// No description provided for @customerDetailSavedSuccess.
  ///
  /// In en, this message translates to:
  /// **'Customer detail saved successfully'**
  String get customerDetailSavedSuccess;

  /// No description provided for @pleaseSearchOriginStopLocation.
  ///
  /// In en, this message translates to:
  /// **'Please first search origin stock location before you choose the stock location'**
  String get pleaseSearchOriginStopLocation;

  /// No description provided for @pleaseSearchDropStopLocation.
  ///
  /// In en, this message translates to:
  /// **'Please first search drop stock location before you choose the stock location'**
  String get pleaseSearchDropStopLocation;

  /// No description provided for @anyUndeclared.
  ///
  /// In en, this message translates to:
  /// **'Any undeclared additional items or modifications may result in the cancellation of the trip '**
  String get anyUndeclared;

  /// No description provided for @withoutRefund.
  ///
  /// In en, this message translates to:
  /// **'without refund.'**
  String get withoutRefund;

  /// No description provided for @itIsUserResponsibility.
  ///
  /// In en, this message translates to:
  /// **' It is the user`s responsibility to disclose all relevant details before the journey begins.'**
  String get itIsUserResponsibility;

  /// No description provided for @pleaseAcceptDeclaration.
  ///
  /// In en, this message translates to:
  /// **'Please accept above declaration'**
  String get pleaseAcceptDeclaration;

  /// No description provided for @removeAllVehicle.
  ///
  /// In en, this message translates to:
  /// **'- Remove all vehicle'**
  String get removeAllVehicle;

  /// No description provided for @chooseSlot.
  ///
  /// In en, this message translates to:
  /// **'Choose Slot'**
  String get chooseSlot;

  /// No description provided for @car.
  ///
  /// In en, this message translates to:
  /// **'Car'**
  String get car;

  /// No description provided for @totalCostSlot.
  ///
  /// In en, this message translates to:
  /// **'Total cost for'**
  String get totalCostSlot;

  /// No description provided for @slots.
  ///
  /// In en, this message translates to:
  /// **'Slots'**
  String get slots;

  /// No description provided for @bookingStatus.
  ///
  /// In en, this message translates to:
  /// **'Booking status'**
  String get bookingStatus;

  /// No description provided for @status.
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// No description provided for @myRating.
  ///
  /// In en, this message translates to:
  /// **'My Rating'**
  String get myRating;

  /// No description provided for @insuranceCharges.
  ///
  /// In en, this message translates to:
  /// **'Insurance charges varies upon your vehicle sizes'**
  String get insuranceCharges;

  /// No description provided for @attachPhotos.
  ///
  /// In en, this message translates to:
  /// **'Attach photos of your vehicle additional compartments or modifications allowing the team to review and validate that the vehicle can be transported without issues.'**
  String get attachPhotos;

  /// No description provided for @attachedPhotos.
  ///
  /// In en, this message translates to:
  /// **'Attached photos'**
  String get attachedPhotos;

  /// No description provided for @addAnyDiscrepancies.
  ///
  /// In en, this message translates to:
  /// **'Add any discrepancies or additional observations about your vehicle (e.g., Missing items, Damage, or anything not listed above).'**
  String get addAnyDiscrepancies;

  /// No description provided for @vehicleImages.
  ///
  /// In en, this message translates to:
  /// **'Vehicle images'**
  String get vehicleImages;

  /// No description provided for @invalidRoute.
  ///
  /// In en, this message translates to:
  /// **'Invalid route'**
  String get invalidRoute;

  /// No description provided for @bookingCancelledSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Booking cancelled Successfully'**
  String get bookingCancelledSuccessfully;

  /// No description provided for @tripCancelledSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Trip cancelled Successfully'**
  String get tripCancelledSuccessfully;

  /// No description provided for @changeInTravelPlans.
  ///
  /// In en, this message translates to:
  /// **'Change in travel plans'**
  String get changeInTravelPlans;

  /// No description provided for @bookingMadeAccidentally.
  ///
  /// In en, this message translates to:
  /// **'Booking made accidentally'**
  String get bookingMadeAccidentally;

  /// No description provided for @foundBetterAlternative.
  ///
  /// In en, this message translates to:
  /// **'Found a better alternative'**
  String get foundBetterAlternative;

  /// No description provided for @costTooHigh.
  ///
  /// In en, this message translates to:
  /// **'Cost was too high'**
  String get costTooHigh;

  /// No description provided for @pickupIssue.
  ///
  /// In en, this message translates to:
  /// **'Issues with pickup location or timing'**
  String get pickupIssue;

  /// No description provided for @needToEditBooking.
  ///
  /// In en, this message translates to:
  /// **'Changes needed in the booking'**
  String get needToEditBooking;

  /// No description provided for @anyOtherReason.
  ///
  /// In en, this message translates to:
  /// **'Any other reason'**
  String get anyOtherReason;

  /// No description provided for @reason.
  ///
  /// In en, this message translates to:
  /// **'Reason'**
  String get reason;

  /// No description provided for @pleaseSelectReason.
  ///
  /// In en, this message translates to:
  /// **'Please select reason'**
  String get pleaseSelectReason;

  /// No description provided for @paymentSettlementSummary.
  ///
  /// In en, this message translates to:
  /// **'Payment Settlement Summary'**
  String get paymentSettlementSummary;

  /// No description provided for @totalPaidAmount.
  ///
  /// In en, this message translates to:
  /// **'Total Paid Amount'**
  String get totalPaidAmount;

  /// No description provided for @totalRefundAmount.
  ///
  /// In en, this message translates to:
  /// **'Total Refund Amount'**
  String get totalRefundAmount;

  /// No description provided for @totalDueAmount.
  ///
  /// In en, this message translates to:
  /// **'Total Due Amount'**
  String get totalDueAmount;

  /// No description provided for @refundAmount.
  ///
  /// In en, this message translates to:
  /// **'Refund Amount'**
  String get refundAmount;

  /// No description provided for @dueAmount.
  ///
  /// In en, this message translates to:
  /// **'Due Amount'**
  String get dueAmount;

  /// No description provided for @refundCase.
  ///
  /// In en, this message translates to:
  /// **'Refund Case'**
  String get refundCase;

  /// No description provided for @bookingCancelled.
  ///
  /// In en, this message translates to:
  /// **'Booking Cancelled'**
  String get bookingCancelled;

  /// No description provided for @serviceBreakdown.
  ///
  /// In en, this message translates to:
  /// **'Service Breakdown'**
  String get serviceBreakdown;

  /// No description provided for @initialCharge.
  ///
  /// In en, this message translates to:
  /// **'Initial Charge'**
  String get initialCharge;

  /// No description provided for @deductedAmount.
  ///
  /// In en, this message translates to:
  /// **'Deducted Amount'**
  String get deductedAmount;

  /// No description provided for @remainingToCollect.
  ///
  /// In en, this message translates to:
  /// **'Remaining to Collect'**
  String get remainingToCollect;

  /// No description provided for @refundableAmount.
  ///
  /// In en, this message translates to:
  /// **'Refundable Amount'**
  String get refundableAmount;

  /// No description provided for @extraCharges.
  ///
  /// In en, this message translates to:
  /// **'Extra Charges'**
  String get extraCharges;

  /// No description provided for @dropOffStorage.
  ///
  /// In en, this message translates to:
  /// **'Drop-off Storage'**
  String get dropOffStorage;

  /// No description provided for @pickUpStorage.
  ///
  /// In en, this message translates to:
  /// **'Pick-up Storage'**
  String get pickUpStorage;

  /// No description provided for @proceedToSettlement.
  ///
  /// In en, this message translates to:
  /// **'Proceed to Settlement'**
  String get proceedToSettlement;

  /// No description provided for @allComplaints.
  ///
  /// In en, this message translates to:
  /// **'All Complaints'**
  String get allComplaints;

  /// No description provided for @noComplaints.
  ///
  /// In en, this message translates to:
  /// **'No Complaints'**
  String get noComplaints;

  /// No description provided for @complaints.
  ///
  /// In en, this message translates to:
  /// **'Complaints'**
  String get complaints;

  /// No description provided for @customerRefundRequest.
  ///
  /// In en, this message translates to:
  /// **'Customer Refund Request'**
  String get customerRefundRequest;

  /// No description provided for @refundId.
  ///
  /// In en, this message translates to:
  /// **'Refund id'**
  String get refundId;

  /// No description provided for @noRefundDataFound.
  ///
  /// In en, this message translates to:
  /// **'no refund data found'**
  String get noRefundDataFound;

  /// No description provided for @cancelled.
  ///
  /// In en, this message translates to:
  /// **'Cancelled'**
  String get cancelled;

  /// No description provided for @paymentSuccessful.
  ///
  /// In en, this message translates to:
  /// **'Payment Successful'**
  String get paymentSuccessful;

  /// No description provided for @paymentCompletedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Your payment has been completed successfully!'**
  String get paymentCompletedSuccessfully;

  /// No description provided for @paymentFailed.
  ///
  /// In en, this message translates to:
  /// **'Payment Failed'**
  String get paymentFailed;

  /// No description provided for @paymentFailedMessage.
  ///
  /// In en, this message translates to:
  /// **'Your payment could not be processed. Please try again or contact support.'**
  String get paymentFailedMessage;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'es'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'es':
      return AppLocalizationsEs();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
