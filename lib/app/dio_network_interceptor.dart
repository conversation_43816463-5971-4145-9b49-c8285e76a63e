import 'package:pkg_dio/pkg_dio.dart';
import 'package:transport_match/db/app_db.dart';
import 'package:transport_match/di/injector.dart';
import 'package:transport_match/extensions/ext_string_null.dart';
import 'package:transport_match/shared/rest_api/endpoints.dart';
import 'package:transport_match/shared/shared_api_calls/refresh_token_api.dart';
import 'package:transport_match/utils/logger.dart';

/// Custom Dio network interceptor for managing requests and responses globally
final class DioNetworkInterceptor extends Interceptor {
  /// constructor
  DioNetworkInterceptor({
    // required this.encryptor,
    // required this.deviceLocalUtils,
    required this.isSecure,
  });

  /// encrypt/decrypt utils class
  // final EncryptNetworkData encryptor;

  /// is secure network call or not (for token)
  final bool isSecure;

  /// AppDB
  late final appDB = Injector.instance<AppDB>();

  // late final langCubit = Injector.instance<AppLanguageCubit>();

  @override
  Future<void> onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    // options.headers['accept-language'] = appDB.userSettings?.language ?? 'en';
    // final connectivityResult = await Connectivity().checkConnectivity();
    // if (connectivityResult == ConnectivityResult.none) {
    //   return handler.reject(DioException(requestOptions: options, message: 'No internet connection!'));
    // }
    '//////// DioNetworkInterceptor onRequest: ${options.headers}'.logV;
    if (!appDB.isInternetConnected) {
      return handler.reject(
        DioException(
          requestOptions: options,
          // message: 'No internet connection! [${options.path}]',
          message: 'No internet connection!',
          type: DioExceptionType.connectionError,
          response: Response(statusCode: 412, requestOptions: options),
        ),
      );
    }
    // options.headers['device-country-code'] = await deviceLocalUtils.getDeviceLocalCode();
    final apiToken =
        (options.headers['Authorization'] as String?) ?? appDB.token;
    if (apiToken.isNotEmptyAndNotNull) {
      options.headers['Authorization'] = 'Bearer $apiToken';
    } else {
      // options.headers['Authorization'] = '';
    }
    options.headers['Accept'] = 'application/json';
    return handler.next(options);
  }

  // @override
  // Future<void> onResponse(Response<dynamic> response, ResponseInterceptorHandler handler) async {
  //
  //   return handler.next(response);
  // }

  @override
  Future<void> onError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
     
      "///// 🔥 DioInterceptor onError: ".logV;
      "///// status=${err.response?.statusCode}, ".logV;
      "///// data=${err.response?.data}, ".logV;
      "///// path=${err.requestOptions.path}".logV;

       
  
    if (err.response?.statusCode == 401 &&
        !err.requestOptions.uri.path.contains(EndPoints.refreshToken)) {
      var isSuccessful = false;
      await RefreshTokenApi.refreshToken(
        callback: () {
          isSuccessful = true;
        },
      );
      if (isSuccessful) {
        'Bearer ${appDB.token}'.logD;
        final res =
            await Injector.instance<Dio>(instanceName: 'open').request<dynamic>(
          err.requestOptions.path,
          options: Options(
            method: err.requestOptions.method,
            headers: {
              'Authorization': appDB.token,
            },
            contentType: err.requestOptions.contentType,
            responseType: err.requestOptions.responseType,
            validateStatus: err.requestOptions.validateStatus,
            receiveDataWhenStatusError:
                err.requestOptions.receiveDataWhenStatusError,
            followRedirects: err.requestOptions.followRedirects,
            maxRedirects: err.requestOptions.maxRedirects,
            receiveTimeout: err.requestOptions.receiveTimeout,
            sendTimeout: err.requestOptions.sendTimeout,
            extra: err.requestOptions.extra,
            listFormat: err.requestOptions.listFormat,
          ),
          data: err.requestOptions.data,
          queryParameters: err.requestOptions.queryParameters,
          onReceiveProgress: err.requestOptions.onReceiveProgress,
          onSendProgress: err.requestOptions.onSendProgress,
        );
        if (res.statusCode?.clamp(200, 299) == res.statusCode) {
          handler.resolve(res);
          return;
        } else {
          if (err.type != DioExceptionType.cancel) handler.reject(err);
          return;
        }
      }
    }
    if (err.response?.statusCode?.clamp(500, 599) == err.response?.statusCode) {
      // final message = err.response?.statusMessage;
      // if (message.isNotEmptyAndNotNull) Alert.instance.showMessage(message!);
      err.response?.data = <String, dynamic>{};
      err.response?.statusMessage =
          'Oops, something went wrong. Please try again';
      // err.response?.statusMessage = 'Oops, something went wrong. Please try again [${err.requestOptions.path}]';
    }
    if (err.type != DioExceptionType.cancel) handler.next(err);
  }
}
