import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:path/path.dart';
import 'package:transport_match/style/custom_theme.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/app_dropdown.dart';

/// extension for [BuildContext]
extension BuildContextEx on BuildContext {
  /// to get theme
  ThemeData get theme => CustomTheme().light;

  /// to get colorScheme
  ColorScheme get colorScheme => CustomTheme().light.colorScheme;

  /// to get text theme
  TextTheme get textTheme => CustomTheme().light.textTheme;

  // ThemeColors get themeColors => Theme.of(this).extension<ThemeColors>()!;

  /// to get width from media query
  double get width => MediaQuery.sizeOf(this).width;

  /// to get width in pixels
  double get widthPixel =>
      MediaQuery.sizeOf(this).width * MediaQuery.of(this).devicePixelRatio;

  /// to get height from media query
  double get height => MediaQuery.sizeOf(this).height;

  /// to get height in pixels
  double get heightPixel =>
      MediaQuery.sizeOf(this).height * MediaQuery.of(this).devicePixelRatio;

  /// to theme brightness [Brightness.dark] or [Brightness.light]
  bool get isDarkMode => Theme.of(this).brightness == Brightness.dark;

  // double get heightAspectRatio {
  //   return height / designSize.height;
  // }

  // double get widthAspectRatio {
  //   return width / designSize.width;
  // }

  /// to show common dialog
  Future<bool?> showAlertDialog({
    required String defaultActionText,
    required String cancelActionText,
    String? title,
    String? content,
    void Function(BuildContext dialogContext)? onDefaultActionPressed,
    void Function(BuildContext dialogContext)? onCancelActionPressed,
    Widget? titleWidget,
    Widget? contentWidget,
    Color defaultActionBgColor = AppColors.primaryColor,
    Color cancelActionBgColor = AppColors.white,
    ValueNotifier<bool>? isLoadingDefaultAction,
  }) async => showDialog<bool>(
    context: this,
    builder: (dContext) => AlertDialog(
      surfaceTintColor: Colors.white,
      insetPadding: EdgeInsets.all(AppSize.h24),
      title:
          titleWidget ??
          Text(
            title ?? '',
            textAlign: TextAlign.center,
            style: dContext.textTheme.titleSmall?.copyWith(
              fontSize: AppSize.sp16,
            ),
          ),
      content:
          contentWidget ?? Text(content ?? '', textAlign: TextAlign.center),
      actions: [
        Row(
          children: [
            Expanded(
              child: AppButton(
                onPressed: () => onCancelActionPressed?.call(dContext),
                isBottomBtn: false,
                text: cancelActionText,
                visualDensity: VisualDensity.compact,
                isFillButton: false,
                buttonColor: cancelActionBgColor,
              ),
            ),
            Gap(AppSize.w16),
            Expanded(
              child: isLoadingDefaultAction != null
                  ? ValueListenableBuilder<bool>(
                      valueListenable: isLoadingDefaultAction,
                      builder: (context, isLoading, child) {
                        return AppButton(
                          onPressed: () {
                            onDefaultActionPressed?.call(dContext);
                          },
                          isBottomBtn: false,
                          text: defaultActionText,
                          visualDensity: VisualDensity.compact,
                          isLoading: isLoading,
                          backgroundColor: defaultActionBgColor,
                        );
                      },
                    )
                  : AppButton(
                      onPressed: () {
                        onDefaultActionPressed?.call(dContext);
                      },
                      text: defaultActionText,
                      isBottomBtn: false,
                      visualDensity: VisualDensity.compact,
                      backgroundColor: defaultActionBgColor,
                    ),
            ),
          ],
        ),
      ],
    ),
  );

  /// to show common dialog
  Future<bool?> showCancelDialogWithReasons({
    required String defaultActionText,
    required String cancelActionText,
    required SingleValueDropDownController controller,
    String? title,
    String? content,
    void Function(BuildContext dialogContext)? onDefaultActionPressed,
    void Function(BuildContext dialogContext)? onCancelActionPressed,
    required List<Map<String, String>>
    reasons, // e.g. [{"key": "BOOKED_BY_MISTAKE", "label": "Booking made accidentally"}]
    Widget? titleWidget,
    Color defaultActionBgColor = AppColors.primaryColor,
    Color cancelActionBgColor = AppColors.white,
    ValueNotifier<bool>? isLoadingDefaultAction,
    String dropDownHintText = '',
  }) async => showDialog<bool>(
    context: this,
    builder: (dContext) => AlertDialog(
      surfaceTintColor: Colors.white,
      // insetPadding: EdgeInsets.all(AppSize.h24),
      title:
          titleWidget ??
          Text(
            title ?? '',
            textAlign: TextAlign.center,
            style: dContext.textTheme.titleSmall?.copyWith(
              fontSize: AppSize.sp16,
            ),
          ),
      content: SizedBox(
        width: dContext.width * 0.85,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              content ?? '',
              textAlign: TextAlign.center,
              style: dContext.textTheme.bodyMedium?.copyWith(
                fontSize: AppSize.sp15,
              ),
            ),
            Gap(AppSize.h16),
            AppDropdown(
              items: reasons
                  .map(
                    (e) => DropDownValueModel(
                      name: e['label'] ?? '',
                      value: e['key'],
                    ),
                  )
                  .toList(),
              controller: controller,
              hintText: dropDownHintText,
            ),
          ],
        ),
      ),
      actions: [
        Row(
          children: [
            Expanded(
              child: AppButton(
                onPressed: () => onCancelActionPressed?.call(dContext),
                isBottomBtn: false,
                text: cancelActionText,
                visualDensity: VisualDensity.compact,
                isFillButton: false,
                buttonColor: cancelActionBgColor,
              ),
            ),
            Gap(AppSize.w16),
            Expanded(
              child: isLoadingDefaultAction != null
                  ? ValueListenableBuilder<bool>(
                      valueListenable: isLoadingDefaultAction,
                      builder: (context, isLoading, child) {
                        return AppButton(
                          onPressed: () {
                            onDefaultActionPressed?.call(dContext);
                          },
                          isBottomBtn: false,
                          text: defaultActionText,
                          visualDensity: VisualDensity.compact,
                          isLoading: isLoading,
                          backgroundColor: defaultActionBgColor,
                        );
                      },
                    )
                  : AppButton(
                      onPressed: () {
                        onDefaultActionPressed?.call(dContext);
                      },
                      text: defaultActionText,
                      isBottomBtn: false,
                      visualDensity: VisualDensity.compact,
                      backgroundColor: defaultActionBgColor,
                    ),
            ),
          ],
        ),
      ],
    ),
  );
}
