import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/payment_page/widgets/payment_summary_widget.dart';

void main() {
  group('Payment Summary Widget Tests', () {
    testWidgets('SummaryRow displays title and amount correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SummaryRow(
              title: 'Transportation Cost',
              amount: '\$100.00',
            ),
          ),
        ),
      );

      expect(find.text('Transportation Cost'), findsOneWidget);
      expect(find.text('\$100.00'), findsOneWidget);
    });

    testWidgets('SummaryRow with empty amount is not displayed', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SummaryRow(
              title: 'Transportation Cost',
              amount: '',
            ),
          ),
        ),
      );

      expect(find.text('Transportation Cost'), findsNothing);
      expect(find.text(''), findsNothing);
    });

    testWidgets('SummaryRow with isSubItem shows indented layout', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SummaryRow(
              title: 'Net Amount',
              amount: '\$80.00',
              isSubItem: true,
            ),
          ),
        ),
      );

      expect(find.text('Net Amount'), findsOneWidget);
      expect(find.text('\$80.00'), findsOneWidget);
      
      // Check if the widget has left padding for indentation
      final paddingWidget = tester.widget<Padding>(find.byType(Padding));
      expect(paddingWidget.padding, isA<EdgeInsets>());
    });

    testWidgets('DetailedPaymentBreakdown shows gross and net amounts', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DetailedPaymentBreakdown(
              title: 'Transportation Cost',
              grossAmount: '\$100.00',
              netAmount: '\$80.00',
            ),
          ),
        ),
      );

      expect(find.text('Transportation Cost'), findsOneWidget);
      expect(find.text('• Gross Amount'), findsOneWidget);
      expect(find.text('\$100.00'), findsOneWidget);
      expect(find.text('• Net Amount (You Pay)'), findsOneWidget);
      expect(find.text('\$80.00'), findsOneWidget);
    });

    testWidgets('DetailedPaymentBreakdown hides gross amount when showGrossAmount is false', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DetailedPaymentBreakdown(
              title: 'Transportation Cost',
              grossAmount: '\$100.00',
              netAmount: '\$80.00',
              showGrossAmount: false,
            ),
          ),
        ),
      );

      expect(find.text('Transportation Cost'), findsOneWidget);
      expect(find.text('• Gross Amount'), findsNothing);
      expect(find.text('\$100.00'), findsNothing);
      expect(find.text('• Net Amount (You Pay)'), findsOneWidget);
      expect(find.text('\$80.00'), findsOneWidget);
    });

    testWidgets('DetailedPaymentBreakdown handles empty amounts', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DetailedPaymentBreakdown(
              title: 'Transportation Cost',
              grossAmount: '',
              netAmount: '',
            ),
          ),
        ),
      );

      expect(find.text('Transportation Cost'), findsOneWidget);
      expect(find.text('• Gross Amount'), findsNothing);
      expect(find.text('• Net Amount (You Pay)'), findsNothing);
    });
  });
}
