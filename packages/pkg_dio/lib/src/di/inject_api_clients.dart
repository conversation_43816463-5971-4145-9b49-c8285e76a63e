import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart' show immutable, kDebugMode;
import 'package:get_it/get_it.dart';
import 'package:native_dio_adapter/native_dio_adapter.dart';
import 'package:pkg_dio/src/dio_manager/logger.dart';

/// Api client injector
@immutable
class ApiClientsInjector {
  /// Constructor
  ApiClientsInjector({
    required this.injector,
    required this.baseUrl,
    required this.isSecure,
    this.interceptors,
  }) {
    _init();
  }

  /// instance of injector
  final GetIt injector;

  /// Base Url
  final String baseUrl;

  /// Custom network interceptor
  final List<Interceptor>? interceptors;

  /// for secure header with token
  final bool isSecure;

  late final _baseOptions = BaseOptions(
    baseUrl: baseUrl,
    contentType: Headers.jsonContentType,
    connectTimeout: const Duration(seconds: 60),
    sendTimeout: const Duration(seconds: 60),
    receiveTimeout: const Duration(seconds: 60),
  );

  final _logger = LogInterceptor(requestBody: true, responseBody: true);

  late final _dioClient = Dio(_baseOptions);

  void _init() {
    /* _dioClient.httpClientAdapter = Http2Adapter(
      ConnectionManager(
        idleTimeout: const Duration(minutes: 3),

        /// Ignore bad certificate
        onClientCreate: (_, config) => config.onBadCertificate = (_) => true,
      ),
    );*/
    if (isSecure) {
      injector.registerLazySingleton<Dio>(
        () {
          if (kDebugMode) interceptors?.add(_logger);
          if (interceptors != null) _dioClient.interceptors.addAll(interceptors!);
          /*_dioClient.interceptors
              .fold(
                '',
                (previousValue, element) => '$previousValue - $element',
              )
              .logI;*/
          final nativeAdapter = NativeAdapter(
              // createCupertinoConfiguration: URLSessionConfiguration.ephemeralSessionConfiguration,
              );
          _dioClient.httpClientAdapter = nativeAdapter;
          return _dioClient;
        },
      );
    } else {
      injector.registerLazySingleton<Dio>(
        () {
          if (kDebugMode) interceptors?.add(_logger);
          if (interceptors != null) _dioClient.interceptors.addAll(interceptors!);
          _dioClient.interceptors
              .fold(
                '',
                (previousValue, element) => '$previousValue - $element',
              )
              .logI;
          final nativeAdapter = NativeAdapter(
              // createCupertinoConfiguration: URLSessionConfiguration.ephemeralSessionConfiguration,
              );
          _dioClient.httpClientAdapter = nativeAdapter;
          return _dioClient;
        },
        instanceName: 'open',
      );
    }
  }
}
